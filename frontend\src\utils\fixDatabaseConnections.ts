import { DatabaseConnectionManager } from './databaseApi'

/**
 * 修复数据库连接配置
 * 清理旧的连接并重新初始化默认连接
 */
export function fixDatabaseConnections() {
  try {
    // 清理localStorage中的旧连接
    localStorage.removeItem('database-connections')
    
    // 清理内存中的连接
    const connections = DatabaseConnectionManager.getAllConnections()
    connections.forEach(conn => {
      if (conn.id) {
        DatabaseConnectionManager.removeConnection(conn.id)
      }
    })
    
    // 重新初始化默认连接
    DatabaseConnectionManager.initializeDefaults()
    
    console.log('数据库连接配置已修复')
    return true
  } catch (error) {
    console.error('修复数据库连接配置失败:', error)
    return false
  }
}

// 在开发环境中自动执行修复
if (import.meta.env.DEV) {
  fixDatabaseConnections()
}
