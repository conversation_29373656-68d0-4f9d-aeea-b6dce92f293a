{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.43071454763412476, "root": {"boundingVolume": {"box": [-70.238525390625, 115.88259887695312, -53.468482971191406, 16.23541259765625, 0.0, 0.0, 0.0, 22.62075424194336, 0.0, 0.0, 0.0, 17.74859046936035]}, "children": [{"boundingVolume": {"box": [-70.238525390625, 106.45729064941406, -53.02667236328125, 16.23541259765625, 0.0, 0.0, 0.0, 13.195438385009766, 0.0, 0.0, 0.0, 17.306779861450195]}, "children": [{"boundingVolume": {"box": [-70.238525390625, 106.45729064941406, -61.97886657714844, 16.23541259765625, 0.0, 0.0, 0.0, 13.195438385009766, 0.0, 0.0, 0.0, 6.945272445678711]}, "content": {"uri": "Block_L19_121.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.238525390625, 106.45729064941406, -45.37674331665039, 16.23541259765625, 0.0, 0.0, 0.0, 13.195438385009766, 0.0, 0.0, 0.0, 9.656852722167969]}, "children": [{"boundingVolume": {"box": [-70.238525390625, 106.45729064941406, -45.37674331665039, 16.23541259765625, 0.0, 0.0, 0.0, 13.195438385009766, 0.0, 0.0, 0.0, 9.656852722167969]}, "children": [{"boundingVolume": {"box": [-83.76803588867188, 101.29399108886719, -45.37674331665039, 2.705902099609375, 0.0, 0.0, 0.0, 8.03213882446289, 0.0, 0.0, 0.0, 9.656852722167969]}, "children": [{"boundingVolume": {"box": [-83.76803588867188, 95.93922424316406, -50.205684661865234, 2.705902099609375, 0.0, 0.0, 0.0, 2.677379608154297, 0.0, 0.0, 0.0, 4.827911376953125]}, "content": {"uri": "Block_L22_646.b3dm"}, "geometricError": 0.01221818383783102, "refine": "REPLACE"}, {"boundingVolume": {"box": [-85.34608459472656, 103.97136688232422, -50.37654113769531, 1.1278572082519531, 0.0, 0.0, 0.0, 5.354759216308594, 0.0, 0.0, 0.0, 4.015155792236328]}, "content": {"uri": "Block_L22_645.b3dm"}, "geometricError": 0.013087130151689053, "refine": "REPLACE"}, {"boundingVolume": {"box": [-83.86294555664062, 95.50910949707031, -40.549861907958984, 2.6109886169433594, 0.0, 0.0, 0.0, 2.2472572326660156, 0.0, 0.0, 0.0, 4.827911376953125]}, "content": {"uri": "Block_L23_624.b3dm"}, "geometricError": 0.009880327619612217, "refine": "REPLACE"}], "content": {"uri": "Block_L21_425.b3dm"}, "geometricError": 0.02244631201028824, "refine": "REPLACE"}, {"boundingVolume": {"box": [-67.53262329101562, 106.45729064941406, -51.45562744140625, 13.529510498046875, 0.0, 0.0, 0.0, 13.195438385009766, 0.0, 0.0, 0.0, 3.5779666900634766]}, "children": [{"boundingVolume": {"box": [-69.78754425048828, 104.04979705810547, -51.45746612548828, 11.274591445922852, 0.0, 0.0, 0.0, 10.787948608398438, 0.0, 0.0, 0.0, 3.576129913330078]}, "content": {"uri": "Block_L22_644.b3dm"}, "geometricError": 0.012734374962747097, "refine": "REPLACE"}, {"boundingVolume": {"box": [-56.17070770263672, 108.30609130859375, -51.77297592163086, 2.167593002319336, 0.0, 0.0, 0.0, 3.782207489013672, 0.0, 0.0, 0.0, 3.2606201171875]}, "content": {"uri": "Block_L22_643.b3dm"}, "geometricError": 0.011785962618887424, "refine": "REPLACE"}, {"boundingVolume": {"box": [-56.258033752441406, 115.87051391601562, -52.10821533203125, 2.2549190521240234, 0.0, 0.0, 0.0, 3.7822113037109375, 0.0, 0.0, 0.0, 2.9253807067871094]}, "content": {"uri": "Block_L22_642.b3dm"}, "geometricError": 0.011532183736562729, "refine": "REPLACE"}], "content": {"uri": "Block_L21_424.b3dm"}, "geometricError": 0.023525994271039963, "refine": "REPLACE"}], "content": {"uri": "Block_L20_232.b3dm"}, "geometricError": 0.045824527740478516, "refine": "REPLACE"}], "content": {"uri": "Block_L19_120.b3dm"}, "geometricError": 0.09165968000888824, "refine": "REPLACE"}], "content": {"uri": "Block_L18_62.b3dm"}, "geometricError": 0.21506017446517944, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.238525390625, 129.07803344726562, -60.71631622314453, 16.23541259765625, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 10.401605606079102]}, "children": [{"boundingVolume": {"box": [-77.00328063964844, 129.07803344726562, -60.1041259765625, 9.470657348632812, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 9.789419174194336]}, "content": {"uri": "Block_L19_119.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.76786804199219, 129.07803344726562, -64.40916442871094, 6.7647552490234375, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 6.561893463134766]}, "children": [{"boundingVolume": {"box": [-60.76786804199219, 129.07803344726562, -64.40731811523438, 6.7647552490234375, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 6.5600433349609375]}, "children": [{"boundingVolume": {"box": [-60.76786804199219, 124.3653793334961, -64.39996337890625, 6.7647552490234375, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 6.552684783935547]}, "children": [{"boundingVolume": {"box": [-64.1502456665039, 124.3653793334961, -63.78171920776367, 3.3823776245117188, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 5.934444427490234]}, "content": {"uri": "Block_L22_631.b3dm"}, "geometricError": 0.014642011374235153, "refine": "REPLACE"}, {"boundingVolume": {"box": [-57.38549041748047, 124.3653793334961, -64.39801025390625, 3.3823776245117188, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 6.5507354736328125]}, "content": {"uri": "Block_L22_630.b3dm"}, "geometricError": 0.015272836200892925, "refine": "REPLACE"}], "content": {"uri": "Block_L21_417.b3dm"}, "geometricError": 0.029914719983935356, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.76786804199219, 133.7906951904297, -64.40731811523438, 6.7647552490234375, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 6.5600433349609375]}, "children": [{"boundingVolume": {"box": [-64.71397399902344, 133.7906951904297, -64.10914611816406, 2.8186473846435547, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 6.261867523193359]}, "content": {"uri": "Block_L22_629.b3dm"}, "geometricError": 0.014516249299049377, "refine": "REPLACE"}, {"boundingVolume": {"box": [-57.94921875, 133.7906951904297, -64.40731811523438, 3.946107864379883, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 6.5600433349609375]}, "content": {"uri": "Block_L22_628.b3dm"}, "geometricError": 0.01525308471173048, "refine": "REPLACE"}], "content": {"uri": "Block_L21_416.b3dm"}, "geometricError": 0.029427887871861458, "refine": "REPLACE"}], "content": {"uri": "Block_L20_228.b3dm"}, "geometricError": 0.05931559577584267, "refine": "REPLACE"}], "content": {"uri": "Block_L19_118.b3dm"}, "geometricError": 0.11863359808921814, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.76786804199219, 129.07803344726562, -54.56712341308594, 6.7647552490234375, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 3.2801513671875]}, "content": {"uri": "Block_L19_117.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_61.b3dm"}, "geometricError": 0.2139832228422165, "refine": "REPLACE"}], "content": {"uri": "Block_L17_29.b3dm"}, "geometricError": 0.43071454763412476, "refine": "REPLACE"}}