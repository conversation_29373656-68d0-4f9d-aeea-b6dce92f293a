<template>
  <div class="map-selection-mode">
    <!-- 选择工具栏 -->
    <div class="selection-toolbar">
      <div class="toolbar-title">
        <el-icon><Pointer /></el-icon>
        <span>地图要素选择</span>
      </div>
      
      <div class="tool-buttons">
        <el-tooltip content="单击选择要素" placement="top">
          <el-button 
            :type="selectionActive ? 'primary' : 'default'"
            size="small"
            @click="toggleSelection"
            :loading="isActivating"
          >
            <el-icon><Pointer /></el-icon>
            {{ selectionActive ? '选择中' : '开始选择' }}
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="清除所有选择" placement="top">
          <el-button 
            size="small"
            @click="clearSelection"
            :disabled="selectedCount === 0"
          >
            <el-icon><Delete /></el-icon>
            清除
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 选择状态显示 -->
    <div class="selection-status">
      <div class="status-info">
        <el-tag 
          :type="selectedCount > 0 ? 'success' : 'info'" 
          size="small"
        >
          已选择: {{ selectedCount }} 个要素
        </el-tag>
        
        <div v-if="selectedCount > 0" class="feature-summary">
          {{ featureSummary }}
        </div>
      </div>
      
      <!-- 要素信息预览 -->
      <div v-if="selectedCount > 0" class="feature-preview">
        <el-collapse v-model="previewCollapsed" size="small">
          <el-collapse-item title="要素详情" name="details">
            <div class="feature-details">
              <div v-for="(count, type) in featuresByType" :key="type" class="type-item">
                <el-tag size="small" effect="plain">
                  {{ getFeatureTypeName(type) }}: {{ count }}个
                </el-tag>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="selection-tips">
      <el-alert
        v-if="selectionActive"
        title="选择提示"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="tips-content">
            <p>• <strong>单击要素</strong>：选择单个要素（清除之前的选择）</p>
            <p>• <strong>拖拽画框</strong>：框选多个要素</p>
            <p>• <strong>Ctrl+点击</strong>：切换要素选择状态（已选择的取消，未选择的添加）</p>
            <p>• <strong>Shift+点击</strong>：追加选择要素（只添加，不取消）</p>
            <p>• <strong>点击空白处</strong>：清除所有选择（按住Ctrl键可保持选择）</p>
          </div>
        </template>
      </el-alert>
      
      <el-alert
        v-else-if="selectedCount === 0"
        title="请点击'开始选择'按钮激活选择工具"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 确认选择按钮 -->
    <div class="selection-actions">
      <el-button
        type="primary"
        @click="openConfirmDialog"
        :disabled="selectedCount === 0"
        size="small"
      >
        <el-icon><Check /></el-icon>
        确认选择 ({{ selectedCount }})
      </el-button>

      <el-button
        @click="cancelSelection"
        size="small"
      >
        <el-icon><Close /></el-icon>
        取消
      </el-button>
    </div>

    <!-- 要素确认对话框 -->
    <el-dialog
      v-model="confirmDialogVisible"
      title="确认要进行缓冲区分析的要素"
      width="600px"
      :before-close="handleConfirmDialogClose"
    >
      <div class="feature-confirm-content">
        <el-alert
          title="请选择需要进行缓冲区分析的要素"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 16px;"
        />

        <!-- 按图层分组显示要素 -->
        <div v-for="(layerGroup, layerName) in featuresByLayer" :key="layerName" class="layer-group">
          <div class="layer-header">
            <el-checkbox
              v-model="layerGroup.allSelected"
              :indeterminate="layerGroup.indeterminate"
              @change="handleLayerSelectAll(layerName, $event)"
            >
              <strong>{{ layerName }}</strong> ({{ layerGroup.features.length }}个要素)
            </el-checkbox>
          </div>

          <div class="feature-list">
            <el-checkbox-group v-model="layerGroup.selectedIds">
              <div v-for="feature in layerGroup.features" :key="feature.id" class="feature-item">
                <el-checkbox :label="feature.id">
                  <div class="feature-info">
                    <el-tag size="small" :type="getFeatureTagType(feature.type)">
                      {{ getFeatureTypeName(feature.type) }}
                    </el-tag>
                    <span class="feature-desc">
                      {{ getFeatureDescription(feature) }}
                    </span>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="selection-summary">
          <el-tag type="success" size="large">
            已选择 {{ confirmedFeatureCount }} / {{ selectedCount }} 个要素
          </el-tag>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleConfirmDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="confirmFinalSelection"
            :disabled="confirmedFeatureCount === 0"
          >
            确认进行缓冲区分析 ({{ confirmedFeatureCount }}个要素)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Pointer, Delete, Check, Close 
} from '@element-plus/icons-vue'
import type { GeoFeature, FeatureType } from '../../../../utils/geo/core/types'
import { FeatureConverter } from '../../../../utils/geo/core/featureConverter'
import { geoEventBus, GEO_EVENTS } from '../../../../utils/geo/core/eventBus'
import { useMapStore } from '../../../../stores/map'

// 定义事件
const emit = defineEmits<{
  'selection-confirmed': [features: GeoFeature[]]
  'selection-cancelled': []
}>()

// 响应式数据
const selectionActive = ref(false)
const isActivating = ref(false)
const selectedFeatures = ref<GeoFeature[]>([])
const previewCollapsed = ref<string[]>([])
const confirmDialogVisible = ref(false)
const mapStore = useMapStore()

// 确认对话框相关数据
interface LayerGroup {
  features: GeoFeature[]
  selectedIds: string[]
  allSelected: boolean
  indeterminate: boolean
}

const featuresByLayer = ref<Record<string, LayerGroup>>({})

// 计算属性
const selectedCount = computed(() => selectedFeatures.value.length)

const featureSummary = computed(() => {
  return FeatureConverter.createFeatureSummary(selectedFeatures.value)
})

const featuresByType = computed(() => {
  const stats = FeatureConverter.getFeatureStatistics(selectedFeatures.value)
  return stats.byType
})

// 计算确认的要素数量
const confirmedFeatureCount = computed(() => {
  return Object.values(featuresByLayer.value).reduce((total, layerGroup) => {
    return total + layerGroup.selectedIds.length
  }, 0)
})

// 方法
const toggleSelection = async () => {
  if (selectionActive.value) {
    await deactivateSelection()
  } else {
    await activateSelection()
  }
}

const activateSelection = async () => {
  try {
    isActivating.value = true
    
    // 获取GeoManager实例
    const geoManager = mapStore.geoManager
    if (!geoManager) {
      throw new Error('GeoManager未初始化')
    }
    
    // 激活选择工具
    geoManager.activateSelection('multiple')
    selectionActive.value = true
    
    ElMessage.success('选择工具已激活')
    console.log('🎯 地图选择模式已激活')
  } catch (error) {
    console.error('激活选择工具失败:', error)
    ElMessage.error('激活选择工具失败')
  } finally {
    isActivating.value = false
  }
}

const deactivateSelection = async () => {
  try {
    const geoManager = mapStore.geoManager
    if (geoManager) {
      geoManager.deactivateSelection(false) // 不清除选择
    }
    
    selectionActive.value = false
    ElMessage.info('选择工具已停用')
    console.log('🎯 地图选择模式已停用')
  } catch (error) {
    console.error('停用选择工具失败:', error)
  }
}

const clearSelection = () => {
  try {
    const geoManager = mapStore.geoManager
    if (geoManager) {
      geoManager.clearSelection()
    }
    
    selectedFeatures.value = []
    ElMessage.success('已清除所有选择')
    console.log('🧹 已清除选择')
  } catch (error) {
    console.error('清除选择失败:', error)
  }
}

// 打开确认对话框
const openConfirmDialog = () => {
  if (selectedFeatures.value.length === 0) {
    ElMessage.warning('请先选择要素')
    return
  }

  // 按图层分组要素
  groupFeaturesByLayer()
  confirmDialogVisible.value = true
}

// 按图层分组要素
const groupFeaturesByLayer = () => {
  const groups: Record<string, LayerGroup> = {}

  selectedFeatures.value.forEach(feature => {
    const layerName = feature.layerName || '未知图层'

    if (!groups[layerName]) {
      groups[layerName] = {
        features: [],
        selectedIds: [],
        allSelected: false,
        indeterminate: false
      }
    }

    groups[layerName].features.push(feature)
    groups[layerName].selectedIds.push(feature.id) // 默认全选
  })

  // 更新选择状态
  Object.values(groups).forEach(group => {
    updateLayerSelectionState(group)
  })

  featuresByLayer.value = groups
}

// 更新图层选择状态
const updateLayerSelectionState = (layerGroup: LayerGroup) => {
  const selectedCount = layerGroup.selectedIds.length
  const totalCount = layerGroup.features.length

  layerGroup.allSelected = selectedCount === totalCount
  layerGroup.indeterminate = selectedCount > 0 && selectedCount < totalCount
}

// 处理图层全选/取消全选
const handleLayerSelectAll = (layerName: string, checked: boolean) => {
  const layerGroup = featuresByLayer.value[layerName]
  if (!layerGroup) return

  if (checked) {
    layerGroup.selectedIds = layerGroup.features.map(f => f.id)
  } else {
    layerGroup.selectedIds = []
  }

  updateLayerSelectionState(layerGroup)
}

// 确认最终选择
const confirmFinalSelection = () => {
  const confirmedFeatures: GeoFeature[] = []

  Object.values(featuresByLayer.value).forEach(layerGroup => {
    layerGroup.features.forEach(feature => {
      if (layerGroup.selectedIds.includes(feature.id)) {
        confirmedFeatures.push(feature)
      }
    })
  })

  if (confirmedFeatures.length === 0) {
    ElMessage.warning('请至少选择一个要素')
    return
  }

  confirmDialogVisible.value = false
  emit('selection-confirmed', confirmedFeatures)
  ElMessage.success(`已确认选择 ${confirmedFeatures.length} 个要素进行缓冲区分析`)
}

// 处理确认对话框关闭
const handleConfirmDialogClose = () => {
  confirmDialogVisible.value = false
}

const cancelSelection = () => {
  clearSelection()
  if (selectionActive.value) {
    deactivateSelection()
  }
  emit('selection-cancelled')
}

const getFeatureTypeName = (type: FeatureType): string => {
  const typeNames: Record<FeatureType, string> = {
    point: '点',
    line: '线',
    polygon: '面',
    circle: '圆',
    rectangle: '矩形'
  }
  return typeNames[type] || type
}

// 获取要素标签类型
const getFeatureTagType = (type: FeatureType): string => {
  const tagTypes: Record<FeatureType, string> = {
    point: 'success',
    line: 'warning',
    polygon: 'info',
    circle: 'info',
    rectangle: 'info'
  }
  return tagTypes[type] || 'info'
}

// 获取要素描述
const getFeatureDescription = (feature: GeoFeature): string => {
  const props = feature.properties || {}

  // 尝试获取有意义的属性作为描述
  const nameFields = ['name', 'Name', 'NAME', 'title', 'Title', 'label', 'Label']
  for (const field of nameFields) {
    if (props[field]) {
      return String(props[field])
    }
  }

  // 如果没有名称字段，显示ID或坐标信息
  if (feature.id) {
    return `ID: ${feature.id}`
  }

  // 根据几何类型显示不同信息
  switch (feature.type) {
    case 'point':
      return '点要素'
    case 'line':
      return '线要素'
    case 'polygon':
      return '面要素'
    default:
      return '未知要素'
  }
}

// 事件监听
const handleSelectionChanged = (features: GeoFeature[]) => {
  selectedFeatures.value = [...features]
  console.log('🔄 选择状态更新:', features.length)
}

// 生命周期
onMounted(() => {
  // 监听选择变化事件
  geoEventBus.on(GEO_EVENTS.SELECTION_CHANGED, handleSelectionChanged)
  console.log('🎯 MapSelectionMode组件已挂载')
})

onBeforeUnmount(() => {
  // 清理事件监听
  geoEventBus.off(GEO_EVENTS.SELECTION_CHANGED, handleSelectionChanged)
  
  // 停用选择工具
  if (selectionActive.value) {
    deactivateSelection()
  }
  
  console.log('🎯 MapSelectionMode组件已卸载')
})
</script>

<style scoped>
.map-selection-mode {
  padding: 1rem;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
  font-size: 12px; /* 缩小2号字体 */
}

.selection-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #333;
}

.tool-buttons {
  display: flex;
  gap: 0.5rem;
}

.selection-status {
  margin-bottom: 1rem;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.feature-summary {
  font-size: 11px; /* 缩小2号字体 */
  color: #666;
}

.feature-preview {
  margin-top: 0.5rem;
}

.feature-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.type-item {
  margin-bottom: 0.25rem;
}

.selection-tips {
  margin-bottom: 1rem;
}

.tips-content p {
  margin: 0.25rem 0;
  font-size: 11px; /* 缩小2号字体 */
}

.selection-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* 确认对话框样式 */
.feature-confirm-content {
  max-height: 500px;
  overflow-y: auto;
}

.layer-group {
  margin-bottom: 1.5rem;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.layer-header {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.feature-list {
  padding: 8px 16px;
}

.feature-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.feature-item:last-child {
  border-bottom: none;
}

.feature-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-desc {
  font-size: 11px; /* 缩小2号字体 */
  color: #666;
}

.selection-summary {
  margin-top: 16px;
  text-align: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

/* 全局字体大小调整 - 缩小2号 */
:deep(.el-button) {
  font-size: 11px;
}

:deep(.el-button--small) {
  font-size: 10px;
}

:deep(.el-tag) {
  font-size: 10px;
}

:deep(.el-alert__title) {
  font-size: 12px;
}

:deep(.el-alert__description) {
  font-size: 11px;
}

:deep(.el-collapse-item__header) {
  font-size: 12px;
}

:deep(.el-collapse-item__content) {
  font-size: 11px;
}

:deep(.el-checkbox__label) {
  font-size: 12px;
}

:deep(.el-checkbox-group) {
  font-size: 12px;
}

:deep(.el-dialog__title) {
  font-size: 14px;
}

:deep(.el-dialog__body) {
  font-size: 12px;
}
</style>
