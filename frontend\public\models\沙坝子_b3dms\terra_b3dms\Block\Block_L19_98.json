{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.09324251860380173, "root": {"boundingVolume": {"box": [-3.1708059310913086, 149.81373596191406, -69.09107208251953, 4.831969261169434, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 6.163915634155273]}, "children": [{"boundingVolume": {"box": [-3.1708059310913086, 143.2160186767578, -69.09107208251953, 4.831969261169434, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 6.163915634155273]}, "children": [{"boundingVolume": {"box": [-3.1708059310913086, 143.2160186767578, -71.65826416015625, 4.831969261169434, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 3.596721649169922]}, "children": [{"boundingVolume": {"box": [-5.586790561676025, 143.2160186767578, -71.65562438964844, 2.415984630584717, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 3.594074249267578]}, "content": {"uri": "Block_L22_461.b3dm"}, "geometricError": 0.013185101561248302, "refine": "REPLACE"}, {"boundingVolume": {"box": [-0.7548213005065918, 143.2160186767578, -71.65374755859375, 2.415984630584717, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 3.592205047607422]}, "content": {"uri": "Block_L22_460.b3dm"}, "geometricError": 0.012778994627296925, "refine": "REPLACE"}], "content": {"uri": "Block_L21_331.b3dm"}, "geometricError": 0.02587961032986641, "refine": "REPLACE"}, {"boundingVolume": {"box": [-1.3401424884796143, 143.2160186767578, -65.49435424804688, 3.0013058185577393, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 2.5671939849853516]}, "children": [{"boundingVolume": {"box": [-2.590686798095703, 140.85968017578125, -65.71576690673828, 1.75076162815094, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 2.3457775115966797]}, "content": {"uri": "Block_L22_459.b3dm"}, "geometricError": 0.0111917145550251, "refine": "REPLACE"}, {"boundingVolume": {"box": [0.4106191396713257, 140.85968017578125, -65.49435424804688, 1.2505441904067993, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 2.5671939849853516]}, "content": {"uri": "Block_L23_486.b3dm"}, "geometricError": 0.01086542196571827, "refine": "REPLACE"}, {"boundingVolume": {"box": [-0.9050190448760986, 145.57235717773438, -65.49435424804688, 2.5661823749542236, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 2.5671939849853516]}, "content": {"uri": "Block_L23_485.b3dm"}, "geometricError": 0.010958380065858364, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1443.b3dm"}, "geometricError": 0.021989384666085243, "refine": "REPLACE"}], "content": {"uri": "Block_L20_182.b3dm"}, "geometricError": 0.04746529087424278, "refine": "REPLACE"}, {"boundingVolume": {"box": [-3.1708059310913086, 154.52639770507812, -68.83953857421875, 4.831969261169434, 0.0, 0.0, 0.0, 6.59771728515625, 0.0, 0.0, 0.0, 5.912382125854492]}, "children": [{"boundingVolume": {"box": [-4.042276859283447, 150.677734375, -71.30303192138672, 3.960498332977295, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 3.4488906860351562]}, "children": [{"boundingVolume": {"box": [-4.0439066886901855, 150.677734375, -71.30303192138672, 3.9588682651519775, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 3.4488906860351562]}, "content": {"uri": "Block_L22_458.b3dm"}, "geometricError": 0.012645142152905464, "refine": "REPLACE"}], "content": {"uri": "Block_L21_330.b3dm"}, "geometricError": 0.025268109515309334, "refine": "REPLACE"}, {"boundingVolume": {"box": [-3.1708059310913086, 150.677734375, -65.3906478881836, 4.831969261169434, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 2.463491439819336]}, "children": [{"boundingVolume": {"box": [-5.586790561676025, 150.677734375, -65.3906478881836, 2.415984630584717, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 2.463491439819336]}, "content": {"uri": "Block_L23_484.b3dm"}, "geometricError": 0.010901016183197498, "refine": "REPLACE"}, {"boundingVolume": {"box": [-0.7548213005065918, 150.677734375, -65.3906478881836, 2.415984630584717, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 2.463491439819336]}, "content": {"uri": "Block_L23_483.b3dm"}, "geometricError": 0.010316219180822372, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1442.b3dm"}, "geometricError": 0.021121827885508537, "refine": "REPLACE"}, {"boundingVolume": {"box": [-3.1708059310913086, 157.27545166015625, -68.10370635986328, 4.831969261169434, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 5.176549911499023]}, "children": [{"boundingVolume": {"box": [-3.1708059310913086, 157.27545166015625, -68.1001968383789, 4.831969261169434, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 5.173036575317383]}, "content": {"uri": "Block_L22_457.b3dm"}, "geometricError": 0.011396357789635658, "refine": "REPLACE"}], "content": {"uri": "Block_L21_329.b3dm"}, "geometricError": 0.02279515564441681, "refine": "REPLACE"}], "content": {"uri": "Block_L20_181.b3dm"}, "geometricError": 0.04579062759876251, "refine": "REPLACE"}], "content": {"uri": "Block_L19_98.b3dm"}, "geometricError": 0.09324251860380173, "refine": "REPLACE"}}