# Simple database connection test
Write-Host "MTDT Database Connection Test" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.access_token
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test database connection
Write-Host "Step 2: Test database connection..." -ForegroundColor Yellow
$connectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $connectionData
    Write-Host "Database connection successful" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
} catch {
    Write-Host "Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Get tables
Write-Host "Step 3: Get database tables..." -ForegroundColor Yellow
try {
    $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $connectionData
    Write-Host "Found $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    
    $tablesResponse | ForEach-Object {
        Write-Host "- $($_.schema).$($_.name) ($($_.type))" -ForegroundColor White
    }
} catch {
    Write-Host "Get tables failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed successfully!" -ForegroundColor Green
Write-Host "You can now use the database layer functionality in MTDT." -ForegroundColor Cyan
