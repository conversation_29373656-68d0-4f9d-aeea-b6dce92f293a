# MTDT系统集成测试脚本
# 测试各个功能模块的集成情况

Write-Host "========================================" -ForegroundColor Green
Write-Host "MTDT系统集成测试开始" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 测试配置
$frontendUrl = "http://localhost:5174"
$backendUrl = "http://localhost:8000"
$productionFrontendUrl = "http://localhost:8080"

# 测试结果统计
$testResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Details = @()
}

function Test-Service {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Description
    )
    
    $testResults.Total++
    Write-Host "测试: $Name" -ForegroundColor Yellow
    Write-Host "描述: $Description" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $Url -Method HEAD -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 通过" -ForegroundColor Green
            $testResults.Passed++
            $testResults.Details += @{
                Name = $Name
                Status = "PASS"
                Message = "服务正常响应"
            }
        } else {
            Write-Host "❌ 失败 - 状态码: $($response.StatusCode)" -ForegroundColor Red
            $testResults.Failed++
            $testResults.Details += @{
                Name = $Name
                Status = "FAIL"
                Message = "状态码: $($response.StatusCode)"
            }
        }
    } catch {
        Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
        $testResults.Failed++
        $testResults.Details += @{
            Name = $Name
            Status = "FAIL"
            Message = $_.Exception.Message
        }
    }
    Write-Host ""
}

function Test-API {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null,
        [string]$Description
    )
    
    $testResults.Total++
    Write-Host "测试: $Name" -ForegroundColor Yellow
    Write-Host "描述: $Description" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            TimeoutSec = 10
        }
        
        if ($Headers.Count -gt 0) {
            $params.Headers = $Headers
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        Write-Host "✅ 通过" -ForegroundColor Green
        $testResults.Passed++
        $testResults.Details += @{
            Name = $Name
            Status = "PASS"
            Message = "API正常响应"
        }
    } catch {
        Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
        $testResults.Failed++
        $testResults.Details += @{
            Name = $Name
            Status = "FAIL"
            Message = $_.Exception.Message
        }
    }
    Write-Host ""
}

function Test-Docker {
    param(
        [string]$ServiceName,
        [string]$Description
    )
    
    $testResults.Total++
    Write-Host "测试: Docker服务 - $ServiceName" -ForegroundColor Yellow
    Write-Host "描述: $Description" -ForegroundColor Gray
    
    try {
        $result = docker-compose -f docker-compose.internal.yml ps --services --filter "status=running" | Where-Object { $_ -eq $ServiceName }
        if ($result) {
            Write-Host "✅ 通过" -ForegroundColor Green
            $testResults.Passed++
            $testResults.Details += @{
                Name = "Docker-$ServiceName"
                Status = "PASS"
                Message = "容器正常运行"
            }
        } else {
            Write-Host "❌ 失败 - 容器未运行" -ForegroundColor Red
            $testResults.Failed++
            $testResults.Details += @{
                Name = "Docker-$ServiceName"
                Status = "FAIL"
                Message = "容器未运行"
            }
        }
    } catch {
        Write-Host "❌ 失败 - 错误: $($_.Exception.Message)" -ForegroundColor Red
        $testResults.Failed++
        $testResults.Details += @{
            Name = "Docker-$ServiceName"
            Status = "FAIL"
            Message = $_.Exception.Message
        }
    }
    Write-Host ""
}

# 开始测试
Write-Host "1. 基础服务测试" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# 测试Docker容器状态
Test-Docker -ServiceName "postgres" -Description "PostgreSQL数据库服务"
Test-Docker -ServiceName "redis" -Description "Redis缓存服务"
Test-Docker -ServiceName "backend" -Description "后端API服务"
Test-Docker -ServiceName "frontend" -Description "前端Web服务"

Write-Host "2. 网络连接测试" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# 测试服务可访问性
Test-Service -Name "前端开发服务器" -Url $frontendUrl -Description "Vite开发服务器"
Test-Service -Name "生产前端服务" -Url $productionFrontendUrl -Description "容器化前端服务"
Test-Service -Name "后端API服务" -Url "$backendUrl/api/health" -Description "后端健康检查"

Write-Host "3. API功能测试" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# 测试主要API端点
Test-API -Name "健康检查API" -Url "$backendUrl/api/health" -Description "系统健康状态检查"
Test-API -Name "认证API" -Url "$backendUrl/api/auth/check" -Description "认证状态检查"

Write-Host "4. 数据库连接测试" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

# 测试数据库连接（需要认证，这里只测试端点可达性）
Test-Service -Name "数据库端口" -Url "http://localhost:5432" -Description "PostgreSQL数据库端口"

Write-Host "5. 文件系统测试" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# 检查关键文件和目录
$criticalPaths = @(
    @{ Path = "frontend/dist"; Description = "前端构建产物" },
    @{ Path = "backend/uploads"; Description = "文件上传目录" },
    @{ Path = ".env"; Description = "环境配置文件" },
    @{ Path = "docker-compose.internal.yml"; Description = "内网部署配置" }
)

foreach ($pathInfo in $criticalPaths) {
    $testResults.Total++
    Write-Host "测试: 文件系统 - $($pathInfo.Description)" -ForegroundColor Yellow
    
    if (Test-Path $pathInfo.Path) {
        Write-Host "✅ 通过" -ForegroundColor Green
        $testResults.Passed++
        $testResults.Details += @{
            Name = "FileSystem-$($pathInfo.Path)"
            Status = "PASS"
            Message = "路径存在"
        }
    } else {
        Write-Host "❌ 失败 - 路径不存在: $($pathInfo.Path)" -ForegroundColor Red
        $testResults.Failed++
        $testResults.Details += @{
            Name = "FileSystem-$($pathInfo.Path)"
            Status = "FAIL"
            Message = "路径不存在"
        }
    }
    Write-Host ""
}

# 输出测试结果
Write-Host "========================================" -ForegroundColor Green
Write-Host "测试结果汇总" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "总测试数: $($testResults.Total)" -ForegroundColor White
Write-Host "通过: $($testResults.Passed)" -ForegroundColor Green
Write-Host "失败: $($testResults.Failed)" -ForegroundColor Red

$successRate = [math]::Round(($testResults.Passed / $testResults.Total) * 100, 2)
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host ""
Write-Host "详细结果:" -ForegroundColor White
Write-Host "----------" -ForegroundColor White

foreach ($detail in $testResults.Details) {
    $statusColor = if ($detail.Status -eq "PASS") { "Green" } else { "Red" }
    $statusSymbol = if ($detail.Status -eq "PASS") { "✅" } else { "❌" }
    Write-Host "$statusSymbol $($detail.Name): $($detail.Message)" -ForegroundColor $statusColor
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "集成测试完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 根据测试结果设置退出码
if ($testResults.Failed -eq 0) {
    Write-Host "🎉 所有测试通过！系统运行正常。" -ForegroundColor Green
    exit 0
} else {
    Write-Host "⚠️  有 $($testResults.Failed) 个测试失败，请检查系统状态。" -ForegroundColor Yellow
    exit 1
}
