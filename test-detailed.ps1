# Detailed database connection test with debugging
Write-Host "MTDT Detailed Database Connection Test" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Step 1: Login and get token
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

Write-Host "Login request body: $loginData" -ForegroundColor Gray

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.access_token
    Write-Host "Login successful" -ForegroundColor Green
    Write-Host "Token (first 50 chars): $($token.Substring(0,50))..." -ForegroundColor Gray
    Write-Host "Full token length: $($token.Length)" -ForegroundColor Gray
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test token format
Write-Host ""
Write-Host "Step 2: Validate token format..." -ForegroundColor Yellow
if ($token -match '^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+$') {
    Write-Host "Token format is valid JWT" -ForegroundColor Green
} else {
    Write-Host "Token format is invalid" -ForegroundColor Red
    Write-Host "Token: $token" -ForegroundColor Red
    exit 1
}

# Step 3: Test database connection with detailed headers
Write-Host ""
Write-Host "Step 3: Test database connection..." -ForegroundColor Yellow

$connectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

Write-Host "Connection request body: $connectionData" -ForegroundColor Gray

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "Request headers:" -ForegroundColor Gray
$headers.GetEnumerator() | ForEach-Object {
    if ($_.Key -eq "Authorization") {
        Write-Host "  $($_.Key): Bearer $($token.Substring(0,20))..." -ForegroundColor Gray
    } else {
        Write-Host "  $($_.Key): $($_.Value)" -ForegroundColor Gray
    }
}

try {
    Write-Host "Making request to: http://localhost:8000/api/database/test-connection" -ForegroundColor Gray
    
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $connectionData -Verbose
    
    Write-Host "Database connection successful" -ForegroundColor Green
    Write-Host "Response: $($testResponse | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Get detailed error information
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
        
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Red
        }
    }
    
    Write-Host "Full exception: $($_.Exception)" -ForegroundColor Red
    exit 1
}

# Step 4: Test health endpoint (no auth required)
Write-Host ""
Write-Host "Step 4: Test health endpoint..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/health" -Method GET
    Write-Host "Health check successful" -ForegroundColor Green
    Write-Host "Health response: $($healthResponse | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed" -ForegroundColor Green
