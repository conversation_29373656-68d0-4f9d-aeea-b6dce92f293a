const { Pool } = require('pg');

// 列出所有用户
async function listAllUsers() {
  console.log('🔍 查询所有已注册用户...');
  
  const config = {
    host: 'localhost',
    port: 5432,
    database: 'webgis_db',
    user: 'testUser',
    password: '1234',
    max: 1,
    connectionTimeoutMillis: 5000,
  };

  let pool;
  let client;

  try {
    console.log(`📡 连接到: ${config.host}:${config.port}/${config.database}`);
    
    pool = new Pool(config);
    client = await pool.connect();
    
    console.log('✅ 数据库连接成功');
    
    // 查询所有用户
    const usersQuery = `
      SELECT
        user_id,
        username,
        email,
        full_name,
        is_active,
        login_attempts,
        locked_until,
        last_login,
        created_at
      FROM system.users
      ORDER BY created_at DESC
    `;
    
    const usersResult = await client.query(usersQuery);
    console.log(`\n📋 找到 ${usersResult.rows.length} 个用户:\n`);
    
    usersResult.rows.forEach((user, index) => {
      console.log(`${index + 1}. 用户名: ${user.username}`);
      console.log(`   邮箱: ${user.email}`);
      console.log(`   全名: ${user.full_name || '未设置'}`);
      console.log(`   状态: ${user.is_active ? '✅ 激活' : '❌ 未激活'}`);
      console.log(`   锁定: ${user.locked_until ? '🔒 已锁定' : '🔓 未锁定'}`);
      console.log(`   登录尝试: ${user.login_attempts}`);
      console.log(`   最后登录: ${user.last_login ? new Date(user.last_login).toLocaleString('zh-CN') : '从未登录'}`);
      console.log(`   创建时间: ${new Date(user.created_at).toLocaleString('zh-CN')}`);
      console.log('   ─────────────────────────────────────');
    });
    
    // 统计信息
    const activeUsers = usersResult.rows.filter(user => user.is_active).length;
    const lockedUsers = usersResult.rows.filter(user => user.locked_until).length;
    
    console.log(`\n📊 统计信息:`);
    console.log(`   总用户数: ${usersResult.rows.length}`);
    console.log(`   激活用户: ${activeUsers}`);
    console.log(`   锁定用户: ${lockedUsers}`);
    
    console.log('\n🎉 用户列表查询完成');
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    if (client) {
      client.release();
    }
    if (pool) {
      await pool.end();
    }
  }
}

// 运行查询
listAllUsers().catch(console.error);
