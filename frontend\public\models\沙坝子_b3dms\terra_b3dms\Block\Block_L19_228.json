{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.17394423484802246, "root": {"boundingVolume": {"box": [-9.935562133789062, 172.4344940185547, -67.4452896118164, 11.596725463867188, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.338869094848633]}, "children": [{"boundingVolume": {"box": [-16.700319290161133, 172.4344940185547, -67.43583679199219, 4.831968784332275, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.32942008972168]}, "children": [{"boundingVolume": {"box": [-16.700319290161133, 166.779296875, -67.43583679199219, 4.831968784332275, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 7.32942008972168]}, "children": [{"boundingVolume": {"box": [-16.700319290161133, 166.779296875, -70.47862243652344, 4.831968784332275, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 4.270961761474609]}, "children": [{"boundingVolume": {"box": [-16.700319290161133, 163.48043823242188, -70.47853088378906, 4.831968784332275, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 4.270870208740234]}, "content": {"uri": "Block_L22_456.b3dm"}, "geometricError": 0.012539396062493324, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.700319290161133, 169.13563537597656, -70.27420043945312, 4.831968784332275, 0.0, 0.0, 0.0, 3.298858642578125, 0.0, 0.0, 0.0, 4.066547393798828]}, "content": {"uri": "Block_L22_455.b3dm"}, "geometricError": 0.012892736122012138, "refine": "REPLACE"}], "content": {"uri": "Block_L21_328.b3dm"}, "geometricError": 0.025373630225658417, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.700319290161133, 166.94235229492188, -63.15703582763672, 4.831968784332275, 0.0, 0.0, 0.0, 5.492149353027344, 0.0, 0.0, 0.0, 3.050619125366211]}, "children": [{"boundingVolume": {"box": [-15.514089584350586, 164.2010498046875, -63.15703582763672, 3.645740032196045, 0.0, 0.0, 0.0, 2.7444839477539062, 0.0, 0.0, 0.0, 3.050619125366211]}, "content": {"uri": "Block_L23_474.b3dm"}, "geometricError": 0.01040777936577797, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.700319290161133, 169.69000244140625, -63.15703582763672, 4.831968784332275, 0.0, 0.0, 0.0, 2.7444839477539062, 0.0, 0.0, 0.0, 3.050619125366211]}, "content": {"uri": "Block_L23_473.b3dm"}, "geometricError": 0.010407964698970318, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1437.b3dm"}, "geometricError": 0.020817169919610023, "refine": "REPLACE"}], "content": {"uri": "Block_L20_180.b3dm"}, "geometricError": 0.04586288705468178, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.700319290161133, 178.08969116210938, -67.20620727539062, 4.831968784332275, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 7.099786758422852]}, "children": [{"boundingVolume": {"box": [-17.098957061767578, 178.08969116210938, -70.1644515991211, 4.43333101272583, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 4.14154052734375]}, "children": [{"boundingVolume": {"box": [-17.098957061767578, 175.2620849609375, -70.1644515991211, 4.43333101272583, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 4.14154052734375]}, "content": {"uri": "Block_L22_454.b3dm"}, "geometricError": 0.01266159676015377, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.235881805419922, 180.9172821044922, -69.7218017578125, 4.2964067459106445, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 3.6988868713378906]}, "content": {"uri": "Block_L22_453.b3dm"}, "geometricError": 0.012062231078743935, "refine": "REPLACE"}], "content": {"uri": "Block_L21_327.b3dm"}, "geometricError": 0.024725202471017838, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.700319290161133, 178.08969116210938, -63.064666748046875, 4.831968784332275, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 2.9582462310791016]}, "children": [{"boundingVolume": {"box": [-16.700319290161133, 175.2620849609375, -63.064666748046875, 4.831968784332275, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.9582462310791016]}, "content": {"uri": "Block_L23_472.b3dm"}, "geometricError": 0.009976712055504322, "refine": "REPLACE"}, {"boundingVolume": {"box": [-17.232202529907227, 180.9172821044922, -63.064666748046875, 4.300084590911865, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.9582462310791016]}, "content": {"uri": "Block_L23_471.b3dm"}, "geometricError": 0.009966736659407616, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1436.b3dm"}, "geometricError": 0.019957812502980232, "refine": "REPLACE"}], "content": {"uri": "Block_L20_179.b3dm"}, "geometricError": 0.04427686333656311, "refine": "REPLACE"}], "content": {"uri": "Block_L19_97.b3dm"}, "geometricError": 0.09009896963834763, "refine": "REPLACE"}, {"boundingVolume": {"box": [-5.103593349456787, 172.4344940185547, -67.24805450439453, 6.764756679534912, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.141633987426758]}, "children": [{"boundingVolume": {"box": [-5.103593349456787, 172.4344940185547, -67.24805450439453, 6.764756679534912, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.141633987426758]}, "children": [{"boundingVolume": {"box": [-5.103593349456787, 164.8942413330078, -69.62503814697266, 6.764756679534912, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 4.759307861328125]}, "children": [{"boundingVolume": {"box": [-5.103593349456787, 164.8942413330078, -69.62503814697266, 6.764756679534912, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 4.759307861328125]}, "content": {"uri": "Block_L22_452.b3dm"}, "geometricError": 0.01105443574488163, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1435.b3dm"}, "geometricError": 0.022091083228588104, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.049701690673828, 164.8942413330078, -62.48607635498047, 2.81864857673645, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.3796558380126953]}, "children": [{"boundingVolume": {"box": [-9.049701690673828, 163.00918579101562, -62.48607635498047, 2.81864857673645, 0.0, 0.0, 0.0, 1.8850631713867188, 0.0, 0.0, 0.0, 2.3796558380126953]}, "content": {"uri": "Block_L23_470.b3dm"}, "geometricError": 0.009802705608308315, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.049701690673828, 166.779296875, -62.48607635498047, 2.81864857673645, 0.0, 0.0, 0.0, 1.8850631713867188, 0.0, 0.0, 0.0, 2.3796558380126953]}, "content": {"uri": "Block_L23_469.b3dm"}, "geometricError": 0.00969760026782751, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1434.b3dm"}, "geometricError": 0.01950668729841709, "refine": "REPLACE"}, {"boundingVolume": {"box": [-2.284944772720337, 164.8942413330078, -62.48607635498047, 3.946108102798462, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.3796558380126953]}, "children": [{"boundingVolume": {"box": [-4.257998943328857, 164.8942413330078, -62.48607635498047, 1.973054051399231, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.3796558380126953]}, "content": {"uri": "Block_L23_468.b3dm"}, "geometricError": 0.009671293199062347, "refine": "REPLACE"}, {"boundingVolume": {"box": [-0.31189072132110596, 164.8942413330078, -62.48607635498047, 1.973054051399231, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.3796558380126953]}, "content": {"uri": "Block_L23_467.b3dm"}, "geometricError": 0.01028534211218357, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1433.b3dm"}, "geometricError": 0.019945712760090828, "refine": "REPLACE"}, {"boundingVolume": {"box": [-5.103593349456787, 176.20462036132812, -63.93883514404297, 6.764756679534912, 0.0, 0.0, 0.0, 7.540252685546875, 0.0, 0.0, 0.0, 3.8324146270751953]}, "children": [{"boundingVolume": {"box": [-5.103593349456787, 171.80613708496094, -63.93669128417969, 6.764756679534912, 0.0, 0.0, 0.0, 3.1417694091796875, 0.0, 0.0, 0.0, 3.8302745819091797]}, "content": {"uri": "Block_L23_466.b3dm"}, "geometricError": 0.01004057377576828, "refine": "REPLACE"}, {"boundingVolume": {"box": [-4.503357887268066, 179.3463897705078, -63.22581481933594, 6.164521217346191, 0.0, 0.0, 0.0, 4.3984832763671875, 0.0, 0.0, 0.0, 3.1193981170654297]}, "content": {"uri": "Block_L22_451.b3dm"}, "geometricError": 0.011358508840203285, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1432.b3dm"}, "geometricError": 0.021270623430609703, "refine": "REPLACE"}], "content": {"uri": "Block_L21_826.b3dm"}, "geometricError": 0.041208863258361816, "refine": "REPLACE"}], "content": {"uri": "Block_L20_435.b3dm"}, "geometricError": 0.08272691816091537, "refine": "REPLACE"}], "content": {"uri": "Block_L19_228.b3dm"}, "geometricError": 0.17394423484802246, "refine": "REPLACE"}}