@echo off
echo Deploying MTDT Updates...

REM 检查是否有修改
echo Checking for changes...

REM 显示当前服务状态
echo Current service status:
docker-compose -f docker-compose.internal.yml ps

echo.
echo WARNING: This will briefly interrupt service for internal users.
echo Continue? (Y/N)
set /p confirm=
if /i not "%confirm%"=="Y" (
    echo Deployment cancelled.
    pause
    exit /b 0
)

echo.
echo Starting deployment...

REM 重新构建并部署
echo Building and deploying updates...
docker-compose -f docker-compose.internal.yml up --build -d

REM 等待服务启动
echo Waiting for services to restart...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo Checking service status...
docker-compose -f docker-compose.internal.yml ps

REM 获取本地IP
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found_ip
    )
)
:found_ip

echo.
echo ========================================
echo Deployment Completed!
echo ========================================
echo.
echo Internal Users Access: http://%LOCAL_IP%:8080
echo Backend API: http://%LOCAL_IP%:8000/api
echo.
echo Services should be available now.
echo.
pause
