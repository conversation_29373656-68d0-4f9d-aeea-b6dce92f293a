import * as XLSX from 'xlsx'

export interface ImportOptions {
  coordinateSystem?: 'WGS84' | 'GCJ02' | 'BD09'
  encoding?: 'UTF-8' | 'GBK' | 'GB2312'
  geometryField?: string
  latField?: string
  lngField?: string
}

export interface ImportResult {
  success: boolean
  data?: any
  error?: string
  metadata?: {
    filename: string
    fileSize: number
    recordCount: number
    geometryType?: string
    bounds?: [number, number, number, number]
  }
}

/**
 * 数据导入工具类
 */
export class DataImporter {
  /**
   * 导入文件数据
   */
  static async importFile(file: File, options: ImportOptions = {}): Promise<ImportResult> {
    try {
      const fileExtension = this.getFileExtension(file.name)
      
      switch (fileExtension) {
        case 'geojson':
        case 'json':
          return await this.importGeoJSON(file, options)
        case 'kml':
          return await this.importKML(file, options)
        case 'csv':
          return await this.importCSV(file, options)
        case 'xlsx':
        case 'xls':
          return await this.importExcel(file, options)
        case 'shp':
          return await this.importShapefile(file, options)
        default:
          return {
            success: false,
            error: `不支持的文件格式: ${fileExtension}`
          }
      }
    } catch (error) {
      console.error('文件导入失败:', error)
      return {
        success: false,
        error: `导入失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 导入GeoJSON文件
   */
  private static async importGeoJSON(file: File, options: ImportOptions): Promise<ImportResult> {
    const text = await this.readFileAsText(file, options.encoding)
    
    try {
      const geojson = JSON.parse(text)
      
      // 验证GeoJSON格式
      if (!this.validateGeoJSON(geojson)) {
        return {
          success: false,
          error: '无效的GeoJSON格式'
        }
      }

      // 坐标系转换（如果需要）
      if (options.coordinateSystem && options.coordinateSystem !== 'WGS84') {
        this.transformCoordinateSystem(geojson, options.coordinateSystem, 'WGS84')
      }

      const metadata = this.extractGeoJSONMetadata(file, geojson)

      return {
        success: true,
        data: geojson,
        metadata
      }
    } catch (error) {
      return {
        success: false,
        error: `GeoJSON解析失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 导入KML文件
   */
  private static async importKML(file: File, options: ImportOptions): Promise<ImportResult> {
    const text = await this.readFileAsText(file, options.encoding)
    
    try {
      const geojson = this.convertKMLToGeoJSON(text)
      const metadata = this.extractGeoJSONMetadata(file, geojson)

      return {
        success: true,
        data: geojson,
        metadata
      }
    } catch (error) {
      return {
        success: false,
        error: `KML解析失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 导入CSV文件
   */
  private static async importCSV(file: File, options: ImportOptions): Promise<ImportResult> {
    const text = await this.readFileAsText(file, options.encoding)
    
    try {
      const rows = this.parseCSV(text)
      if (rows.length === 0) {
        return {
          success: false,
          error: 'CSV文件为空'
        }
      }

      const geojson = this.convertCSVToGeoJSON(rows, options)
      const metadata = this.extractGeoJSONMetadata(file, geojson)

      return {
        success: true,
        data: geojson,
        metadata
      }
    } catch (error) {
      return {
        success: false,
        error: `CSV解析失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 导入Excel文件
   */
  private static async importExcel(file: File, options: ImportOptions): Promise<ImportResult> {
    try {
      const arrayBuffer = await this.readFileAsArrayBuffer(file)
      const workbook = XLSX.read(arrayBuffer, { type: 'array' })
      
      // 使用第一个工作表
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      if (jsonData.length === 0) {
        return {
          success: false,
          error: 'Excel文件为空'
        }
      }

      // 转换为标准格式
      const headers = jsonData[0] as string[]
      const rows = jsonData.slice(1).map(row => {
        const obj: any = {}
        headers.forEach((header, index) => {
          obj[header] = (row as any[])[index] || ''
        })
        return obj
      })

      const geojson = this.convertCSVToGeoJSON(rows, options)
      const metadata = this.extractGeoJSONMetadata(file, geojson)

      return {
        success: true,
        data: geojson,
        metadata
      }
    } catch (error) {
      return {
        success: false,
        error: `Excel解析失败: ${(error as Error).message}`
      }
    }
  }

  /**
   * 导入Shapefile文件
   */
  private static async importShapefile(file: File, options: ImportOptions): Promise<ImportResult> {
    // Shapefile导入需要专门的库，这里先返回错误
    return {
      success: false,
      error: 'Shapefile导入功能正在开发中，请使用其他格式'
    }
  }

  /**
   * 读取文件为文本
   */
  private static readFileAsText(file: File, encoding: string = 'UTF-8'): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, encoding)
    })
  }

  /**
   * 读取文件为ArrayBuffer
   */
  private static readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || ''
  }

  /**
   * 验证GeoJSON格式
   */
  private static validateGeoJSON(geojson: any): boolean {
    if (!geojson || typeof geojson !== 'object') return false
    
    if (geojson.type === 'FeatureCollection') {
      return Array.isArray(geojson.features)
    } else if (geojson.type === 'Feature') {
      return geojson.geometry && geojson.properties
    } else if (['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'].includes(geojson.type)) {
      return geojson.coordinates
    }
    
    return false
  }

  /**
   * 解析CSV文本
   */
  private static parseCSV(text: string): any[] {
    const lines = text.split('\n').filter(line => line.trim())
    if (lines.length === 0) return []

    const headers = this.parseCSVLine(lines[0])
    const rows = lines.slice(1).map(line => {
      const values = this.parseCSVLine(line)
      const obj: any = {}
      headers.forEach((header, index) => {
        obj[header] = values[index] || ''
      })
      return obj
    })

    return rows
  }

  /**
   * 解析CSV行
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  /**
   * 转换CSV数据为GeoJSON
   */
  private static convertCSVToGeoJSON(rows: any[], options: ImportOptions): any {
    const features = rows.map((row, index) => {
      let geometry = null

      // 尝试从指定字段或常见字段名中提取几何信息
      const latField = options.latField || this.findField(row, ['lat', 'latitude', '纬度', 'y'])
      const lngField = options.lngField || this.findField(row, ['lng', 'lon', 'longitude', '经度', 'x'])

      if (latField && lngField) {
        const lat = parseFloat(row[latField])
        const lng = parseFloat(row[lngField])
        
        if (!isNaN(lat) && !isNaN(lng)) {
          geometry = {
            type: 'Point',
            coordinates: [lng, lat]
          }
        }
      }

      return {
        type: 'Feature',
        properties: row,
        geometry,
        id: index
      }
    })

    return {
      type: 'FeatureCollection',
      features
    }
  }

  /**
   * 查找字段名
   */
  private static findField(obj: any, candidates: string[]): string | null {
    for (const candidate of candidates) {
      if (obj.hasOwnProperty(candidate)) {
        return candidate
      }
    }
    return null
  }

  /**
   * 转换KML为GeoJSON（简化版）
   */
  private static convertKMLToGeoJSON(kmlText: string): any {
    // 这里需要实现KML到GeoJSON的转换
    // 可以使用togeojson库或自己实现解析
    throw new Error('KML转换功能正在开发中')
  }

  /**
   * 提取GeoJSON元数据
   */
  private static extractGeoJSONMetadata(file: File, geojson: any): any {
    const features = geojson.features || []
    const geometryTypes = new Set()
    let bounds: [number, number, number, number] | undefined

    features.forEach((feature: any) => {
      if (feature.geometry) {
        geometryTypes.add(feature.geometry.type)
        
        // 计算边界（简化版）
        if (feature.geometry.type === 'Point') {
          const [lng, lat] = feature.geometry.coordinates
          if (!bounds) {
            bounds = [lng, lat, lng, lat]
          } else {
            bounds[0] = Math.min(bounds[0], lng)
            bounds[1] = Math.min(bounds[1], lat)
            bounds[2] = Math.max(bounds[2], lng)
            bounds[3] = Math.max(bounds[3], lat)
          }
        }
      }
    })

    return {
      filename: file.name,
      fileSize: file.size,
      recordCount: features.length,
      geometryType: Array.from(geometryTypes).join(', '),
      bounds
    }
  }

  /**
   * 坐标系转换（简化版）
   */
  private static transformCoordinateSystem(geojson: any, from: string, to: string): void {
    // 这里可以集成proj4js库进行坐标系转换
    console.warn(`坐标系转换功能正在开发中: ${from} -> ${to}`)
  }

  /**
   * 获取支持的导入格式
   */
  static getSupportedFormats(): Array<{ value: string; label: string; accept: string }> {
    return [
      { value: 'geojson', label: 'GeoJSON', accept: '.geojson,.json' },
      { value: 'kml', label: 'KML', accept: '.kml' },
      { value: 'csv', label: 'CSV', accept: '.csv' },
      { value: 'excel', label: 'Excel', accept: '.xlsx,.xls' },
      { value: 'shapefile', label: 'Shapefile', accept: '.shp' }
    ]
  }
}
