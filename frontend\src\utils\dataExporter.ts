import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

export interface ExportOptions {
  format: 'geojson' | 'kml' | 'csv' | 'excel' | 'shapefile'
  filename?: string
  includeAttributes?: boolean
  coordinateSystem?: 'WGS84' | 'GCJ02' | 'BD09'
}

export interface ExportData {
  type: 'FeatureCollection' | 'Feature' | 'table'
  data: any
  metadata?: {
    title?: string
    description?: string
    source?: string
    createdAt?: string
    author?: string
  }
}

/**
 * 数据导出工具类
 */
export class DataExporter {
  /**
   * 导出数据到指定格式
   */
  static async exportData(data: ExportData, options: ExportOptions): Promise<void> {
    const filename = options.filename || `export_${Date.now()}`
    
    try {
      switch (options.format) {
        case 'geojson':
          await this.exportGeoJSON(data, filename)
          break
        case 'kml':
          await this.exportKML(data, filename)
          break
        case 'csv':
          await this.exportCSV(data, filename)
          break
        case 'excel':
          await this.exportExcel(data, filename)
          break
        case 'shapefile':
          await this.exportShapefile(data, filename)
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }
    } catch (error) {
      console.error('数据导出失败:', error)
      throw new Error(`导出失败: ${(error as Error).message}`)
    }
  }

  /**
   * 导出为GeoJSON格式
   */
  private static async exportGeoJSON(data: ExportData, filename: string): Promise<void> {
    const geojson = this.prepareGeoJSONData(data)
    const blob = new Blob([JSON.stringify(geojson, null, 2)], { 
      type: 'application/geo+json' 
    })
    saveAs(blob, `${filename}.geojson`)
  }

  /**
   * 导出为KML格式
   */
  private static async exportKML(data: ExportData, filename: string): Promise<void> {
    const kml = this.convertToKML(data)
    const blob = new Blob([kml], { type: 'application/vnd.google-earth.kml+xml' })
    saveAs(blob, `${filename}.kml`)
  }

  /**
   * 导出为CSV格式
   */
  private static async exportCSV(data: ExportData, filename: string): Promise<void> {
    const csv = this.convertToCSV(data)
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8' })
    saveAs(blob, `${filename}.csv`)
  }

  /**
   * 导出为Excel格式
   */
  private static async exportExcel(data: ExportData, filename: string): Promise<void> {
    const workbook = this.createExcelWorkbook(data)
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    saveAs(blob, `${filename}.xlsx`)
  }

  /**
   * 导出为Shapefile格式（简化版）
   */
  private static async exportShapefile(data: ExportData, filename: string): Promise<void> {
    // 这里可以集成shp-write库或其他Shapefile生成库
    // 目前先导出为GeoJSON格式作为替代
    console.warn('Shapefile导出功能正在开发中，将导出为GeoJSON格式')
    await this.exportGeoJSON(data, filename)
  }

  /**
   * 准备GeoJSON数据
   */
  private static prepareGeoJSONData(data: ExportData): any {
    if (data.type === 'FeatureCollection' || data.type === 'Feature') {
      const geojson = { ...data.data }
      
      // 添加元数据
      if (data.metadata) {
        geojson.metadata = data.metadata
      }
      
      return geojson
    }
    
    // 如果是表格数据，尝试转换为GeoJSON
    if (data.type === 'table' && Array.isArray(data.data)) {
      return {
        type: 'FeatureCollection',
        features: data.data.map((row: any, index: number) => ({
          type: 'Feature',
          properties: row,
          geometry: null,
          id: index
        })),
        metadata: data.metadata
      }
    }
    
    return data.data
  }

  /**
   * 转换为KML格式
   */
  private static convertToKML(data: ExportData): string {
    const geojson = this.prepareGeoJSONData(data)
    
    let kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>${data.metadata?.title || '导出数据'}</name>
    <description>${data.metadata?.description || ''}</description>
`

    if (geojson.features) {
      geojson.features.forEach((feature: any, index: number) => {
        kml += this.featureToKMLPlacemark(feature, index)
      })
    }

    kml += `  </Document>
</kml>`

    return kml
  }

  /**
   * 要素转换为KML Placemark
   */
  private static featureToKMLPlacemark(feature: any, index: number): string {
    const name = feature.properties?.name || `要素_${index + 1}`
    const description = feature.properties?.description || ''
    
    let placemark = `    <Placemark>
      <name>${name}</name>
      <description>${description}</description>
`

    if (feature.geometry) {
      placemark += this.geometryToKML(feature.geometry)
    }

    placemark += `    </Placemark>
`

    return placemark
  }

  /**
   * 几何体转换为KML
   */
  private static geometryToKML(geometry: any): string {
    switch (geometry.type) {
      case 'Point':
        return `      <Point>
        <coordinates>${geometry.coordinates[0]},${geometry.coordinates[1]}</coordinates>
      </Point>
`
      case 'LineString':
        const lineCoords = geometry.coordinates.map((coord: number[]) => 
          `${coord[0]},${coord[1]}`).join(' ')
        return `      <LineString>
        <coordinates>${lineCoords}</coordinates>
      </LineString>
`
      case 'Polygon':
        const polyCoords = geometry.coordinates[0].map((coord: number[]) => 
          `${coord[0]},${coord[1]}`).join(' ')
        return `      <Polygon>
        <outerBoundaryIs>
          <LinearRing>
            <coordinates>${polyCoords}</coordinates>
          </LinearRing>
        </outerBoundaryIs>
      </Polygon>
`
      default:
        return ''
    }
  }

  /**
   * 转换为CSV格式
   */
  private static convertToCSV(data: ExportData): string {
    const geojson = this.prepareGeoJSONData(data)
    
    if (!geojson.features || geojson.features.length === 0) {
      return ''
    }

    // 收集所有属性字段
    const allFields = new Set<string>()
    geojson.features.forEach((feature: any) => {
      if (feature.properties) {
        Object.keys(feature.properties).forEach(key => allFields.add(key))
      }
    })

    // 添加几何信息字段
    allFields.add('geometry_type')
    allFields.add('coordinates')

    const fields = Array.from(allFields)
    
    // 创建CSV头部
    let csv = fields.map(field => `"${field}"`).join(',') + '\n'

    // 添加数据行
    geojson.features.forEach((feature: any) => {
      const row = fields.map(field => {
        if (field === 'geometry_type') {
          return `"${feature.geometry?.type || ''}"`
        } else if (field === 'coordinates') {
          return `"${feature.geometry?.coordinates ? JSON.stringify(feature.geometry.coordinates) : ''}"`
        } else {
          const value = feature.properties?.[field] || ''
          return `"${String(value).replace(/"/g, '""')}"`
        }
      })
      csv += row.join(',') + '\n'
    })

    return csv
  }

  /**
   * 创建Excel工作簿
   */
  private static createExcelWorkbook(data: ExportData): XLSX.WorkBook {
    const workbook = XLSX.utils.book_new()
    const geojson = this.prepareGeoJSONData(data)

    if (geojson.features && geojson.features.length > 0) {
      // 准备数据表
      const tableData = geojson.features.map((feature: any, index: number) => ({
        ID: index + 1,
        几何类型: feature.geometry?.type || '',
        坐标: feature.geometry?.coordinates ? JSON.stringify(feature.geometry.coordinates) : '',
        ...feature.properties
      }))

      const worksheet = XLSX.utils.json_to_sheet(tableData)
      XLSX.utils.book_append_sheet(workbook, worksheet, '要素数据')
    }

    // 添加元数据表
    if (data.metadata) {
      const metadataSheet = XLSX.utils.json_to_sheet([data.metadata])
      XLSX.utils.book_append_sheet(workbook, metadataSheet, '元数据')
    }

    return workbook
  }

  /**
   * 获取支持的导出格式
   */
  static getSupportedFormats(): Array<{ value: string; label: string; description: string }> {
    return [
      { value: 'geojson', label: 'GeoJSON', description: '地理数据交换格式' },
      { value: 'kml', label: 'KML', description: 'Google Earth格式' },
      { value: 'csv', label: 'CSV', description: '逗号分隔值文件' },
      { value: 'excel', label: 'Excel', description: 'Microsoft Excel文件' },
      { value: 'shapefile', label: 'Shapefile', description: 'ESRI Shapefile格式（开发中）' }
    ]
  }
}
