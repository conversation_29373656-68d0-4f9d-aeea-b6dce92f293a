<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>MTDT数据库连接测试</h1>
    
    <div class="test-section info">
        <h3>测试说明</h3>
        <p>此页面用于测试数据库连接功能的修复情况。请按顺序执行以下测试：</p>
        <ol>
            <li>清理旧的连接配置</li>
            <li>初始化新的连接配置</li>
            <li>测试数据库连接</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>1. 清理和初始化连接配置</h3>
        <button onclick="clearAndInitConnections()">清理并初始化连接</button>
        <div id="init-result"></div>
    </div>

    <div class="test-section">
        <h3>2. 查看当前连接配置</h3>
        <button onclick="showConnections()">显示连接列表</button>
        <div id="connections-result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试数据库连接</h3>
        <button onclick="testDatabaseConnection()">测试连接</button>
        <div id="test-result"></div>
    </div>

    <div class="test-section">
        <h3>4. 获取数据库表列表</h3>
        <button onclick="getDatabaseTables()">获取表列表</button>
        <div id="tables-result"></div>
    </div>

    <script>
        // 模拟数据库连接管理器
        class DatabaseConnectionManager {
            static connections = new Map()

            static addConnection(connection) {
                const id = connection.id || `conn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
                const connectionWithId = { ...connection, id }
                this.connections.set(id, connectionWithId)
                this.saveToStorage()
                return id
            }

            static getConnection(id) {
                return this.connections.get(id)
            }

            static getAllConnections() {
                return Array.from(this.connections.values())
            }

            static removeConnection(id) {
                const result = this.connections.delete(id)
                this.saveToStorage()
                return result
            }

            static saveToStorage() {
                try {
                    const connectionsArray = Array.from(this.connections.values())
                    const connectionsWithoutPassword = connectionsArray.map(conn => ({
                        ...conn,
                        password: '' // 清空密码
                    }))
                    localStorage.setItem('database-connections', JSON.stringify(connectionsWithoutPassword))
                } catch (error) {
                    console.warn('Failed to save connections to localStorage:', error)
                }
            }

            static loadFromStorage() {
                try {
                    const stored = localStorage.getItem('database-connections')
                    if (stored) {
                        const connections = JSON.parse(stored)
                        connections.forEach(conn => {
                            if (conn.id) {
                                this.connections.set(conn.id, conn)
                            }
                        })
                    }
                } catch (error) {
                    console.warn('Failed to load connections from localStorage:', error)
                }
            }

            static initializeDefaults() {
                if (this.connections.size === 0) {
                    // 添加本地连接（用于前端开发）
                    this.addConnection({
                        name: 'WebGIS主数据库(本地)',
                        type: 'postgresql',
                        host: 'localhost',
                        port: 5432,
                        database: 'webgis_db',
                        username: 'mtdt_user',
                        password: 'mtdt_secure_password_2024',
                        schema: 'public'
                    })
                    
                    // 添加容器内部连接（用于后端API）
                    this.addConnection({
                        name: 'WebGIS主数据库(容器)',
                        type: 'postgresql',
                        host: 'postgres',
                        port: 5432,
                        database: 'webgis_db',
                        username: 'mtdt_user',
                        password: 'mtdt_secure_password_2024',
                        schema: 'public'
                    })
                }
            }
        }

        // 初始化
        DatabaseConnectionManager.loadFromStorage()

        function clearAndInitConnections() {
            const resultDiv = document.getElementById('init-result')
            try {
                // 清理localStorage
                localStorage.removeItem('database-connections')
                
                // 清理内存中的连接
                DatabaseConnectionManager.connections.clear()
                
                // 重新初始化
                DatabaseConnectionManager.initializeDefaults()
                
                resultDiv.innerHTML = '<div class="success">✅ 连接配置已清理并重新初始化</div>'
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 初始化失败: ${error.message}</div>`
            }
        }

        function showConnections() {
            const resultDiv = document.getElementById('connections-result')
            const connections = DatabaseConnectionManager.getAllConnections()
            
            if (connections.length === 0) {
                resultDiv.innerHTML = '<div class="info">📝 没有找到连接配置</div>'
                return
            }

            let html = '<div class="success">📋 当前连接配置:</div><pre>'
            connections.forEach((conn, index) => {
                html += `${index + 1}. ${conn.name}\n`
                html += `   ID: ${conn.id}\n`
                html += `   主机: ${conn.host}:${conn.port}\n`
                html += `   数据库: ${conn.database}\n`
                html += `   用户: ${conn.username}\n`
                html += `   模式: ${conn.schema || 'public'}\n\n`
            })
            html += '</pre>'
            resultDiv.innerHTML = html
        }

        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('test-result')
            const connections = DatabaseConnectionManager.getAllConnections()
            
            if (connections.length === 0) {
                resultDiv.innerHTML = '<div class="error">❌ 没有可用的连接配置</div>'
                return
            }

            // 使用容器连接进行测试
            const containerConnection = connections.find(conn => conn.host === 'postgres')
            if (!containerConnection) {
                resultDiv.innerHTML = '<div class="error">❌ 没有找到容器连接配置</div>'
                return
            }

            resultDiv.innerHTML = '<div class="info">🔄 正在测试连接...</div>'

            try {
                const response = await fetch('/api/database/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
                    },
                    body: JSON.stringify({
                        host: containerConnection.host,
                        port: containerConnection.port,
                        database: containerConnection.database,
                        username: containerConnection.username,
                        password: containerConnection.password,
                        schema: containerConnection.schema
                    })
                })

                const result = await response.json()
                
                if (result.success) {
                    let html = '<div class="success">✅ 数据库连接成功!</div><pre>'
                    html += `消息: ${result.message}\n`
                    if (result.postgisVersion) {
                        html += `PostGIS版本: ${result.postgisVersion}\n`
                    }
                    html += '</pre>'
                    resultDiv.innerHTML = html
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 连接失败: ${result.message}</div>`
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`
            }
        }

        async function getDatabaseTables() {
            const resultDiv = document.getElementById('tables-result')
            const connections = DatabaseConnectionManager.getAllConnections()
            
            if (connections.length === 0) {
                resultDiv.innerHTML = '<div class="error">❌ 没有可用的连接配置</div>'
                return
            }

            const containerConnection = connections.find(conn => conn.host === 'postgres')
            if (!containerConnection) {
                resultDiv.innerHTML = '<div class="error">❌ 没有找到容器连接配置</div>'
                return
            }

            resultDiv.innerHTML = '<div class="info">🔄 正在获取表列表...</div>'

            try {
                const response = await fetch('/api/database/tables', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
                    },
                    body: JSON.stringify({
                        host: containerConnection.host,
                        port: containerConnection.port,
                        database: containerConnection.database,
                        username: containerConnection.username,
                        password: containerConnection.password,
                        schema: containerConnection.schema
                    })
                })

                const tables = await response.json()
                
                if (Array.isArray(tables) && tables.length > 0) {
                    let html = '<div class="success">✅ 找到数据库表:</div><pre>'
                    tables.forEach((table, index) => {
                        html += `${index + 1}. ${table.schema}.${table.name} (${table.type})\n`
                        if (table.geometryColumns && table.geometryColumns.length > 0) {
                            html += `   几何列: ${table.geometryColumns.map(col => col.columnName).join(', ')}\n`
                        }
                        if (table.totalRows !== undefined) {
                            html += `   记录数: ${table.totalRows}\n`
                        }
                        html += '\n'
                    })
                    html += '</pre>'
                    resultDiv.innerHTML = html
                } else {
                    resultDiv.innerHTML = '<div class="info">📝 没有找到包含几何列的表</div>'
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`
            }
        }

        // 页面加载时显示当前状态
        window.onload = function() {
            showConnections()
        }
    </script>
</body>
</html>
