{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.18012304604053497, "root": {"boundingVolume": {"box": [-104.68896484375, 15.757158279418945, -25.90442657470703, 17.28729248046875, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 11.522685050964355]}, "children": [{"boundingVolume": {"box": [-110.4513931274414, 17.783519744873047, -21.864112854003906, 11.524864196777344, 0.0, 0.0, 0.0, 12.63668441772461, 0.0, 0.0, 0.0, 7.482369422912598]}, "children": [{"boundingVolume": {"box": [-110.4513931274414, 17.78409767150879, -21.861745834350586, 11.524864196777344, 0.0, 0.0, 0.0, 12.636106491088867, 0.0, 0.0, 0.0, 7.480003356933594]}, "children": [{"boundingVolume": {"box": [-110.25506591796875, 11.466044425964355, -20.19746971130371, 11.328540802001953, 0.0, 0.0, 0.0, 6.318053245544434, 0.0, 0.0, 0.0, 5.815727233886719]}, "children": [{"boundingVolume": {"box": [-113.03857421875, 11.466044425964355, -20.19746971130371, 8.46722412109375, 0.0, 0.0, 0.0, 6.318053245544434, 0.0, 0.0, 0.0, 5.815727233886719]}, "content": {"uri": "Block_L22_1192.b3dm"}, "geometricError": 0.01180670503526926, "refine": "REPLACE"}, {"boundingVolume": {"box": [-101.74893951416016, 11.599201202392578, -20.050853729248047, 2.8224105834960938, 0.0, 0.0, 0.0, 6.184896469116211, 0.0, 0.0, 0.0, 5.669111251831055]}, "content": {"uri": "Block_L22_1191.b3dm"}, "geometricError": 0.011883880943059921, "refine": "REPLACE"}], "content": {"uri": "Block_L21_702.b3dm"}, "geometricError": 0.023753011599183083, "refine": "REPLACE"}, {"boundingVolume": {"box": [-110.4513931274414, 24.102149963378906, -21.861745834350586, 11.524864196777344, 0.0, 0.0, 0.0, 6.318053245544434, 0.0, 0.0, 0.0, 7.480003356933594]}, "children": [{"boundingVolume": {"box": [-114.29301452636719, 24.102149963378906, -19.80724334716797, 7.6832427978515625, 0.0, 0.0, 0.0, 6.318053245544434, 0.0, 0.0, 0.0, 5.425500869750977]}, "content": {"uri": "Block_L22_1190.b3dm"}, "geometricError": 0.011569027788937092, "refine": "REPLACE"}, {"boundingVolume": {"box": [-102.76815032958984, 24.102149963378906, -21.860332489013672, 3.8416213989257812, 0.0, 0.0, 0.0, 6.318053245544434, 0.0, 0.0, 0.0, 7.478589057922363]}, "content": {"uri": "Block_L22_1189.b3dm"}, "geometricError": 0.011069048196077347, "refine": "REPLACE"}], "content": {"uri": "Block_L21_701.b3dm"}, "geometricError": 0.022427093237638474, "refine": "REPLACE"}], "content": {"uri": "Block_L20_381.b3dm"}, "geometricError": 0.04615309461951256, "refine": "REPLACE"}], "content": {"uri": "Block_L19_199.b3dm"}, "geometricError": 0.09232592582702637, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 9.647555351257324, -25.90442657470703, 5.762428283691406, 0.0, 0.0, 0.0, 8.55344295501709, 0.0, 0.0, 0.0, 11.522685050964355]}, "children": [{"boundingVolume": {"box": [-93.16410064697266, 10.9038667678833, -30.696453094482422, 5.762428283691406, 0.0, 0.0, 0.0, 7.297131538391113, 0.0, 0.0, 0.0, 6.718327522277832]}, "children": [{"boundingVolume": {"box": [-92.25282287597656, 7.865405082702637, -28.961627960205078, 4.8511505126953125, 0.0, 0.0, 0.0, 4.258669853210449, 0.0, 0.0, 0.0, 4.983504295349121]}, "children": [{"boundingVolume": {"box": [-92.25055694580078, 7.8702287673950195, -28.961627960205078, 4.848884582519531, 0.0, 0.0, 0.0, 4.253846168518066, 0.0, 0.0, 0.0, 4.983504295349121]}, "content": {"uri": "Block_L22_1188.b3dm"}, "geometricError": 0.012128815054893494, "refine": "REPLACE"}], "content": {"uri": "Block_L21_700.b3dm"}, "geometricError": 0.024280956014990807, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 15.16253662109375, -30.69559097290039, 5.762428283691406, 0.0, 0.0, 0.0, 3.038461685180664, 0.0, 0.0, 0.0, 6.717467308044434]}, "children": [{"boundingVolume": {"box": [-90.88131713867188, 15.16253662109375, -32.93474578857422, 3.4796409606933594, 0.0, 0.0, 0.0, 3.038461685180664, 0.0, 0.0, 0.0, 4.478311538696289]}, "content": {"uri": "Block_L22_1187.b3dm"}, "geometricError": 0.012340241111814976, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 15.16253662109375, -26.217281341552734, 5.762428283691406, 0.0, 0.0, 0.0, 3.038461685180664, 0.0, 0.0, 0.0, 2.2391557693481445]}, "content": {"uri": "Block_L22_1186.b3dm"}, "geometricError": 0.011329109780490398, "refine": "REPLACE"}], "content": {"uri": "Block_L21_699.b3dm"}, "geometricError": 0.02336098812520504, "refine": "REPLACE"}], "content": {"uri": "Block_L20_380.b3dm"}, "geometricError": 0.04734404385089874, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 9.647555351257324, -19.179933547973633, 5.762428283691406, 0.0, 0.0, 0.0, 8.55344295501709, 0.0, 0.0, 0.0, 4.798191070556641]}, "children": [{"boundingVolume": {"box": [-93.16410064697266, 4.658046722412109, -19.175670623779297, 5.762428283691406, 0.0, 0.0, 0.0, 3.563934326171875, 0.0, 0.0, 0.0, 4.793928146362305]}, "children": [{"boundingVolume": {"box": [-96.04531860351562, 4.658046722412109, -18.688304901123047, 2.881214141845703, 0.0, 0.0, 0.0, 3.563934326171875, 0.0, 0.0, 0.0, 4.306562423706055]}, "content": {"uri": "Block_L22_1185.b3dm"}, "geometricError": 0.012213509529829025, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.28288269042969, 4.658046722412109, -19.175670623779297, 2.881214141845703, 0.0, 0.0, 0.0, 3.563934326171875, 0.0, 0.0, 0.0, 4.793928146362305]}, "content": {"uri": "Block_L23_952.b3dm"}, "geometricError": 0.01060003787279129, "refine": "REPLACE"}], "content": {"uri": "Block_L21_698.b3dm"}, "geometricError": 0.022541824728250504, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 13.2114896774292, -19.179933547973633, 5.762428283691406, 0.0, 0.0, 0.0, 4.989508628845215, 0.0, 0.0, 0.0, 4.798191070556641]}, "children": [{"boundingVolume": {"box": [-96.04531860351562, 13.2114896774292, -19.179933547973633, 2.881214141845703, 0.0, 0.0, 0.0, 4.989508628845215, 0.0, 0.0, 0.0, 4.798191070556641]}, "content": {"uri": "Block_L22_1184.b3dm"}, "geometricError": 0.011235488578677177, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.28288269042969, 10.331317901611328, -20.906173706054688, 2.881214141845703, 0.0, 0.0, 0.0, 2.1093363761901855, 0.0, 0.0, 0.0, 3.071950912475586]}, "content": {"uri": "Block_L23_951.b3dm"}, "geometricError": 0.010062790475785732, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1677.b3dm"}, "geometricError": 0.021425919607281685, "refine": "REPLACE"}], "content": {"uri": "Block_L21_941.b3dm"}, "geometricError": 0.044105686247348785, "refine": "REPLACE"}], "content": {"uri": "Block_L19_198.b3dm"}, "geometricError": 0.09184424579143524, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 24.31060028076172, -25.90442657470703, 5.762428283691406, 0.0, 0.0, 0.0, 6.109602928161621, 0.0, 0.0, 0.0, 11.522685050964355]}, "children": [{"boundingVolume": {"box": [-93.16410064697266, 24.31060028076172, -30.678865432739258, 5.762428283691406, 0.0, 0.0, 0.0, 6.109602928161621, 0.0, 0.0, 0.0, 6.733209609985352]}, "children": [{"boundingVolume": {"box": [-93.16410064697266, 24.31060028076172, -30.678865432739258, 5.762428283691406, 0.0, 0.0, 0.0, 6.109602928161621, 0.0, 0.0, 0.0, 6.733209609985352]}, "children": [{"boundingVolume": {"box": [-90.029052734375, 24.31060028076172, -33.42987060546875, 2.6273841857910156, 0.0, 0.0, 0.0, 6.109602928161621, 0.0, 0.0, 0.0, 3.905263900756836]}, "content": {"uri": "Block_L22_1183.b3dm"}, "geometricError": 0.012760327197611332, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 21.25579833984375, -26.735130310058594, 5.762428283691406, 0.0, 0.0, 0.0, 3.0548009872436523, 0.0, 0.0, 0.0, 2.7894744873046875]}, "content": {"uri": "Block_L22_1182.b3dm"}, "geometricError": 0.011955739930272102, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.16410064697266, 27.365402221679688, -26.735130310058594, 5.762428283691406, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 2.7894744873046875]}, "content": {"uri": "Block_L22_1181.b3dm"}, "geometricError": 0.01171643566340208, "refine": "REPLACE"}], "content": {"uri": "Block_L21_697.b3dm"}, "geometricError": 0.02422187477350235, "refine": "REPLACE"}], "content": {"uri": "Block_L20_379.b3dm"}, "geometricError": 0.048459794372320175, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.31201171875, 24.31060028076172, -19.163700103759766, 5.6145172119140625, 0.0, 0.0, 0.0, 6.109602928161621, 0.0, 0.0, 0.0, 4.781956672668457]}, "children": [{"boundingVolume": {"box": [-93.53877258300781, 21.764934539794922, -19.214923858642578, 5.387752532958984, 0.0, 0.0, 0.0, 3.5639352798461914, 0.0, 0.0, 0.0, 4.730731010437012]}, "children": [{"boundingVolume": {"box": [-96.68364715576172, 21.764934539794922, -19.215953826904297, 2.2428817749023438, 0.0, 0.0, 0.0, 3.5639352798461914, 0.0, 0.0, 0.0, 4.729701995849609]}, "content": {"uri": "Block_L23_950.b3dm"}, "geometricError": 0.009480527602136135, "refine": "REPLACE"}, {"boundingVolume": {"box": [-91.30073547363281, 22.772090911865234, -20.116003036499023, 3.140033721923828, 0.0, 0.0, 0.0, 2.556778907775879, 0.0, 0.0, 0.0, 3.829652786254883]}, "content": {"uri": "Block_L23_949.b3dm"}, "geometricError": 0.009745548479259014, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1676.b3dm"}, "geometricError": 0.019251244142651558, "refine": "REPLACE"}, {"boundingVolume": {"box": [-93.31201171875, 27.874536514282227, -19.163700103759766, 5.6145172119140625, 0.0, 0.0, 0.0, 2.5456676483154297, 0.0, 0.0, 0.0, 4.781956672668457]}, "children": [{"boundingVolume": {"box": [-96.11927032470703, 27.874536514282227, -19.163700103759766, 2.8072586059570312, 0.0, 0.0, 0.0, 2.5456676483154297, 0.0, 0.0, 0.0, 4.781956672668457]}, "content": {"uri": "Block_L23_948.b3dm"}, "geometricError": 0.009304819628596306, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.50475311279297, 27.874536514282227, -19.965595245361328, 2.8072586059570312, 0.0, 0.0, 0.0, 2.5456676483154297, 0.0, 0.0, 0.0, 3.9800615310668945]}, "content": {"uri": "Block_L23_947.b3dm"}, "geometricError": 0.009821739979088306, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1675.b3dm"}, "geometricError": 0.01920616254210472, "refine": "REPLACE"}], "content": {"uri": "Block_L21_940.b3dm"}, "geometricError": 0.03853217512369156, "refine": "REPLACE"}], "content": {"uri": "Block_L20_494.b3dm"}, "geometricError": 0.0862116664648056, "refine": "REPLACE"}], "content": {"uri": "Block_L18_106.b3dm"}, "geometricError": 0.18012304604053497, "refine": "REPLACE"}}