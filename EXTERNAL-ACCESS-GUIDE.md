# 🌐 山思数字平台外网访问指南

## 📋 概述

本指南将帮助您配置系统，使其能够从互联网访问。我们提供了多种方案，您可以根据自己的需求选择。

## 🚀 方案1：路由器端口转发（推荐）

### 步骤1：部署外网配置
```powershell
# 运行外网部署脚本
.\deploy-external.bat
```

### 步骤2：配置路由器端口转发

#### 2.1 登录路由器管理界面
- 打开浏览器，访问路由器IP（通常是 `***********` 或 `***********`）
- 使用管理员账号登录

#### 2.2 找到端口转发设置
- 查找"端口转发"、"虚拟服务器"、"NAT转发"等选项
- 通常在"高级设置"或"网络设置"中

#### 2.3 添加转发规则
```
规则1 - 前端服务：
  外部端口：8080
  内部IP：*************
  内部端口：8080
  协议：TCP

规则2 - 后端API：
  外部端口：8000
  内部IP：*************
  内部端口：8000
  协议：TCP
```

### 步骤3：获取公网IP
```powershell
# 方法1：命令行查询
curl ifconfig.me

# 方法2：网页查询
# 访问 https://whatismyipaddress.com/
```

### 步骤4：测试外网访问
```
前端：http://您的公网IP:8080
后端API：http://您的公网IP:8000/api
```

## 🔧 方案2：内网穿透工具

### 选项A：ngrok（简单快速）

#### 安装ngrok
1. 访问 https://ngrok.com/download
2. 下载并解压到任意目录
3. 注册账号获取认证令牌

#### 使用ngrok
```powershell
# 认证（只需执行一次）
ngrok authtoken 您的认证令牌

# 启动前端穿透
ngrok http 8080

# 新开窗口，启动后端穿透
ngrok http 8000
```

#### 访问地址
ngrok会提供临时域名，如：
```
前端：https://abc123.ngrok.io
后端：https://def456.ngrok.io
```

### 选项B：花生壳（国内服务）
1. 访问 https://hsk.oray.com/
2. 注册账号并下载客户端
3. 配置内网穿透规则

## 🛡️ 安全配置

### 1. 强密码设置
编辑 `.env` 文件：
```env
# 使用强密码
POSTGRES_PASSWORD=YourStrongPassword123!@#
REDIS_PASSWORD=YourRedisPassword456!@#
JWT_SECRET=your-super-strong-jwt-secret-key-change-this-now
```

### 2. 防火墙配置
```powershell
# Windows防火墙允许端口
netsh advfirewall firewall add rule name="MTDT Frontend" dir=in action=allow protocol=TCP localport=8080
netsh advfirewall firewall add rule name="MTDT Backend" dir=in action=allow protocol=TCP localport=8000
```

### 3. CORS配置
在 `.env` 文件中设置允许的域名：
```env
CORS_ORIGIN=http://您的公网IP:8080,https://您的域名.com
```

## 🔒 SSL/HTTPS配置（可选）

### 使用Let's Encrypt免费证书
```bash
# 安装certbot
# 获取SSL证书
certbot certonly --standalone -d 您的域名.com

# 配置nginx使用证书
```

## 📊 监控和维护

### 查看系统状态
```powershell
# 查看服务状态
docker-compose -f docker-compose.external.yml ps

# 查看实时日志
docker-compose -f docker-compose.external.yml logs -f

# 查看资源使用
docker stats
```

### 重启服务
```powershell
# 重启所有服务
docker-compose -f docker-compose.external.yml restart

# 重启特定服务
docker-compose -f docker-compose.external.yml restart backend
```

## ⚠️ 注意事项

### 安全提醒
1. **定期更新密码**：至少每3个月更换一次
2. **监控访问日志**：定期检查异常访问
3. **备份数据**：定期备份数据库
4. **更新系统**：及时更新Docker镜像

### 网络要求
1. **公网IP**：需要固定或动态公网IP
2. **带宽**：建议上行带宽至少2Mbps
3. **稳定性**：确保网络连接稳定

### 成本考虑
1. **路由器端口转发**：免费，但需要公网IP
2. **内网穿透**：免费版有限制，付费版更稳定
3. **云服务器**：按需付费，更专业

## 🆘 故障排除

### 常见问题

#### 1. 无法从外网访问
```powershell
# 检查服务是否运行
docker-compose -f docker-compose.external.yml ps

# 检查端口是否开放
netstat -an | findstr :8080
netstat -an | findstr :8000

# 检查防火墙设置
netsh advfirewall firewall show rule name="MTDT Frontend"
```

#### 2. 路由器配置问题
- 确认路由器支持端口转发
- 检查内网IP是否正确
- 确认端口映射规则

#### 3. 公网IP变化
- 使用动态DNS服务（如花生壳、No-IP）
- 定期检查IP变化

## 📞 技术支持

如果遇到问题：
1. 检查部署日志
2. 确认网络配置
3. 验证防火墙设置
4. 测试内网访问是否正常

## 🎯 下一步

成功配置外网访问后，建议：
1. 设置SSL证书启用HTTPS
2. 配置域名解析
3. 设置监控告警
4. 制定备份策略
