# MTDT系统部署指南

## 概述

本文档详细说明了MTDT山思数字平台的部署流程，包括内网部署、外网部署和开发环境配置。

## 部署方式对比

| 部署方式 | 适用场景 | 访问范围 | 安全性 | 维护难度 |
|---------|---------|---------|--------|----------|
| 内网部署 | 企业内部使用 | 局域网 | 高 | 低 |
| 外网部署 | 公网访问 | 互联网 | 中 | 中 |
| 开发部署 | 开发测试 | 本机 | 低 | 低 |

## 内网部署（推荐）

### 适用场景
- 企业内部使用
- 数据安全要求高
- 网络环境稳定

### 部署步骤

#### 1. 环境准备
```bash
# 检查系统要求
- Windows 10/11 或 Linux
- Docker Desktop 4.0+
- 8GB+ RAM
- 20GB+ 磁盘空间
- 稳定的网络连接
```

#### 2. 快速部署
```bash
# 使用增强部署脚本（推荐）
deploy-enhanced.bat

# 或使用标准部署脚本
deploy-internal.bat
```

#### 3. 手动部署
```bash
# 1. 检查配置文件
if not exist ".env" (
    echo 创建默认配置...
    copy .env.example .env
)

# 2. 构建前端
cd frontend
npm install
npm run build
cd ..

# 3. 启动服务
docker-compose -f docker-compose.internal.yml up -d

# 4. 验证部署
health-check.bat
```

#### 4. 访问系统
```
内网访问: http://[服务器IP]:8080
本机访问: http://localhost:8080
后端API: http://[服务器IP]:8000/api
```

### 网络配置

#### 防火墙设置
```bash
# Windows防火墙
- 允许端口 8080 (前端)
- 允许端口 8000 (后端)
- 允许端口 5432 (数据库，可选)

# Linux防火墙 (ufw)
sudo ufw allow 8080
sudo ufw allow 8000
```

#### 端口映射
```yaml
# docker-compose.internal.yml
services:
  frontend:
    ports:
      - "0.0.0.0:8080:80"    # 绑定所有网络接口
  backend:
    ports:
      - "0.0.0.0:8000:3000"  # 绑定所有网络接口
```

## 外网部署

### 适用场景
- 需要公网访问
- 远程办公支持
- 客户演示

### 部署步骤

#### 1. 域名和SSL配置
```bash
# 1. 准备域名
your-domain.com

# 2. 申请SSL证书
# 使用Let's Encrypt或其他CA

# 3. 配置反向代理
# 使用Nginx或Traefik
```

#### 2. 安全配置
```bash
# 1. 修改默认端口
# 2. 启用HTTPS
# 3. 配置防火墙
# 4. 设置访问控制
```

#### 3. 部署命令
```bash
# 使用外网部署脚本
deploy-external.bat

# 或手动部署
docker-compose -f docker-compose.external.yml up -d
```

### 安全建议

#### 1. 网络安全
- 使用HTTPS加密传输
- 配置防火墙规则
- 限制访问IP范围
- 定期更新系统补丁

#### 2. 应用安全
- 修改默认密码
- 启用访问日志
- 配置备份策略
- 监控异常访问

## 开发环境部署

### 混合开发模式（推荐）

#### 1. 启动基础服务
```bash
# 启动数据库和后端
docker-compose -f docker-compose.internal.yml up -d postgres redis backend
```

#### 2. 启动前端开发服务器
```bash
cd frontend
npm install
npm run dev
```

#### 3. 访问地址
```
开发前端: http://localhost:5174
生产前端: http://localhost:8080
后端API: http://localhost:8000
```

### 完全本地开发

#### 1. 安装依赖
```bash
# 前端依赖
cd frontend
npm install

# 后端依赖
cd ../backend
npm install
```

#### 2. 配置数据库
```bash
# 启动数据库容器
docker-compose -f docker-compose.internal.yml up -d postgres redis
```

#### 3. 启动服务
```bash
# 启动后端
cd backend
npm run start:dev

# 启动前端
cd ../frontend
npm run dev
```

## 系统管理

### 管理工具

#### 1. 系统管理控制台
```bash
manage-system.bat
```

功能包括：
- 查看系统状态
- 启动/停止服务
- 查看日志
- 系统更新
- 数据库管理
- 性能测试

#### 2. 健康检查
```bash
health-check.bat
```

检查项目：
- 容器状态
- 网络连接
- 数据库连接
- 响应时间
- 文件系统

#### 3. 性能测试
```bash
performance-test.bat
```

测试内容：
- 资源使用率
- 响应时间
- 并发性能
- 内存使用

### 日常维护

#### 1. 日志管理
```bash
# 查看所有日志
docker-compose -f docker-compose.internal.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.internal.yml logs backend

# 实时日志
docker-compose -f docker-compose.internal.yml logs -f
```

#### 2. 数据备份
```bash
# 自动备份
manage-system.bat -> 选择备份选项

# 手动备份
docker-compose -f docker-compose.internal.yml exec postgres pg_dump -U mtdt_user webgis_db > backup.sql
```

#### 3. 系统更新
```bash
# 使用管理控制台
manage-system.bat -> 选择更新选项

# 手动更新
docker-compose -f docker-compose.internal.yml down
docker-compose -f docker-compose.internal.yml up --build -d
```

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查Docker状态
docker --version
docker-compose --version

# 检查端口占用
netstat -an | findstr ":8080\|:8000\|:5432"

# 查看错误日志
docker-compose -f docker-compose.internal.yml logs
```

#### 2. 网络连接问题
```bash
# 检查防火墙
# Windows: 控制面板 -> 系统和安全 -> Windows Defender 防火墙
# Linux: sudo ufw status

# 检查端口绑定
docker-compose -f docker-compose.internal.yml ps
```

#### 3. 数据库连接失败
```bash
# 检查数据库容器
docker-compose -f docker-compose.internal.yml ps postgres

# 测试数据库连接
docker-compose -f docker-compose.internal.yml exec postgres psql -U mtdt_user -d webgis_db -c "SELECT 1"

# 查看数据库日志
docker-compose -f docker-compose.internal.yml logs postgres
```

#### 4. 前端无法访问
```bash
# 检查前端容器
docker-compose -f docker-compose.internal.yml ps frontend

# 检查前端构建
ls frontend/dist

# 重新构建前端
cd frontend && npm run build
```

### 性能优化

#### 1. 系统优化
- 增加系统内存
- 使用SSD存储
- 优化网络配置
- 定期清理缓存

#### 2. 应用优化
- 启用前端压缩
- 优化数据库查询
- 使用Redis缓存
- 配置CDN加速

#### 3. Docker优化
- 清理无用镜像
- 优化镜像大小
- 配置资源限制
- 使用多阶段构建

## 监控和告警

### 系统监控
```bash
# 资源监控
docker stats

# 日志监控
docker-compose -f docker-compose.internal.yml logs -f

# 健康检查
health-check.bat
```

### 告警配置
- 设置资源使用阈值
- 配置服务异常告警
- 监控磁盘空间
- 跟踪响应时间

## 安全最佳实践

### 1. 访问控制
- 使用强密码
- 启用双因素认证
- 限制管理员权限
- 定期审查用户权限

### 2. 数据保护
- 定期备份数据
- 加密敏感数据
- 配置访问日志
- 监控异常操作

### 3. 网络安全
- 使用HTTPS
- 配置防火墙
- 限制IP访问
- 定期安全扫描

## 扩展部署

### 集群部署
- 使用Docker Swarm
- 配置负载均衡
- 实现高可用
- 数据库主从复制

### 云部署
- AWS/Azure/阿里云
- 使用托管数据库
- 配置自动扩缩容
- 实现灾备方案

---

如需更多帮助，请参考：
- [README.md](README.md) - 项目概述
- [API文档](API.md) - 接口说明
- [用户手册](USER_GUIDE.md) - 使用指南
