@echo off
title MTDT System Management
color 0B

:menu
cls
echo.
echo ========================================
echo      MTDT System Management Console
echo ========================================
echo.
echo Please select an option:
echo.
echo 1. View System Status
echo 2. Start Services
echo 3. Stop Services
echo 4. Restart Services
echo 5. View Logs
echo 6. Update System
echo 7. Backup Database
echo 8. Restore Database
echo 9. Performance Test
echo 10. Clean Up System
echo 11. Development Mode
echo 0. Exit
echo.
set /p choice="Enter your choice (0-11): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto restart
if "%choice%"=="5" goto logs
if "%choice%"=="6" goto update
if "%choice%"=="7" goto backup
if "%choice%"=="8" goto restore
if "%choice%"=="9" goto performance
if "%choice%"=="10" goto cleanup
if "%choice%"=="11" goto devmode
if "%choice%"=="0" goto exit
goto menu

:status
cls
echo ========================================
echo           System Status
echo ========================================
echo.
echo Docker Services:
docker-compose -f docker-compose.internal.yml ps
echo.
echo System Resources:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
echo.
echo Network Ports:
netstat -an | findstr ":5174\|:8080\|:8000\|:5432\|:6379"
echo.
pause
goto menu

:start
cls
echo ========================================
echo         Starting Services
echo ========================================
echo.
docker-compose -f docker-compose.internal.yml up -d
echo.
echo Services started. Waiting for initialization...
timeout /t 15 /nobreak >nul
docker-compose -f docker-compose.internal.yml ps
echo.
pause
goto menu

:stop
cls
echo ========================================
echo         Stopping Services
echo ========================================
echo.
docker-compose -f docker-compose.internal.yml down
echo.
echo All services stopped.
echo.
pause
goto menu

:restart
cls
echo ========================================
echo        Restarting Services
echo ========================================
echo.
docker-compose -f docker-compose.internal.yml restart
echo.
echo Services restarted. Waiting for initialization...
timeout /t 15 /nobreak >nul
docker-compose -f docker-compose.internal.yml ps
echo.
pause
goto menu

:logs
cls
echo ========================================
echo            Service Logs
echo ========================================
echo.
echo Select service to view logs:
echo 1. All services
echo 2. Backend
echo 3. Frontend
echo 4. Database
echo 5. Redis
echo 0. Back to menu
echo.
set /p logchoice="Enter choice: "

if "%logchoice%"=="1" docker-compose -f docker-compose.internal.yml logs --tail=50
if "%logchoice%"=="2" docker-compose -f docker-compose.internal.yml logs backend --tail=50
if "%logchoice%"=="3" docker-compose -f docker-compose.internal.yml logs frontend --tail=50
if "%logchoice%"=="4" docker-compose -f docker-compose.internal.yml logs postgres --tail=50
if "%logchoice%"=="5" docker-compose -f docker-compose.internal.yml logs redis --tail=50
if "%logchoice%"=="0" goto menu

echo.
pause
goto menu

:update
cls
echo ========================================
echo         Updating System
echo ========================================
echo.
echo This will rebuild and restart all services.
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Stopping services...
docker-compose -f docker-compose.internal.yml down

echo Building frontend...
cd frontend
call npm run build
cd ..

echo Starting updated services...
docker-compose -f docker-compose.internal.yml up --build -d

echo.
echo Update completed!
timeout /t 10 /nobreak >nul
docker-compose -f docker-compose.internal.yml ps
echo.
pause
goto menu

:backup
cls
echo ========================================
echo        Database Backup
echo ========================================
echo.
set backup_file=backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql
set backup_file=%backup_file: =0%

echo Creating database backup: %backup_file%
docker-compose -f docker-compose.internal.yml exec -T postgres pg_dump -U mtdt_user webgis_db > %backup_file%

if %ERRORLEVEL% EQU 0 (
    echo Backup created successfully: %backup_file%
) else (
    echo Backup failed!
)
echo.
pause
goto menu

:restore
cls
echo ========================================
echo       Database Restore
echo ========================================
echo.
echo Available backup files:
dir *.sql /b 2>nul
echo.
set /p restore_file="Enter backup filename to restore: "

if not exist "%restore_file%" (
    echo File not found!
    pause
    goto menu
)

echo.
echo WARNING: This will overwrite the current database!
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo Restoring database from %restore_file%...
docker-compose -f docker-compose.internal.yml exec -T postgres psql -U mtdt_user -d webgis_db < %restore_file%

if %ERRORLEVEL% EQU 0 (
    echo Database restored successfully!
) else (
    echo Restore failed!
)
echo.
pause
goto menu

:performance
cls
echo ========================================
echo        Performance Test
echo ========================================
echo.
call performance-test.bat
pause
goto menu

:cleanup
cls
echo ========================================
echo         System Cleanup
echo ========================================
echo.
echo This will remove unused Docker images and containers.
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Cleaning up Docker system...
docker system prune -f
docker volume prune -f

echo.
echo Cleanup completed!
echo.
pause
goto menu

:devmode
cls
echo ========================================
echo        Development Mode
echo ========================================
echo.
echo Starting development environment...
echo.
echo 1. Ensuring production services are running...
docker-compose -f docker-compose.internal.yml up -d postgres redis backend

echo.
echo 2. Starting frontend development server...
start "Frontend Dev Server" cmd /k "cd frontend && npm run dev"

echo.
echo Development environment ready!
echo - Production backend: http://localhost:8000
echo - Development frontend: http://localhost:5174
echo - Database: localhost:5432
echo.
pause
goto menu

:exit
cls
echo.
echo Thank you for using MTDT System Management Console!
echo.
exit /b 0
