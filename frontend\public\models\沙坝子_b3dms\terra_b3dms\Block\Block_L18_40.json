{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.2100631594657898, "root": {"boundingVolume": {"box": [34.781402587890625, 17.414936065673828, -68.7321548461914, 13.637746810913086, 0.0, 0.0, 0.0, 12.626957893371582, 0.0, 0.0, 0.0, 12.658126831054688]}, "children": [{"boundingVolume": {"box": [26.826051712036133, 11.101457595825195, -67.3060302734375, 5.682394027709961, 0.0, 0.0, 0.0, 6.313478469848633, 0.0, 0.0, 0.0, 11.231998443603516]}, "content": {"uri": "Block_L19_78.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.826051712036133, 23.728416442871094, -67.6465835571289, 5.682394027709961, 0.0, 0.0, 0.0, 6.313479423522949, 0.0, 0.0, 0.0, 11.381820678710938]}, "children": [{"boundingVolume": {"box": [26.826051712036133, 23.728416442871094, -74.26465606689453, 5.682394027709961, 0.0, 0.0, 0.0, 6.313479423522949, 0.0, 0.0, 0.0, 4.7637481689453125]}, "children": [{"boundingVolume": {"box": [26.826051712036133, 20.57167625427246, -74.23137664794922, 5.682394027709961, 0.0, 0.0, 0.0, 3.156740188598633, 0.0, 0.0, 0.0, 4.73046875]}, "children": [{"boundingVolume": {"box": [24.45838737487793, 20.88780403137207, -73.7703857421875, 3.314729690551758, 0.0, 0.0, 0.0, 2.8406124114990234, 0.0, 0.0, 0.0, 4.269481658935547]}, "content": {"uri": "Block_L22_322.b3dm"}, "geometricError": 0.013073049485683441, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.14078140258789, 20.57167625427246, -74.22810363769531, 2.367664337158203, 0.0, 0.0, 0.0, 3.156740188598633, 0.0, 0.0, 0.0, 4.727195739746094]}, "content": {"uri": "Block_L22_321.b3dm"}, "geometricError": 0.013932708650827408, "refine": "REPLACE"}], "content": {"uri": "Block_L21_261.b3dm"}, "geometricError": 0.027092086151242256, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.826051712036133, 26.885154724121094, -74.53202819824219, 5.682394027709961, 0.0, 0.0, 0.0, 3.1567392349243164, 0.0, 0.0, 0.0, 4.419536590576172]}, "children": [{"boundingVolume": {"box": [23.98485565185547, 25.306785583496094, -72.9234619140625, 2.8411970138549805, 0.0, 0.0, 0.0, 1.578369140625, 0.0, 0.0, 0.0, 2.8109664916992188]}, "content": {"uri": "Block_L22_320.b3dm"}, "geometricError": 0.013504745438694954, "refine": "REPLACE"}, {"boundingVolume": {"box": [23.98485565185547, 28.463523864746094, -73.63975524902344, 2.8411970138549805, 0.0, 0.0, 0.0, 1.5783700942993164, 0.0, 0.0, 0.0, 2.1469459533691406]}, "content": {"uri": "Block_L22_319.b3dm"}, "geometricError": 0.013883420266211033, "refine": "REPLACE"}, {"boundingVolume": {"box": [29.667247772216797, 26.885154724121094, -75.03099822998047, 2.8411970138549805, 0.0, 0.0, 0.0, 3.1567392349243164, 0.0, 0.0, 0.0, 3.9205703735351562]}, "content": {"uri": "Block_L22_318.b3dm"}, "geometricError": 0.014145633205771446, "refine": "REPLACE"}], "content": {"uri": "Block_L21_260.b3dm"}, "geometricError": 0.02773330919444561, "refine": "REPLACE"}], "content": {"uri": "Block_L20_145.b3dm"}, "geometricError": 0.05492278188467026, "refine": "REPLACE"}, {"boundingVolume": {"box": [26.1088809967041, 20.377212524414062, -62.882835388183594, 4.96522331237793, 0.0, 0.0, 0.0, 2.962277412414551, 0.0, 0.0, 0.0, 6.618072509765625]}, "children": [{"boundingVolume": {"box": [26.10679054260254, 20.377212524414062, -62.882835388183594, 4.963132858276367, 0.0, 0.0, 0.0, 2.962277412414551, 0.0, 0.0, 0.0, 6.618072509765625]}, "children": [{"boundingVolume": {"box": [26.10679054260254, 20.373416900634766, -67.84638977050781, 4.963132858276367, 0.0, 0.0, 0.0, 2.958481788635254, 0.0, 0.0, 0.0, 1.6545181274414062]}, "content": {"uri": "Block_L22_317.b3dm"}, "geometricError": 0.012255141511559486, "refine": "REPLACE"}, {"boundingVolume": {"box": [23.522808074951172, 19.549789428710938, -61.22831726074219, 2.3791494369506836, 0.0, 0.0, 0.0, 2.134852409362793, 0.0, 0.0, 0.0, 4.963554382324219]}, "content": {"uri": "Block_L22_316.b3dm"}, "geometricError": 0.011095408350229263, "refine": "REPLACE"}], "content": {"uri": "Block_L21_259.b3dm"}, "geometricError": 0.023556239902973175, "refine": "REPLACE"}], "content": {"uri": "Block_L20_144.b3dm"}, "geometricError": 0.04708113521337509, "refine": "REPLACE"}], "content": {"uri": "Block_L19_77.b3dm"}, "geometricError": 0.10445701330900192, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.46379852294922, 17.414936065673828, -73.92180633544922, 7.955352783203125, 0.0, 0.0, 0.0, 12.626957893371582, 0.0, 0.0, 0.0, 7.468475341796875]}, "children": [{"boundingVolume": {"box": [40.46379852294922, 10.049211502075195, -73.9244613647461, 7.955352783203125, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 7.455474853515625]}, "children": [{"boundingVolume": {"box": [35.16023254394531, 10.049211502075195, -73.06170654296875, 2.651784896850586, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 6.513980865478516]}, "children": [{"boundingVolume": {"box": [35.16023254394531, 10.049211502075195, -75.75589752197266, 2.651784896850586, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 3.7903900146484375]}, "content": {"uri": "Block_L22_315.b3dm"}, "geometricError": 0.01486237719655037, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.7725944519043, 10.049211502075195, -69.25808715820312, 2.264148712158203, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 2.7074241638183594]}, "content": {"uri": "Block_L22_314.b3dm"}, "geometricError": 0.012617476284503937, "refine": "REPLACE"}], "content": {"uri": "Block_L21_258.b3dm"}, "geometricError": 0.027149813249707222, "refine": "REPLACE"}, {"boundingVolume": {"box": [43.11558532714844, 10.049211502075195, -79.65933227539062, 5.303567886352539, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 1.7206077575683594]}, "children": [{"boundingVolume": {"box": [40.46379852294922, 10.049211502075195, -79.27056884765625, 2.651784896850586, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 0.908966064453125]}, "content": {"uri": "Block_L22_313.b3dm"}, "geometricError": 0.015528514049947262, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.76736831665039, 10.049211502075195, -80.24693298339844, 2.651782989501953, 0.0, 0.0, 0.0, 5.261232376098633, 0.0, 0.0, 0.0, 1.0841140747070312]}, "content": {"uri": "Block_L22_312.b3dm"}, "geometricError": 0.015809159725904465, "refine": "REPLACE"}], "content": {"uri": "Block_L21_257.b3dm"}, "geometricError": 0.031358618289232254, "refine": "REPLACE"}], "content": {"uri": "Block_L20_143.b3dm"}, "geometricError": 0.05787501856684685, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.46379852294922, 22.676170349121094, -75.35324096679688, 7.955352783203125, 0.0, 0.0, 0.0, 7.365725517272949, 0.0, 0.0, 0.0, 5.333641052246094]}, "children": [{"boundingVolume": {"box": [36.486122131347656, 22.676170349121094, -75.2103042602539, 3.9776763916015625, 0.0, 0.0, 0.0, 7.365725517272949, 0.0, 0.0, 0.0, 5.0937042236328125]}, "children": [{"boundingVolume": {"box": [36.486122131347656, 18.99330711364746, -75.2093734741211, 3.9776763916015625, 0.0, 0.0, 0.0, 3.682863235473633, 0.0, 0.0, 0.0, 5.0927734375]}, "content": {"uri": "Block_L22_311.b3dm"}, "geometricError": 0.0152587303891778, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.486122131347656, 26.359031677246094, -78.3738784790039, 3.9776763916015625, 0.0, 0.0, 0.0, 3.6828622817993164, 0.0, 0.0, 0.0, 0.8169708251953125]}, "content": {"uri": "Block_L22_310.b3dm"}, "geometricError": 0.015276462770998478, "refine": "REPLACE"}], "content": {"uri": "Block_L21_256.b3dm"}, "geometricError": 0.030529657378792763, "refine": "REPLACE"}, {"boundingVolume": {"box": [44.44147491455078, 22.676170349121094, -79.55488586425781, 3.9776763916015625, 0.0, 0.0, 0.0, 7.365725517272949, 0.0, 0.0, 0.0, 1.1180992126464844]}, "children": [{"boundingVolume": {"box": [44.44147491455078, 18.99330711364746, -79.55767822265625, 3.9776763916015625, 0.0, 0.0, 0.0, 3.682863235473633, 0.0, 0.0, 0.0, 1.1120491027832031]}, "content": {"uri": "Block_L22_309.b3dm"}, "geometricError": 0.015607010573148727, "refine": "REPLACE"}, {"boundingVolume": {"box": [44.44147491455078, 26.359031677246094, -79.58921813964844, 3.9776763916015625, 0.0, 0.0, 0.0, 3.6828622817993164, 0.0, 0.0, 0.0, 1.0002555847167969]}, "content": {"uri": "Block_L22_308.b3dm"}, "geometricError": 0.015507353469729424, "refine": "REPLACE"}], "content": {"uri": "Block_L21_255.b3dm"}, "geometricError": 0.031117042526602745, "refine": "REPLACE"}], "content": {"uri": "Block_L20_142.b3dm"}, "geometricError": 0.06163929030299187, "refine": "REPLACE"}], "content": {"uri": "Block_L19_76.b3dm"}, "geometricError": 0.11932467669248581, "refine": "REPLACE"}], "content": {"uri": "Block_L18_40.b3dm"}, "geometricError": 0.2100631594657898, "refine": "REPLACE"}}