{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.138121098279953, "root": {"boundingVolume": {"box": [11.402410507202148, -53.96720504760742, -57.91555404663086, 9.741247177124023, 0.0, 0.0, 0.0, 18.348918914794922, 0.0, 0.0, 0.0, 14.007938385009766]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -60.082950592041016, -54.33702850341797, 9.741247177124023, 0.0, 0.0, 0.0, 12.233173370361328, 0.0, 0.0, 0.0, 10.429412841796875]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -60.082950592041016, -54.33702850341797, 9.741247177124023, 0.0, 0.0, 0.0, 12.233173370361328, 0.0, 0.0, 0.0, 10.429412841796875]}, "children": [{"boundingVolume": {"box": [16.819091796875, -64.15843200683594, -48.65081787109375, 4.324565410614014, 0.0, 0.0, 0.0, 8.154327392578125, 0.0, 0.0, 0.0, 4.743202209472656]}, "content": {"uri": "Block_L21_69.b3dm"}, "geometricError": 0.023729657754302025, "refine": "REPLACE"}, {"boundingVolume": {"box": [11.402410507202148, -51.92694091796875, -54.33702850341797, 9.741247177124023, 0.0, 0.0, 0.0, 4.0771636962890625, 0.0, 0.0, 0.0, 10.429412841796875]}, "content": {"uri": "Block_L22_421.b3dm"}, "geometricError": 0.019806545227766037, "refine": "REPLACE"}], "content": {"uri": "Block_L21_313.b3dm"}, "geometricError": 0.042049773037433624, "refine": "REPLACE"}], "content": {"uri": "Block_L20_171.b3dm"}, "geometricError": 0.0841808021068573, "refine": "REPLACE"}, {"boundingVolume": {"box": [11.402410507202148, -41.734031677246094, -62.581077575683594, 9.741247177124023, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 9.33673095703125]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -41.734031677246094, -62.581077575683594, 9.741247177124023, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 9.33673095703125]}, "children": [{"boundingVolume": {"box": [6.531786918640137, -41.734031677246094, -62.224578857421875, 4.870623588562012, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 8.980232238769531]}, "content": {"uri": "Block_L22_420.b3dm"}, "geometricError": 0.01654021628201008, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.273033142089844, -41.734031677246094, -62.581077575683594, 4.870623588562012, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 9.33673095703125]}, "content": {"uri": "Block_L22_419.b3dm"}, "geometricError": 0.017350690439343452, "refine": "REPLACE"}], "content": {"uri": "Block_L21_312.b3dm"}, "geometricError": 0.03391491621732712, "refine": "REPLACE"}], "content": {"uri": "Block_L20_170.b3dm"}, "geometricError": 0.06797897070646286, "refine": "REPLACE"}, {"boundingVolume": {"box": [11.402410507202148, -41.734031677246094, -48.57598114013672, 9.741247177124023, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 4.668365478515625]}, "children": [{"boundingVolume": {"box": [6.531786918640137, -41.734031677246094, -48.57598114013672, 4.870623588562012, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 4.668365478515625]}, "children": [{"boundingVolume": {"box": [6.531786918640137, -44.79190444946289, -48.57598114013672, 4.870623588562012, 0.0, 0.0, 0.0, 3.057872772216797, 0.0, 0.0, 0.0, 4.668365478515625]}, "content": {"uri": "Block_L22_418.b3dm"}, "geometricError": 0.014305665157735348, "refine": "REPLACE"}, {"boundingVolume": {"box": [6.531786918640137, -38.6761589050293, -49.248687744140625, 4.870623588562012, 0.0, 0.0, 0.0, 3.057872772216797, 0.0, 0.0, 0.0, 3.9956588745117188]}, "content": {"uri": "Block_L22_417.b3dm"}, "geometricError": 0.014078747481107712, "refine": "REPLACE"}], "content": {"uri": "Block_L21_311.b3dm"}, "geometricError": 0.028591610491275787, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.273033142089844, -41.734031677246094, -49.333892822265625, 4.870623588562012, 0.0, 0.0, 0.0, 6.115745544433594, 0.0, 0.0, 0.0, 3.9104537963867188]}, "children": [{"boundingVolume": {"box": [16.273033142089844, -44.79190444946289, -49.333892822265625, 4.870623588562012, 0.0, 0.0, 0.0, 3.057872772216797, 0.0, 0.0, 0.0, 3.9104537963867188]}, "content": {"uri": "Block_L22_416.b3dm"}, "geometricError": 0.018628420308232307, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.273033142089844, -38.6761589050293, -49.81682586669922, 4.870623588562012, 0.0, 0.0, 0.0, 3.057872772216797, 0.0, 0.0, 0.0, 3.427522659301758]}, "content": {"uri": "Block_L22_415.b3dm"}, "geometricError": 0.015344436280429363, "refine": "REPLACE"}], "content": {"uri": "Block_L21_310.b3dm"}, "geometricError": 0.03373483940958977, "refine": "REPLACE"}], "content": {"uri": "Block_L20_169.b3dm"}, "geometricError": 0.06251467019319534, "refine": "REPLACE"}], "content": {"uri": "Block_L19_92.b3dm"}, "geometricError": 0.138121098279953, "refine": "REPLACE"}}