{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.20491427183151245, "root": {"boundingVolume": {"box": [11.402410507202148, 17.414936065673828, -67.2047119140625, 9.741247177124023, 0.0, 0.0, 0.0, 12.626957893371582, 0.0, 0.0, 0.0, 8.328306198120117]}, "children": [{"boundingVolume": {"box": [11.402410507202148, 12.153703689575195, -65.24910736083984, 9.741247177124023, 0.0, 0.0, 0.0, 7.365724563598633, 0.0, 0.0, 0.0, 6.372697830200195]}, "children": [{"boundingVolume": {"box": [11.402410507202148, 12.153703689575195, -65.24910736083984, 9.741247177124023, 0.0, 0.0, 0.0, 7.365724563598633, 0.0, 0.0, 0.0, 6.372697830200195]}, "children": [{"boundingVolume": {"box": [7.343557357788086, 12.153703689575195, -65.24910736083984, 5.682394027709961, 0.0, 0.0, 0.0, 7.365724563598633, 0.0, 0.0, 0.0, 6.372697830200195]}, "children": [{"boundingVolume": {"box": [7.343557357788086, 10.312273025512695, -65.24909973144531, 5.682394027709961, 0.0, 0.0, 0.0, 5.524293422698975, 0.0, 0.0, 0.0, 6.37269401550293]}, "content": {"uri": "Block_L22_344.b3dm"}, "geometricError": 0.013199463486671448, "refine": "REPLACE"}, {"boundingVolume": {"box": [7.343557357788086, 17.677997589111328, -65.21453857421875, 5.682394027709961, 0.0, 0.0, 0.0, 1.8414311408996582, 0.0, 0.0, 0.0, 6.338132858276367]}, "content": {"uri": "Block_L22_343.b3dm"}, "geometricError": 0.01258153934031725, "refine": "REPLACE"}], "content": {"uri": "Block_L21_272.b3dm"}, "geometricError": 0.02548961713910103, "refine": "REPLACE"}, {"boundingVolume": {"box": [17.08480453491211, 12.153703689575195, -63.567237854003906, 4.0588531494140625, 0.0, 0.0, 0.0, 7.365724563598633, 0.0, 0.0, 0.0, 4.690828323364258]}, "children": [{"boundingVolume": {"box": [17.08480453491211, 10.312273025512695, -63.26899719238281, 4.0588531494140625, 0.0, 0.0, 0.0, 5.524293422698975, 0.0, 0.0, 0.0, 4.392587661743164]}, "content": {"uri": "Block_L22_342.b3dm"}, "geometricError": 0.011791792698204517, "refine": "REPLACE"}, {"boundingVolume": {"box": [17.08480453491211, 17.677997589111328, -63.567237854003906, 4.0588531494140625, 0.0, 0.0, 0.0, 1.8414311408996582, 0.0, 0.0, 0.0, 4.690828323364258]}, "content": {"uri": "Block_L22_341.b3dm"}, "geometricError": 0.012521005235612392, "refine": "REPLACE"}], "content": {"uri": "Block_L21_271.b3dm"}, "geometricError": 0.024534933269023895, "refine": "REPLACE"}], "content": {"uri": "Block_L20_150.b3dm"}, "geometricError": 0.04998067766427994, "refine": "REPLACE"}], "content": {"uri": "Block_L19_80.b3dm"}, "geometricError": 0.0998295396566391, "refine": "REPLACE"}, {"boundingVolume": {"box": [11.402410507202148, 24.780662536621094, -67.2047119140625, 9.741247177124023, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 8.328306198120117]}, "children": [{"boundingVolume": {"box": [7.343557357788086, 24.780662536621094, -66.78601837158203, 5.682394027709961, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 7.909608840942383]}, "children": [{"boundingVolume": {"box": [7.343557357788086, 24.780662536621094, -71.23905944824219, 5.682394027709961, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 3.2533302307128906]}, "children": [{"boundingVolume": {"box": [4.5023603439331055, 24.780662536621094, -70.34803771972656, 2.8411970138549805, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 2.3623085021972656]}, "content": {"uri": "Block_L22_340.b3dm"}, "geometricError": 0.014725256711244583, "refine": "REPLACE"}, {"boundingVolume": {"box": [10.184754371643066, 24.780662536621094, -71.23905944824219, 2.8411970138549805, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 3.2533302307128906]}, "content": {"uri": "Block_L22_339.b3dm"}, "geometricError": 0.013624792918562889, "refine": "REPLACE"}], "content": {"uri": "Block_L21_270.b3dm"}, "geometricError": 0.02812848798930645, "refine": "REPLACE"}, {"boundingVolume": {"box": [7.343557357788086, 24.780662536621094, -63.431068420410156, 5.682394027709961, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 4.554658889770508]}, "children": [{"boundingVolume": {"box": [4.975893020629883, 24.780662536621094, -63.431068420410156, 3.314729690551758, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 4.554658889770508]}, "content": {"uri": "Block_L22_338.b3dm"}, "geometricError": 0.012645029462873936, "refine": "REPLACE"}, {"boundingVolume": {"box": [10.658287048339844, 22.058971405029297, -63.431068420410156, 2.367664337158203, 0.0, 0.0, 0.0, 2.5395421981811523, 0.0, 0.0, 0.0, 4.554658889770508]}, "content": {"uri": "Block_L22_337.b3dm"}, "geometricError": 0.012402517721056938, "refine": "REPLACE"}], "content": {"uri": "Block_L21_269.b3dm"}, "geometricError": 0.025027776136994362, "refine": "REPLACE"}], "content": {"uri": "Block_L20_149.b3dm"}, "geometricError": 0.05340202897787094, "refine": "REPLACE"}, {"boundingVolume": {"box": [17.08480453491211, 24.780662536621094, -67.1972427368164, 4.0588531494140625, 0.0, 0.0, 0.0, 5.261233329772949, 0.0, 0.0, 0.0, 8.320833206176758]}, "children": [{"boundingVolume": {"box": [17.08480453491211, 25.722301483154297, -72.03861999511719, 4.0588531494140625, 0.0, 0.0, 0.0, 4.31959342956543, 0.0, 0.0, 0.0, 3.4637413024902344]}, "children": [{"boundingVolume": {"box": [17.08480453491211, 23.924388885498047, -70.55526733398438, 4.0588531494140625, 0.0, 0.0, 0.0, 2.5189733505249023, 0.0, 0.0, 0.0, 1.9803886413574219]}, "content": {"uri": "Block_L22_336.b3dm"}, "geometricError": 0.01282199751585722, "refine": "REPLACE"}, {"boundingVolume": {"box": [17.08480453491211, 28.24262809753418, -72.03861999511719, 4.0588531494140625, 0.0, 0.0, 0.0, 1.7992668151855469, 0.0, 0.0, 0.0, 3.4637413024902344]}, "content": {"uri": "Block_L22_335.b3dm"}, "geometricError": 0.012924464419484138, "refine": "REPLACE"}], "content": {"uri": "Block_L21_268.b3dm"}, "geometricError": 0.025759108364582062, "refine": "REPLACE"}, {"boundingVolume": {"box": [17.08480453491211, 24.476001739501953, -63.72563934326172, 4.0588531494140625, 0.0, 0.0, 0.0, 4.956572532653809, 0.0, 0.0, 0.0, 4.849233627319336]}, "children": [{"boundingVolume": {"box": [17.08480453491211, 21.170467376708984, -63.72563934326172, 4.0588531494140625, 0.0, 0.0, 0.0, 1.6510381698608398, 0.0, 0.0, 0.0, 4.849233627319336]}, "content": {"uri": "Block_L22_334.b3dm"}, "geometricError": 0.01229731272906065, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.775226593017578, 26.123580932617188, -67.36842346191406, 3.7492761611938477, 0.0, 0.0, 0.0, 3.3020763397216797, 0.0, 0.0, 0.0, 1.206451416015625]}, "content": {"uri": "Block_L22_333.b3dm"}, "geometricError": 0.012168156914412975, "refine": "REPLACE"}], "content": {"uri": "Block_L21_267.b3dm"}, "geometricError": 0.024525649845600128, "refine": "REPLACE"}], "content": {"uri": "Block_L20_148.b3dm"}, "geometricError": 0.05026552081108093, "refine": "REPLACE"}], "content": {"uri": "Block_L19_79.b3dm"}, "geometricError": 0.10343881696462631, "refine": "REPLACE"}], "content": {"uri": "Block_L18_41.b3dm"}, "geometricError": 0.20491427183151245, "refine": "REPLACE"}}