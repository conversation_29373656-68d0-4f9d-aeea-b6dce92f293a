# Test with correct database credentials
Write-Host "MTDT Database Test with Correct Credentials" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Step 2: Test container connection with correct credentials
Write-Host ""
Write-Host "Step 2: Test container connection (postgres host)..." -ForegroundColor Yellow

$containerConnectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "testUser"
    password = "1234"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $containerConnectionData
    Write-Host "Container connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
} catch {
    Write-Host "Container connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Test host connection with correct credentials
Write-Host ""
Write-Host "Step 3: Test host connection (host.docker.internal)..." -ForegroundColor Yellow

$hostConnectionData = @{
    host = "host.docker.internal"
    port = 5432
    database = "webgis_db"
    username = "testUser"
    password = "1234"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $hostConnectionData
    Write-Host "Host connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
} catch {
    Write-Host "Host connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "This is normal if PostgreSQL is not installed on the host machine" -ForegroundColor Yellow
}

# Step 4: Get tables from container connection
Write-Host ""
Write-Host "Step 4: Get tables from container connection..." -ForegroundColor Yellow

try {
    $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $containerConnectionData
    Write-Host "Successfully retrieved $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    
    if ($tablesResponse.Count -gt 0) {
        Write-Host ""
        Write-Host "Tables found:" -ForegroundColor Cyan
        $tablesResponse | ForEach-Object {
            Write-Host "- $($_.schema).$($_.name) ($($_.type))" -ForegroundColor White
            if ($_.geometryColumns -and $_.geometryColumns.Count -gt 0) {
                $geomCols = $_.geometryColumns | ForEach-Object { $_.columnName }
                Write-Host "  Geometry columns: $($geomCols -join ', ')" -ForegroundColor Gray
            }
            if ($_.totalRows -ne $null) {
                Write-Host "  Total rows: $($_.totalRows)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "No tables with geometry columns found" -ForegroundColor Yellow
        Write-Host "This is normal for a new database" -ForegroundColor Gray
    }
} catch {
    Write-Host "Get tables failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Test completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Database credentials confirmed:" -ForegroundColor Cyan
Write-Host "- Username: testUser" -ForegroundColor White
Write-Host "- Password: 1234" -ForegroundColor White
Write-Host "- Database: webgis_db" -ForegroundColor White
Write-Host ""
Write-Host "You can now use the database layer functionality in MTDT" -ForegroundColor Cyan
