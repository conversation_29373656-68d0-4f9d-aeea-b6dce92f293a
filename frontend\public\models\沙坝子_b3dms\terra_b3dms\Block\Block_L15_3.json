{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.4695773124694824, "root": {"boundingVolume": {"box": [-76.07044219970703, 274.2279052734375, -51.631568908691406, 77.73160552978516, 0.0, 0.0, 0.0, 90.4830322265625, 0.0, 0.0, 0.0, 22.519811630249023]}, "children": [{"boundingVolume": {"box": [-101.11781311035156, 206.36563110351562, -51.57619094848633, 51.401947021484375, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 21.13283920288086]}, "children": [{"boundingVolume": {"box": [-101.11781311035156, 206.36563110351562, -51.57619094848633, 51.401947021484375, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 21.13283920288086]}, "content": {"uri": "Block_L18_50.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_22.b3dm"}, "geometricError": 0.6999914646148682, "refine": "REPLACE"}, {"boundingVolume": {"box": [-24.02735137939453, 206.36563110351562, -58.82910919189453, 25.688514709472656, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 15.322271347045898]}, "children": [{"boundingVolume": {"box": [-34.730899810791016, 206.36563110351562, -60.45088195800781, 14.984966278076172, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 13.70050048828125]}, "content": {"uri": "Block_L18_49.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.04238510131836, 206.36563110351562, -57.90403747558594, 10.703548431396484, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 14.397195816040039]}, "content": {"uri": "Block_L18_48.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_21.b3dm"}, "geometricError": 0.4199979305267334, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.84309387207031, 296.8486633300781, -54.7438850402832, 62.50425720214844, 0.0, 0.0, 0.0, 67.86227416992188, 0.0, 0.0, 0.0, 17.164966583251953]}, "children": [{"boundingVolume": {"box": [-60.84309387207031, 296.8486633300781, -54.7438850402832, 62.50425720214844, 0.0, 0.0, 0.0, 67.86227416992188, 0.0, 0.0, 0.0, 17.164966583251953]}, "children": [{"boundingVolume": {"box": [-60.84309387207031, 251.60714721679688, -54.7438850402832, 62.50425720214844, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 17.164966583251953]}, "content": {"uri": "Block_L18_16.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-56.67886734008789, 319.46942138671875, -56.41402816772461, 58.340030670166016, 0.0, 0.0, 0.0, 45.24151611328125, 0.0, 0.0, 0.0, 14.262012481689453]}, "children": [{"boundingVolume": {"box": [-56.67886734008789, 319.46942138671875, -56.41402816772461, 58.340030670166016, 0.0, 0.0, 0.0, 45.24151611328125, 0.0, 0.0, 0.0, 14.262012481689453]}, "children": [{"boundingVolume": {"box": [-80.98721313476562, 319.46942138671875, -59.96236801147461, 34.03168487548828, 0.0, 0.0, 0.0, 45.24151611328125, 0.0, 0.0, 0.0, 10.608287811279297]}, "children": [{"boundingVolume": {"box": [-80.98721313476562, 289.30841064453125, -59.997711181640625, 34.03168487548828, 0.0, 0.0, 0.0, 15.08050537109375, 0.0, 0.0, 0.0, 10.526779174804688]}, "content": {"uri": "Block_L20_20.b3dm"}, "geometricError": 0.049997031688690186, "refine": "REPLACE"}, {"boundingVolume": {"box": [-76.63328552246094, 334.5499267578125, -59.1848030090332, 29.677757263183594, 0.0, 0.0, 0.0, 30.1610107421875, 0.0, 0.0, 0.0, 9.594440460205078]}, "content": {"uri": "Block_L20_19.b3dm"}, "geometricError": 0.05798071622848511, "refine": "REPLACE"}], "content": {"uri": "Block_L19_10.b3dm"}, "geometricError": 0.10484258085489273, "refine": "REPLACE"}, {"boundingVolume": {"box": [-22.64718246459961, 301.00775146484375, -53.79483413696289, 24.308345794677734, 0.0, 0.0, 0.0, 26.779861450195312, 0.0, 0.0, 0.0, 11.642818450927734]}, "children": [{"boundingVolume": {"box": [-22.64718246459961, 285.3851623535156, -53.77745819091797, 24.308345794677734, 0.0, 0.0, 0.0, 11.157257080078125, 0.0, 0.0, 0.0, 11.623785018920898]}, "content": {"uri": "Block_L21_70.b3dm"}, "geometricError": 0.04364462569355965, "refine": "REPLACE"}, {"boundingVolume": {"box": [-22.64718246459961, 312.1625671386719, -54.803314208984375, 24.308345794677734, 0.0, 0.0, 0.0, 15.620147705078125, 0.0, 0.0, 0.0, 8.02227783203125]}, "content": {"uri": "Block_L20_18.b3dm"}, "geometricError": 0.049461618065834045, "refine": "REPLACE"}], "content": {"uri": "Block_L19_9.b3dm"}, "geometricError": 0.09174033254384995, "refine": "REPLACE"}], "content": {"uri": "Block_L18_5.b3dm"}, "geometricError": 0.19590799510478973, "refine": "REPLACE"}], "content": {"uri": "Block_L17_2.b3dm"}, "geometricError": 0.39360862970352173, "refine": "REPLACE"}], "content": {"uri": "Block_L17_7.b3dm"}, "geometricError": 0.6020102500915527, "refine": "REPLACE"}], "content": {"uri": "Block_L16_5.b3dm"}, "geometricError": 1.2246477603912354, "refine": "REPLACE"}], "content": {"uri": "Block_L15_3.b3dm"}, "geometricError": 1.4695773124694824, "refine": "REPLACE"}}