import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface LoadingState {
  id: string
  message: string
  progress?: number
}

export interface NotificationState {
  id: string
  type: 'success' | 'warning' | 'error' | 'info'
  title: string
  message: string
  duration?: number
  timestamp: number
}

export const useUIStore = defineStore('ui', () => {
  // 侧边栏状态
  const sidebarCollapsed = ref(false)
  
  // 面板状态
  const activePanels = ref<string[]>([])
  
  // 工具栏状态
  const activeTools = ref<string[]>([])

  // 加载状态管理
  const loadingStates = ref<Map<string, LoadingState>>(new Map())
  const globalLoading = ref(false)

  // 通知状态管理
  const notifications = ref<NotificationState[]>([])

  // 性能监控
  const performanceMetrics = ref({
    lastRenderTime: 0,
    averageRenderTime: 0,
    memoryUsage: 0
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 切换面板
  const togglePanel = (panelName: string) => {
    const index = activePanels.value.indexOf(panelName)
    if (index > -1) {
      activePanels.value.splice(index, 1)
    } else {
      activePanels.value.push(panelName)
    }
  }

  // 切换工具
  const toggleTool = (toolName: string) => {
    const index = activeTools.value.indexOf(toolName)
    if (index > -1) {
      activeTools.value.splice(index, 1)
    } else {
      activeTools.value.push(toolName)
    }
  }

  // 关闭面板
  const closePanel = (panelName: string) => {
    const index = activePanels.value.indexOf(panelName)
    if (index > -1) {
      activePanels.value.splice(index, 1)
    }
  }

  // 关闭工具
  const closeTool = (toolName: string) => {
    const index = activeTools.value.indexOf(toolName)
    if (index > -1) {
      activeTools.value.splice(index, 1)
    }
  }

  // 检查面板是否激活
  const isPanelActive = (panelName: string) => {
    return activePanels.value.includes(panelName)
  }

  // 检查工具是否激活
  const isToolActive = (toolName: string) => {
    return activeTools.value.includes(toolName)
  }

  // 加载状态管理
  const setLoading = (id: string, message: string, progress?: number) => {
    loadingStates.value.set(id, { id, message, progress })
    updateGlobalLoading()
  }

  const clearLoading = (id: string) => {
    loadingStates.value.delete(id)
    updateGlobalLoading()
  }

  const updateGlobalLoading = () => {
    globalLoading.value = loadingStates.value.size > 0
  }

  // 通知管理
  const addNotification = (notification: Omit<NotificationState, 'id' | 'timestamp'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newNotification: NotificationState = {
      ...notification,
      id,
      timestamp: Date.now(),
      duration: notification.duration || 3000
    }
    
    notifications.value.push(newNotification)
    
    // 自动移除通知
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
    
    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 性能监控
  const updatePerformanceMetrics = (renderTime: number) => {
    performanceMetrics.value.lastRenderTime = renderTime
    performanceMetrics.value.averageRenderTime = 
      (performanceMetrics.value.averageRenderTime + renderTime) / 2
    
    // 更新内存使用情况（如果可用）
    if ('memory' in performance) {
      performanceMetrics.value.memoryUsage = (performance as any).memory.usedJSHeapSize
    }
  }

  return {
    sidebarCollapsed,
    activePanels,
    activeTools,
    loadingStates,
    globalLoading,
    notifications,
    performanceMetrics,
    toggleSidebar,
    togglePanel,
    toggleTool,
    closePanel,
    closeTool,
    isPanelActive,
    isToolActive,
    setLoading,
    clearLoading,
    addNotification,
    removeNotification,
    updatePerformanceMetrics
  }
})
