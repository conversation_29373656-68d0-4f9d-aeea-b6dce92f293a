{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.799444854259491, "root": {"boundingVolume": {"box": [-42.87025451660156, -11.08987045288086, -35.414588928222656, 44.53141784667969, 0.0, 0.0, 0.0, 23.563289642333984, 0.0, 0.0, 0.0, 35.64833068847656]}, "children": [{"boundingVolume": {"box": [-59.89628219604492, -20.907909393310547, -41.33902359008789, 17.02602767944336, 0.0, 0.0, 0.0, 13.745251655578613, 0.0, 0.0, 0.0, 10.828472137451172]}, "children": [{"boundingVolume": {"box": [-59.89628219604492, -20.907909393310547, -41.30194091796875, 17.02602767944336, 0.0, 0.0, 0.0, 13.745251655578613, 0.0, 0.0, 0.0, 10.791387557983398]}, "content": {"uri": "Block_L18_96.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_50.b3dm"}, "geometricError": 0.40817761421203613, "refine": "REPLACE"}, {"boundingVolume": {"box": [-65.1359634399414, 2.655381202697754, -45.677188873291016, 22.265708923339844, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 15.166637420654297]}, "children": [{"boundingVolume": {"box": [-74.41334533691406, 2.655381202697754, -38.388343811035156, 12.988330841064453, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 7.87779426574707]}, "content": {"uri": "Block_L18_95.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-52.14763259887695, 2.655381202697754, -46.21233367919922, 9.27737808227539, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 14.576817512512207]}, "children": [{"boundingVolume": {"box": [-52.14763259887695, 2.655381202697754, -52.291900634765625, 9.27737808227539, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 8.497251510620117]}, "children": [{"boundingVolume": {"box": [-52.14763259887695, 2.655381202697754, -52.291900634765625, 9.27737808227539, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 8.497251510620117]}, "children": [{"boundingVolume": {"box": [-52.14763259887695, -2.2536377906799316, -52.134071350097656, 9.27737808227539, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 8.339424133300781]}, "children": [{"boundingVolume": {"box": [-55.240089416503906, -2.2536377906799316, -47.896018981933594, 6.184919357299805, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 4.101373672485352]}, "content": {"uri": "Block_L22_1039.b3dm"}, "geometricError": 0.014511313289403915, "refine": "REPLACE"}, {"boundingVolume": {"box": [-45.96271514892578, -2.2536377906799316, -55.60883331298828, 3.092458724975586, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 4.864664077758789]}, "content": {"uri": "Block_L22_1038.b3dm"}, "geometricError": 0.013517963699996471, "refine": "REPLACE"}, {"boundingVolume": {"box": [-45.96271514892578, -4.708147048950195, -47.2694091796875, 3.092458724975586, 0.0, 0.0, 0.0, 2.4545094966888428, 0.0, 0.0, 0.0, 3.474760055541992]}, "content": {"uri": "Block_L22_1037.b3dm"}, "geometricError": 0.013960466720163822, "refine": "REPLACE"}, {"boundingVolume": {"box": [-45.96271514892578, 0.20087170600891113, -47.2694091796875, 3.092458724975586, 0.0, 0.0, 0.0, 2.4545094966888428, 0.0, 0.0, 0.0, 3.474760055541992]}, "content": {"uri": "Block_L22_1036.b3dm"}, "geometricError": 0.01293179765343666, "refine": "REPLACE"}], "content": {"uri": "Block_L21_621.b3dm"}, "geometricError": 0.02739649824798107, "refine": "REPLACE"}, {"boundingVolume": {"box": [-52.14763259887695, 7.5644001960754395, -52.291900634765625, 9.27737808227539, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 8.497251510620117]}, "children": [{"boundingVolume": {"box": [-56.013206481933594, 7.5644001960754395, -48.20102310180664, 5.41180419921875, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 4.406375885009766]}, "content": {"uri": "Block_L22_1035.b3dm"}, "geometricError": 0.013962935656309128, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.7358283996582, 7.5644001960754395, -52.289222717285156, 3.8655738830566406, 0.0, 0.0, 0.0, 4.9090189933776855, 0.0, 0.0, 0.0, 8.494577407836914]}, "content": {"uri": "Block_L22_1034.b3dm"}, "geometricError": 0.012161280028522015, "refine": "REPLACE"}], "content": {"uri": "Block_L21_620.b3dm"}, "geometricError": 0.025603802874684334, "refine": "REPLACE"}], "content": {"uri": "Block_L20_336.b3dm"}, "geometricError": 0.0529496930539608, "refine": "REPLACE"}], "content": {"uri": "Block_L19_177.b3dm"}, "geometricError": 0.10622833669185638, "refine": "REPLACE"}, {"boundingVolume": {"box": [-52.14763259887695, 2.655381202697754, -37.719425201416016, 9.27737808227539, 0.0, 0.0, 0.0, 9.818037986755371, 0.0, 0.0, 0.0, 6.075222969055176]}, "content": {"uri": "Block_L19_176.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_94.b3dm"}, "geometricError": 0.20803317427635193, "refine": "REPLACE"}], "content": {"uri": "Block_L17_49.b3dm"}, "geometricError": 0.41203320026397705, "refine": "REPLACE"}, {"boundingVolume": {"box": [-65.1359634399414, -24.83512306213379, -15.399202346801758, 22.265708923339844, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 15.111349105834961]}, "children": [{"boundingVolume": {"box": [-76.26881408691406, -24.83512306213379, -14.126774787902832, 11.132854461669922, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 13.691849708557129]}, "content": {"uri": "Block_L18_93.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-54.003108978271484, -24.83512306213379, -22.517330169677734, 11.132854461669922, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 7.993220806121826]}, "content": {"uri": "Block_L18_92.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_48.b3dm"}, "geometricError": 0.3765495717525482, "refine": "REPLACE"}, {"boundingVolume": {"box": [-68.51286315917969, -1.2718334197998047, -15.13840389251709, 18.888809204101562, 0.0, 0.0, 0.0, 13.74525260925293, 0.0, 0.0, 0.0, 15.372147560119629]}, "children": [{"boundingVolume": {"box": [-68.51286315917969, -1.2718334197998047, -15.13840389251709, 18.888809204101562, 0.0, 0.0, 0.0, 13.74525260925293, 0.0, 0.0, 0.0, 15.372147560119629]}, "content": {"uri": "Block_L18_91.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_47.b3dm"}, "geometricError": 0.3606624901294708, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.73740005493164, -22.87151527404785, -47.18918991088867, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 19.004131317138672]}, "children": [{"boundingVolume": {"box": [-31.73740005493164, -22.87151527404785, -54.72392272949219, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 11.109048843383789]}, "content": {"uri": "Block_L18_90.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.73740005493164, -22.87151527404785, -35.89996337890625, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 7.714907646179199]}, "children": [{"boundingVolume": {"box": [-31.73740005493164, -28.76233673095703, -35.89996337890625, 11.132854461669922, 0.0, 0.0, 0.0, 5.890822410583496, 0.0, 0.0, 0.0, 7.714907646179199]}, "content": {"uri": "Block_L19_168.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-32.63252258300781, -16.980693817138672, -36.438575744628906, 10.23773193359375, 0.0, 0.0, 0.0, 5.890822410583496, 0.0, 0.0, 0.0, 7.176298141479492]}, "content": {"uri": "Block_L19_167.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_89.b3dm"}, "geometricError": 0.18727026879787445, "refine": "REPLACE"}], "content": {"uri": "Block_L17_46.b3dm"}, "geometricError": 0.3816657066345215, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.471691131591797, -22.87151527404785, -53.08958435058594, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 17.97333526611328]}, "children": [{"boundingVolume": {"box": [-9.471691131591797, -22.87151527404785, -53.08958435058594, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 17.97333526611328]}, "content": {"uri": "Block_L18_88.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_45.b3dm"}, "geometricError": 0.41803640127182007, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.73740005493164, 0.6917743682861328, -53.23210906982422, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 16.015039443969727]}, "children": [{"boundingVolume": {"box": [-31.73740005493164, 0.6917743682861328, -59.982398986816406, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 9.264747619628906]}, "content": {"uri": "Block_L18_87.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.73740005493164, 0.6917743682861328, -44.06397247314453, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 6.653678894042969]}, "content": {"uri": "Block_L19_243.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_44.b3dm"}, "geometricError": 0.37864041328430176, "refine": "REPLACE"}, {"boundingVolume": {"box": [-9.471691131591797, 0.6917743682861328, -58.505455017089844, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 12.275564193725586]}, "children": [{"boundingVolume": {"box": [-9.471691131591797, 0.6917743682861328, -58.505455017089844, 11.132854461669922, 0.0, 0.0, 0.0, 11.781644821166992, 0.0, 0.0, 0.0, 12.275564193725586]}, "content": {"uri": "Block_L18_86.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_43.b3dm"}, "geometricError": 0.42903247475624084, "refine": "REPLACE"}], "content": {"uri": "Block_L16_14.b3dm"}, "geometricError": 0.799444854259491, "refine": "REPLACE"}}