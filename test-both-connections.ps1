# Test both database connections
Write-Host "MTDT Database Connections Test" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Step 2: Test container connection with container credentials
Write-Host ""
Write-Host "Step 2: Test container connection (mtdt_user)..." -ForegroundColor Yellow

$containerConnectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $containerConnectionData
    Write-Host "Container connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
    
    # Get tables from container
    Write-Host "Getting tables from container database..." -ForegroundColor Gray
    try {
        $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $containerConnectionData
        Write-Host "Container database has $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    } catch {
        Write-Host "Failed to get tables from container: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Container connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Test host connection with host credentials
Write-Host ""
Write-Host "Step 3: Test host connection (testUser)..." -ForegroundColor Yellow

$hostConnectionData = @{
    host = "host.docker.internal"
    port = 5432
    database = "webgis_db"
    username = "testUser"
    password = "1234"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $hostConnectionData
    Write-Host "Host connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
    
    # Get tables from host
    Write-Host "Getting tables from host database..." -ForegroundColor Gray
    try {
        $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $hostConnectionData
        Write-Host "Host database has $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
        
        if ($tablesResponse.Count -gt 0) {
            Write-Host ""
            Write-Host "Tables in host database:" -ForegroundColor Cyan
            $tablesResponse | ForEach-Object {
                Write-Host "- $($_.schema).$($_.name) ($($_.type))" -ForegroundColor White
                if ($_.geometryColumns -and $_.geometryColumns.Count -gt 0) {
                    $geomCols = $_.geometryColumns | ForEach-Object { $_.columnName }
                    Write-Host "  Geometry columns: $($geomCols -join ', ')" -ForegroundColor Gray
                }
                if ($_.totalRows -ne $null) {
                    Write-Host "  Total rows: $($_.totalRows)" -ForegroundColor Gray
                }
            }
        }
    } catch {
        Write-Host "Failed to get tables from host: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Host connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Database Configurations:" -ForegroundColor Cyan
Write-Host "1. Container Database (postgres):" -ForegroundColor White
Write-Host "   - Username: mtdt_user" -ForegroundColor Gray
Write-Host "   - Password: mtdt_secure_password_2024" -ForegroundColor Gray
Write-Host "   - Use this for containerized deployment" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Host Database (host.docker.internal):" -ForegroundColor White
Write-Host "   - Username: testUser" -ForegroundColor Gray
Write-Host "   - Password: 1234" -ForegroundColor Gray
Write-Host "   - Use this to connect to your local PostgreSQL" -ForegroundColor Gray
Write-Host ""
Write-Host "In MTDT system, you can choose:" -ForegroundColor Cyan
Write-Host "- 'WebGIS主数据库(容器)' for container database" -ForegroundColor White
Write-Host "- 'WebGIS主数据库(宿主机)' for host database" -ForegroundColor White
