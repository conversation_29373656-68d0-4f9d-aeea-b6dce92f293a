# 外网访问环境变量配置
# 复制此文件为 .env 并修改相应配置

# 数据库配置
POSTGRES_DB=webgis_db
POSTGRES_USER=mtdt_user
POSTGRES_PASSWORD=StrongPassword123!@#  # 使用强密码

# Redis配置（暂时不使用密码）
# REDIS_PASSWORD=RedisPassword456!@#

# JWT配置
JWT_SECRET=your-super-strong-jwt-secret-key-for-production-change-this-immediately
JWT_EXPIRES_IN=24h  # 缩短过期时间

# 邮件配置 - 阿里云企业邮箱
MAIL_HOST=smtp.pfi.org.cn
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASS=Welcome@314

# 安全配置
CORS_ORIGIN=http://localhost:8080,http://*************:8080  # 允许的前端域名
NODE_ENV=production

# 应用配置
PORT=3000

# 日志配置
LOG_LEVEL=warn  # 生产环境减少日志输出
