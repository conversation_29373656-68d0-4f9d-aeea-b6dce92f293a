{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.2507933974266052, "root": {"boundingVolume": {"box": [67.90164184570312, -68.61257934570312, -64.17594909667969, 27.275493621826172, 0.0, 0.0, 0.0, 32.994293212890625, 0.0, 0.0, 0.0, 20.26833724975586]}, "children": [{"boundingVolume": {"box": [67.90164184570312, -82.36019897460938, -64.15750122070312, 27.275493621826172, 0.0, 0.0, 0.0, 19.24666976928711, 0.0, 0.0, 0.0, 20.24988555908203]}, "children": [{"boundingVolume": {"box": [54.26389694213867, -75.85273742675781, -60.232582092285156, 13.637744903564453, 0.0, 0.0, 0.0, 12.739204406738281, 0.0, 0.0, 0.0, 16.324966430664062]}, "children": [{"boundingVolume": {"box": [55.7679557800293, -73.19508361816406, -67.03465270996094, 12.133686065673828, 0.0, 0.0, 0.0, 10.081546783447266, 0.0, 0.0, 0.0, 9.522895812988281]}, "content": {"uri": "Block_L21_65.b3dm"}, "geometricError": 0.03442838788032532, "refine": "REPLACE"}, {"boundingVolume": {"box": [54.26389694213867, -75.84649658203125, -50.709686279296875, 13.637744903564453, 0.0, 0.0, 0.0, 12.732959747314453, 0.0, 0.0, 0.0, 6.802070617675781]}, "content": {"uri": "Block_L21_64.b3dm"}, "geometricError": 0.034071147441864014, "refine": "REPLACE"}], "content": {"uri": "Block_L20_50.b3dm"}, "geometricError": 0.06849668174982071, "refine": "REPLACE"}, {"boundingVolume": {"box": [81.53939056396484, -82.36019897460938, -64.13018798828125, 13.637748718261719, 0.0, 0.0, 0.0, 19.24666976928711, 0.0, 0.0, 0.0, 20.222572326660156]}, "children": [{"boundingVolume": {"box": [74.72052001953125, -75.21688842773438, -74.07766723632812, 6.818874359130859, 0.0, 0.0, 0.0, 12.103351593017578, 0.0, 0.0, 0.0, 9.948360443115234]}, "content": {"uri": "Block_L21_63.b3dm"}, "geometricError": 0.03840366750955582, "refine": "REPLACE"}, {"boundingVolume": {"box": [88.35826110839844, -75.7430419921875, -74.24015808105469, 6.818874359130859, 0.0, 0.0, 0.0, 12.629508972167969, 0.0, 0.0, 0.0, 10.110847473144531]}, "content": {"uri": "Block_L21_62.b3dm"}, "geometricError": 0.04171653836965561, "refine": "REPLACE"}, {"boundingVolume": {"box": [81.53939056396484, -87.2548828125, -54.018463134765625, 13.637748718261719, 0.0, 0.0, 0.0, 14.351993560791016, 0.0, 0.0, 0.0, 10.110847473144531]}, "content": {"uri": "Block_L21_61.b3dm"}, "geometricError": 0.042523931711912155, "refine": "REPLACE"}], "content": {"uri": "Block_L20_49.b3dm"}, "geometricError": 0.08191197365522385, "refine": "REPLACE"}], "content": {"uri": "Block_L19_25.b3dm"}, "geometricError": 0.15073886513710022, "refine": "REPLACE"}, {"boundingVolume": {"box": [51.99094009399414, -49.365909576416016, -64.99085998535156, 11.364788055419922, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 16.331632614135742]}, "children": [{"boundingVolume": {"box": [55.271759033203125, -49.365909576416016, -73.0635986328125, 8.083967208862305, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 8.129779815673828]}, "children": [{"boundingVolume": {"box": [55.33203887939453, -55.013389587402344, -73.05828857421875, 8.023691177368164, 0.0, 0.0, 0.0, 7.98621940612793, 0.0, 0.0, 0.0, 8.124469757080078]}, "content": {"uri": "Block_L21_60.b3dm"}, "geometricError": 0.028392845764756203, "refine": "REPLACE"}, {"boundingVolume": {"box": [56.36988067626953, -41.322731018066406, -72.95513916015625, 6.985849380493164, 0.0, 0.0, 0.0, 5.704442977905273, 0.0, 0.0, 0.0, 8.021320343017578]}, "content": {"uri": "Block_L21_59.b3dm"}, "geometricError": 0.022965561598539352, "refine": "REPLACE"}], "content": {"uri": "Block_L20_48.b3dm"}, "geometricError": 0.04912011697888374, "refine": "REPLACE"}, {"boundingVolume": {"box": [51.99094009399414, -49.365909576416016, -56.800392150878906, 11.364788055419922, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 8.133432388305664]}, "children": [{"boundingVolume": {"box": [51.99094009399414, -55.094085693359375, -56.809356689453125, 11.364788055419922, 0.0, 0.0, 0.0, 8.019447326660156, 0.0, 0.0, 0.0, 8.124465942382812]}, "content": {"uri": "Block_L21_58.b3dm"}, "geometricError": 0.0276313629001379, "refine": "REPLACE"}, {"boundingVolume": {"box": [44.49802017211914, -41.34646224975586, -57.39466857910156, 3.871868133544922, 0.0, 0.0, 0.0, 5.728176116943359, 0.0, 0.0, 0.0, 6.63776969909668]}, "content": {"uri": "Block_L21_57.b3dm"}, "geometricError": 0.02228996902704239, "refine": "REPLACE"}, {"boundingVolume": {"box": [53.790504455566406, -41.34646224975586, -60.910057067871094, 5.420614242553711, 0.0, 0.0, 0.0, 5.728176116943359, 0.0, 0.0, 0.0, 4.023765563964844]}, "content": {"uri": "Block_L22_410.b3dm"}, "geometricError": 0.021647538989782333, "refine": "REPLACE"}], "content": {"uri": "Block_L20_47.b3dm"}, "geometricError": 0.049159836024045944, "refine": "REPLACE"}], "content": {"uri": "Block_L19_24.b3dm"}, "geometricError": 0.09841926395893097, "refine": "REPLACE"}, {"boundingVolume": {"box": [79.26643371582031, -49.365909576416016, -73.73799133300781, 15.91070556640625, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 10.706298828125]}, "children": [{"boundingVolume": {"box": [79.26643371582031, -49.365909576416016, -73.75589752197266, 15.91070556640625, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 10.688392639160156]}, "children": [{"boundingVolume": {"box": [69.98518371582031, -56.239723205566406, -72.83991241455078, 6.629459381103516, 0.0, 0.0, 0.0, 6.873811721801758, 0.0, 0.0, 0.0, 9.772407531738281]}, "content": {"uri": "Block_L21_56.b3dm"}, "geometricError": 0.03178016096353531, "refine": "REPLACE"}, {"boundingVolume": {"box": [69.98518371582031, -42.492095947265625, -74.9632568359375, 6.629459381103516, 0.0, 0.0, 0.0, 6.873811721801758, 0.0, 0.0, 0.0, 8.381851196289062]}, "content": {"uri": "Block_L21_55.b3dm"}, "geometricError": 0.02627578191459179, "refine": "REPLACE"}, {"boundingVolume": {"box": [85.89588928222656, -49.365909576416016, -78.4663314819336, 9.281246185302734, 0.0, 0.0, 0.0, 13.747623443603516, 0.0, 0.0, 0.0, 5.977958679199219]}, "content": {"uri": "Block_L21_54.b3dm"}, "geometricError": 0.03180660679936409, "refine": "REPLACE"}], "content": {"uri": "Block_L20_46.b3dm"}, "geometricError": 0.06067774072289467, "refine": "REPLACE"}], "content": {"uri": "Block_L19_23.b3dm"}, "geometricError": 0.1215384304523468, "refine": "REPLACE"}], "content": {"uri": "Block_L18_14.b3dm"}, "geometricError": 0.2507933974266052, "refine": "REPLACE"}}