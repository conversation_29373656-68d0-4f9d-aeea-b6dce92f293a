{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.10049557685852051, "root": {"boundingVolume": {"box": [-29.88192367553711, 44.452171325683594, -57.16753387451172, 12.988329887390137, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 11.837911605834961]}, "children": [{"boundingVolume": {"box": [-33.129005432128906, 44.452171325683594, -56.43231201171875, 9.741247177124023, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 11.102685928344727]}, "children": [{"boundingVolume": {"box": [-37.999629974365234, 44.452171325683594, -64.31812286376953, 4.870624542236328, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 2.2009124755859375]}, "children": [{"boundingVolume": {"box": [-37.999629974365234, 39.54315185546875, -64.31959533691406, 4.870624542236328, 0.0, 0.0, 0.0, 3.506441116333008, 0.0, 0.0, 0.0, 2.199443817138672]}, "content": {"uri": "Block_L22_797.b3dm"}, "geometricError": 0.014824609272181988, "refine": "REPLACE"}, {"boundingVolume": {"box": [-37.999629974365234, 47.958614349365234, -64.32102966308594, 4.870624542236328, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 2.0755977630615234]}, "content": {"uri": "Block_L22_796.b3dm"}, "geometricError": 0.015086495317518711, "refine": "REPLACE"}], "content": {"uri": "Block_L21_499.b3dm"}, "geometricError": 0.029854806140065193, "refine": "REPLACE"}, {"boundingVolume": {"box": [-28.25838279724121, 44.452171325683594, -64.77757263183594, 4.870622634887695, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 2.739490509033203]}, "children": [{"boundingVolume": {"box": [-28.25838279724121, 39.54315185546875, -64.2770004272461, 4.870622634887695, 0.0, 0.0, 0.0, 3.506441116333008, 0.0, 0.0, 0.0, 2.2389144897460938]}, "content": {"uri": "Block_L22_795.b3dm"}, "geometricError": 0.014485142193734646, "refine": "REPLACE"}, {"boundingVolume": {"box": [-28.25838279724121, 47.958614349365234, -64.82453155517578, 4.870622634887695, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 2.692537307739258]}, "content": {"uri": "Block_L22_794.b3dm"}, "geometricError": 0.01454642042517662, "refine": "REPLACE"}], "content": {"uri": "Block_L21_498.b3dm"}, "geometricError": 0.029060112312436104, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.60052490234375, 44.83108901977539, -53.50141143798828, 8.212764739990234, 0.0, 0.0, 0.0, 8.036544799804688, 0.0, 0.0, 0.0, 8.171789169311523]}, "children": [{"boundingVolume": {"box": [-31.60052490234375, 44.83108901977539, -53.501670837402344, 8.212764739990234, 0.0, 0.0, 0.0, 8.036544799804688, 0.0, 0.0, 0.0, 8.171533584594727]}, "content": {"uri": "Block_L22_793.b3dm"}, "geometricError": 0.011953824199736118, "refine": "REPLACE"}], "content": {"uri": "Block_L21_497.b3dm"}, "geometricError": 0.023875677958130836, "refine": "REPLACE"}], "content": {"uri": "Block_L20_269.b3dm"}, "geometricError": 0.057461198419332504, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.14067840576172, 44.452171325683594, -62.41936492919922, 3.2470827102661133, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 6.5544586181640625]}, "children": [{"boundingVolume": {"box": [-20.14067840576172, 44.452171325683594, -62.38823699951172, 3.2470827102661133, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 6.5233306884765625]}, "children": [{"boundingVolume": {"box": [-20.14067840576172, 40.244441986083984, -62.16779327392578, 3.2470827102661133, 0.0, 0.0, 0.0, 4.207729339599609, 0.0, 0.0, 0.0, 6.302886962890625]}, "content": {"uri": "Block_L22_792.b3dm"}, "geometricError": 0.013127410784363747, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.14067840576172, 48.65990447998047, -62.31650924682617, 3.2470827102661133, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 6.451602935791016]}, "content": {"uri": "Block_L22_791.b3dm"}, "geometricError": 0.01341879740357399, "refine": "REPLACE"}], "content": {"uri": "Block_L21_496.b3dm"}, "geometricError": 0.0264888983219862, "refine": "REPLACE"}], "content": {"uri": "Block_L20_268.b3dm"}, "geometricError": 0.052837785333395004, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.14067840576172, 44.452171325683594, -51.256614685058594, 3.2470827102661133, 0.0, 0.0, 0.0, 8.415460586547852, 0.0, 0.0, 0.0, 4.60828971862793]}, "children": [{"boundingVolume": {"box": [-19.964067459106445, 40.244441986083984, -51.264076232910156, 3.0704727172851562, 0.0, 0.0, 0.0, 4.207729339599609, 0.0, 0.0, 0.0, 4.600828170776367]}, "children": [{"boundingVolume": {"box": [-19.964067459106445, 40.244441986083984, -53.564491271972656, 3.0704727172851562, 0.0, 0.0, 0.0, 4.207729339599609, 0.0, 0.0, 0.0, 2.3004150390625]}, "content": {"uri": "Block_L22_790.b3dm"}, "geometricError": 0.011588252149522305, "refine": "REPLACE"}, {"boundingVolume": {"box": [-19.39580535888672, 40.669647216796875, -48.963661193847656, 2.5022096633911133, 0.0, 0.0, 0.0, 3.782522201538086, 0.0, 0.0, 0.0, 2.300413131713867]}, "content": {"uri": "Block_L23_766.b3dm"}, "geometricError": 0.010426102206110954, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1578.b3dm"}, "geometricError": 0.02198905497789383, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.14067840576172, 48.65990447998047, -51.256614685058594, 3.2470827102661133, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 4.60828971862793]}, "children": [{"boundingVolume": {"box": [-20.14067840576172, 48.65990447998047, -53.560760498046875, 3.2470827102661133, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 2.3041458129882812]}, "content": {"uri": "Block_L22_789.b3dm"}, "geometricError": 0.011165534146130085, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.14067840576172, 48.65990447998047, -48.95246887207031, 3.2470827102661133, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 2.3041439056396484]}, "content": {"uri": "Block_L23_765.b3dm"}, "geometricError": 0.010611973702907562, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1577.b3dm"}, "geometricError": 0.021685317158699036, "refine": "REPLACE"}], "content": {"uri": "Block_L21_894.b3dm"}, "geometricError": 0.043765708804130554, "refine": "REPLACE"}], "content": {"uri": "Block_L19_141.b3dm"}, "geometricError": 0.10049557685852051, "refine": "REPLACE"}}