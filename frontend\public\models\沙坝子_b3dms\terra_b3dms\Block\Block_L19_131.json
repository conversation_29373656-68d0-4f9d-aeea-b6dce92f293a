{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.08887282758951187, "root": {"boundingVolume": {"box": [-137.26759338378906, 112.89590454101562, -43.22502136230469, 11.364791870117188, 0.0, 0.0, 0.0, 10.52695083618164, 0.0, 0.0, 0.0, 9.076683044433594]}, "children": [{"boundingVolume": {"box": [-142.0029296875, 114.33185577392578, -39.49678421020508, 6.629463195800781, 0.0, 0.0, 0.0, 9.09100341796875, 0.0, 0.0, 0.0, 5.348445892333984]}, "children": [{"boundingVolume": {"box": [-141.89859008789062, 110.55624389648438, -39.16217803955078, 6.5251312255859375, 0.0, 0.0, 0.0, 5.298679351806641, 0.0, 0.0, 0.0, 5.01384162902832]}, "children": [{"boundingVolume": {"box": [-144.61740112304688, 110.55735778808594, -35.84642028808594, 3.8063278198242188, 0.0, 0.0, 0.0, 5.2975616455078125, 0.0, 0.0, 0.0, 1.6980838775634766]}, "content": {"uri": "Block_L23_711.b3dm"}, "geometricError": 0.010095701552927494, "refine": "REPLACE"}, {"boundingVolume": {"box": [-138.09225463867188, 110.84844970703125, -39.16217803955078, 2.7188034057617188, 0.0, 0.0, 0.0, 5.006465911865234, 0.0, 0.0, 0.0, 5.01384162902832]}, "content": {"uri": "Block_L23_710.b3dm"}, "geometricError": 0.010778836905956268, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1548.b3dm"}, "geometricError": 0.02089126780629158, "refine": "REPLACE"}, {"boundingVolume": {"box": [-142.0029296875, 119.63888549804688, -39.49678421020508, 6.629463195800781, 0.0, 0.0, 0.0, 3.7839698791503906, 0.0, 0.0, 0.0, 5.348445892333984]}, "children": [{"boundingVolume": {"box": [-145.31765747070312, 119.63888549804688, -39.49678421020508, 3.314727783203125, 0.0, 0.0, 0.0, 3.7839698791503906, 0.0, 0.0, 0.0, 5.348445892333984]}, "content": {"uri": "Block_L23_709.b3dm"}, "geometricError": 0.01025539543479681, "refine": "REPLACE"}, {"boundingVolume": {"box": [-138.68820190429688, 119.63888549804688, -39.393096923828125, 3.3147354125976562, 0.0, 0.0, 0.0, 3.7839698791503906, 0.0, 0.0, 0.0, 5.244760513305664]}, "content": {"uri": "Block_L23_708.b3dm"}, "geometricError": 0.010179293341934681, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1547.b3dm"}, "geometricError": 0.020539548248052597, "refine": "REPLACE"}], "content": {"uri": "Block_L21_880.b3dm"}, "geometricError": 0.04161560907959938, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 112.89590454101562, -43.22502136230469, 4.735328674316406, 0.0, 0.0, 0.0, 10.52695083618164, 0.0, 0.0, 0.0, 9.076683044433594]}, "children": [{"boundingVolume": {"box": [-130.63812255859375, 108.05711364746094, -44.867618560791016, 4.735328674316406, 0.0, 0.0, 0.0, 4.8387908935546875, 0.0, 0.0, 0.0, 4.413822174072266]}, "children": [{"boundingVolume": {"box": [-130.63812255859375, 108.0604248046875, -44.867618560791016, 4.735328674316406, 0.0, 0.0, 0.0, 4.835479736328125, 0.0, 0.0, 0.0, 4.413822174072266]}, "content": {"uri": "Block_L22_714.b3dm"}, "geometricError": 0.012271440587937832, "refine": "REPLACE"}], "content": {"uri": "Block_L21_459.b3dm"}, "geometricError": 0.024552224203944206, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 107.63243103027344, -37.30106735229492, 4.735328674316406, 0.0, 0.0, 0.0, 5.2634735107421875, 0.0, 0.0, 0.0, 3.152729034423828]}, "children": [{"boundingVolume": {"box": [-130.63812255859375, 105.43931579589844, -37.30106735229492, 4.735328674316406, 0.0, 0.0, 0.0, 3.0703582763671875, 0.0, 0.0, 0.0, 3.152729034423828]}, "content": {"uri": "Block_L22_713.b3dm"}, "geometricError": 0.011590089648962021, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 110.70278930664062, -37.30106735229492, 4.735328674316406, 0.0, 0.0, 0.0, 2.193115234375, 0.0, 0.0, 0.0, 3.152729034423828]}, "content": {"uri": "Block_L22_712.b3dm"}, "geometricError": 0.011345628648996353, "refine": "REPLACE"}], "content": {"uri": "Block_L21_458.b3dm"}, "geometricError": 0.022971881553530693, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 118.15937805175781, -43.22502136230469, 4.735328674316406, 0.0, 0.0, 0.0, 5.263477325439453, 0.0, 0.0, 0.0, 9.076683044433594]}, "children": [{"boundingVolume": {"box": [-130.63812255859375, 115.52764129638672, -47.763362884521484, 4.735328674316406, 0.0, 0.0, 0.0, 2.6317367553710938, 0.0, 0.0, 0.0, 4.538341522216797]}, "content": {"uri": "Block_L22_711.b3dm"}, "geometricError": 0.012123015709221363, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 120.79112243652344, -47.763362884521484, 4.735328674316406, 0.0, 0.0, 0.0, 2.6317405700683594, 0.0, 0.0, 0.0, 4.538341522216797]}, "content": {"uri": "Block_L22_710.b3dm"}, "geometricError": 0.011372108943760395, "refine": "REPLACE"}, {"boundingVolume": {"box": [-130.63812255859375, 118.15937805175781, -38.68667984008789, 4.735328674316406, 0.0, 0.0, 0.0, 5.263477325439453, 0.0, 0.0, 0.0, 4.538341522216797]}, "content": {"uri": "Block_L23_707.b3dm"}, "geometricError": 0.010847409255802631, "refine": "REPLACE"}], "content": {"uri": "Block_L21_457.b3dm"}, "geometricError": 0.022883784025907516, "refine": "REPLACE"}], "content": {"uri": "Block_L20_248.b3dm"}, "geometricError": 0.04651414230465889, "refine": "REPLACE"}], "content": {"uri": "Block_L19_131.b3dm"}, "geometricError": 0.08887282758951187, "refine": "REPLACE"}}