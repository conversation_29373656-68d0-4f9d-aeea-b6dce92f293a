{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.7450594305992126, "root": {"boundingVolume": {"box": [-81.83525085449219, 138.50335693359375, -46.864349365234375, 27.832138061523438, 0.0, 0.0, 0.0, 45.241512298583984, 0.0, 0.0, 0.0, 24.84344482421875]}, "children": [{"boundingVolume": {"box": [-98.07066345214844, 102.68716430664062, -43.217018127441406, 11.596725463867188, 0.0, 0.0, 0.0, 9.425312042236328, 0.0, 0.0, 0.0, 21.19611358642578]}, "children": [{"boundingVolume": {"box": [-98.07066345214844, 102.68716430664062, -53.83362579345703, 11.596725463867188, 0.0, 0.0, 0.0, 9.425312042236328, 0.0, 0.0, 0.0, 10.579505920410156]}, "content": {"uri": "Block_L18_65.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.07066345214844, 102.68716430664062, -32.63751220703125, 11.596725463867188, 0.0, 0.0, 0.0, 9.425312042236328, 0.0, 0.0, 0.0, 10.616607666015625]}, "content": {"uri": "Block_L19_236.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_31.b3dm"}, "geometricError": 0.3599258363246918, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.07066345214844, 125.30791473388672, -50.145835876464844, 11.596725463867188, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 15.929487228393555]}, "children": [{"boundingVolume": {"box": [-98.07066345214844, 125.30791473388672, -57.90931701660156, 11.596725463867188, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 7.91278076171875]}, "content": {"uri": "Block_L18_64.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-99.02471923828125, 125.30791473388672, -42.1064453125, 10.642669677734375, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 7.890092849731445]}, "content": {"uri": "Block_L18_63.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_30.b3dm"}, "geometricError": 0.4007404148578644, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.238525390625, 115.88259887695312, -53.468482971191406, 16.23541259765625, 0.0, 0.0, 0.0, 22.62075424194336, 0.0, 0.0, 0.0, 17.74859046936035]}, "content": {"uri": "Block_L17_29.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.7513198852539, 147.92868041992188, -49.53588104248047, 13.916069030761719, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 16.905149459838867]}, "content": {"uri": "Block_L18_122.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.7513198852539, 170.5494384765625, -50.416748046875, 13.916069030761719, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 16.154762268066406]}, "children": [{"boundingVolume": {"box": [-95.7513198852539, 170.5494384765625, -50.416748046875, 13.916069030761719, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 16.154762268066406]}, "children": [{"boundingVolume": {"box": [-95.65809631347656, 170.5494384765625, -57.138187408447266, 13.82284164428711, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 9.40981674194336]}, "content": {"uri": "Block_L19_115.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-96.34304809570312, 170.5494384765625, -40.999263763427734, 13.324337005615234, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 6.729106903076172]}, "content": {"uri": "Block_L20_450.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_234.b3dm"}, "geometricError": 0.17313550412654877, "refine": "REPLACE"}], "content": {"uri": "Block_L18_121.b3dm"}, "geometricError": 0.35063520073890686, "refine": "REPLACE"}, {"boundingVolume": {"box": [-67.91918182373047, 149.81373596191406, -57.36786651611328, 13.916069030761719, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 12.496023178100586]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 149.81373596191406, -57.36786651611328, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 12.496023178100586]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 149.81373596191406, -62.56972122192383, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.284900665283203]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 149.81373596191406, -62.56972122192383, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 7.284900665283203]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 142.2734832763672, -62.56972122192383, 6.958034515380859, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 7.284900665283203]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 142.2734832763672, -64.99801635742188, 6.958034515380859, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 4.856601715087891]}, "content": {"uri": "Block_L22_605.b3dm"}, "geometricError": 0.014192696660757065, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.35623168945312, 142.2734832763672, -57.71311950683594, 3.4790191650390625, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.4282989501953125]}, "content": {"uri": "Block_L22_604.b3dm"}, "geometricError": 0.013012493960559368, "refine": "REPLACE"}, {"boundingVolume": {"box": [-71.398193359375, 142.2734832763672, -57.71311950683594, 3.479015350341797, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 2.4282989501953125]}, "content": {"uri": "Block_L22_603.b3dm"}, "geometricError": 0.012709071859717369, "refine": "REPLACE"}], "content": {"uri": "Block_L21_402.b3dm"}, "geometricError": 0.0265696682035923, "refine": "REPLACE"}, {"boundingVolume": {"box": [-74.87721252441406, 153.5838623046875, -62.20984649658203, 6.958034515380859, 0.0, 0.0, 0.0, 7.540252685546875, 0.0, 0.0, 0.0, 6.925025939941406]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 151.0704345703125, -60.88016891479492, 6.958034515380859, 0.0, 0.0, 0.0, 5.026832580566406, 0.0, 0.0, 0.0, 5.595348358154297]}, "content": {"uri": "Block_L22_602.b3dm"}, "geometricError": 0.012411755509674549, "refine": "REPLACE"}, {"boundingVolume": {"box": [-74.87721252441406, 158.61068725585938, -62.20984649658203, 6.958034515380859, 0.0, 0.0, 0.0, 2.5134201049804688, 0.0, 0.0, 0.0, 6.925025939941406]}, "content": {"uri": "Block_L22_601.b3dm"}, "geometricError": 0.011180603876709938, "refine": "REPLACE"}], "content": {"uri": "Block_L21_401.b3dm"}, "geometricError": 0.023563994094729424, "refine": "REPLACE"}], "content": {"uri": "Block_L20_220.b3dm"}, "geometricError": 0.05066161975264549, "refine": "REPLACE"}], "content": {"uri": "Block_L19_114.b3dm"}, "geometricError": 0.10118268430233002, "refine": "REPLACE"}, {"boundingVolume": {"box": [-74.87721252441406, 149.81373596191406, -50.078330993652344, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 5.206487655639648]}, "content": {"uri": "Block_L19_113.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_59.b3dm"}, "geometricError": 0.1860356330871582, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.96114730834961, 149.81373596191406, -59.399566650390625, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 10.464326858520508]}, "content": {"uri": "Block_L18_58.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_28.b3dm"}, "geometricError": 0.37708988785743713, "refine": "REPLACE"}, {"boundingVolume": {"box": [-67.91918182373047, 172.4344940185547, -59.326141357421875, 13.916069030761719, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 10.608001708984375]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 172.4344940185547, -59.06993103027344, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 10.351791381835938]}, "content": {"uri": "Block_L19_233.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.96114730834961, 172.4344940185547, -60.72241973876953, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 9.211725234985352]}, "children": [{"boundingVolume": {"box": [-60.96114730834961, 172.4344940185547, -60.732276916503906, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 9.201866149902344]}, "content": {"uri": "Block_L20_447.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_232.b3dm"}, "geometricError": 0.1687474250793457, "refine": "REPLACE"}], "content": {"uri": "Block_L18_120.b3dm"}, "geometricError": 0.3250143826007843, "refine": "REPLACE"}], "content": {"uri": "Block_L16_10.b3dm"}, "geometricError": 0.7450594305992126, "refine": "REPLACE"}}