# Test host.docker.internal connection
Write-Host "Testing host.docker.internal connection..." -ForegroundColor Green

# Step 1: Login
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test host.docker.internal connection
Write-Host "Testing host.docker.internal connection..." -ForegroundColor Yellow

$connectionData = @{
    host = "host.docker.internal"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $connectionData
    Write-Host "Host connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
} catch {
    Write-Host "Host connection failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Check if there's a PostgreSQL service running on host
    Write-Host ""
    Write-Host "Checking if PostgreSQL is running on host..." -ForegroundColor Yellow
    try {
        $pgProcess = Get-Process -Name "postgres" -ErrorAction SilentlyContinue
        if ($pgProcess) {
            Write-Host "PostgreSQL process found on host" -ForegroundColor Green
        } else {
            Write-Host "No PostgreSQL process found on host" -ForegroundColor Red
            Write-Host "This is expected if you're only using the containerized database" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Could not check PostgreSQL process" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Note: If host connection fails, this is normal if you don't have" -ForegroundColor Cyan
Write-Host "PostgreSQL installed on your host machine. Use the container" -ForegroundColor Cyan
Write-Host "connection instead." -ForegroundColor Cyan
