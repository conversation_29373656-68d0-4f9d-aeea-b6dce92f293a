# MTDT系统生产环境检查清单

## 部署前检查

### 1. 系统环境检查
- [ ] 服务器硬件配置满足要求（8GB+ RAM, 20GB+ 磁盘）
- [ ] 操作系统版本兼容（Windows 10/11 或 Linux）
- [ ] Docker Desktop 4.0+ 已安装并运行正常
- [ ] 网络连接稳定，带宽充足
- [ ] 防火墙配置正确，必要端口已开放

### 2. 安全配置检查
- [ ] 修改默认密码和密钥
- [ ] 配置HTTPS证书（如需外网访问）
- [ ] 设置访问控制和IP白名单
- [ ] 启用审计日志记录
- [ ] 配置数据备份策略

### 3. 配置文件检查
- [ ] .env文件配置正确
- [ ] 数据库连接参数正确
- [ ] 邮件服务配置正确
- [ ] Redis配置正确
- [ ] JWT密钥已更新

### 4. 数据准备
- [ ] 初始化数据库结构
- [ ] 导入基础数据（如有）
- [ ] 创建管理员账户
- [ ] 测试数据库连接

## 部署过程检查

### 1. 构建检查
- [ ] 前端构建成功（npm run build）
- [ ] Docker镜像构建成功
- [ ] 所有依赖项正确安装
- [ ] 构建产物完整性检查

### 2. 服务启动检查
- [ ] 所有容器成功启动
- [ ] 容器健康检查通过
- [ ] 服务间网络连接正常
- [ ] 端口映射配置正确

### 3. 功能验证
- [ ] 用户注册登录功能正常
- [ ] 地图加载和显示正常
- [ ] 测量工具功能正常
- [ ] 绘图工具功能正常
- [ ] 数据导入导出功能正常
- [ ] 空间分析功能正常

## 部署后检查

### 1. 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 并发用户测试通过
- [ ] 内存使用率 < 80%
- [ ] CPU使用率 < 70%

### 2. 稳定性测试
- [ ] 长时间运行测试（24小时+）
- [ ] 重启恢复测试
- [ ] 异常情况处理测试
- [ ] 数据一致性检查

### 3. 安全测试
- [ ] 访问控制测试
- [ ] 数据传输加密验证
- [ ] SQL注入防护测试
- [ ] XSS攻击防护测试
- [ ] 敏感信息泄露检查

### 4. 备份恢复测试
- [ ] 数据库备份功能测试
- [ ] 数据恢复功能测试
- [ ] 备份文件完整性验证
- [ ] 灾难恢复流程测试

## 监控和维护

### 1. 监控配置
- [ ] 系统资源监控
- [ ] 应用性能监控
- [ ] 错误日志监控
- [ ] 用户访问监控
- [ ] 安全事件监控

### 2. 告警设置
- [ ] 服务异常告警
- [ ] 资源使用告警
- [ ] 安全事件告警
- [ ] 备份失败告警

### 3. 维护计划
- [ ] 定期备份计划
- [ ] 系统更新计划
- [ ] 安全补丁计划
- [ ] 性能优化计划
- [ ] 容量扩展计划

## 文档和培训

### 1. 技术文档
- [ ] 部署文档完整
- [ ] 运维手册完整
- [ ] 故障排除指南完整
- [ ] API文档完整

### 2. 用户文档
- [ ] 用户使用手册
- [ ] 功能操作指南
- [ ] 常见问题解答
- [ ] 视频教程（可选）

### 3. 团队培训
- [ ] 系统管理员培训
- [ ] 最终用户培训
- [ ] 故障处理培训
- [ ] 安全操作培训

## 上线准备

### 1. 上线计划
- [ ] 上线时间安排
- [ ] 回滚方案准备
- [ ] 应急联系人确定
- [ ] 上线步骤清单

### 2. 通知和沟通
- [ ] 用户上线通知
- [ ] 系统维护通知
- [ ] 技术支持联系方式
- [ ] 反馈收集渠道

### 3. 最终检查
- [ ] 所有功能最终测试
- [ ] 性能指标确认
- [ ] 安全配置确认
- [ ] 备份策略确认
- [ ] 监控告警确认

## 检查工具

### 自动化检查脚本
```bash
# 运行完整健康检查
health-check.bat

# 运行性能测试
performance-test.bat

# 运行集成测试
quick-test.bat

# 系统管理控制台
manage-system.bat
```

### 手动检查命令
```bash
# 检查容器状态
docker-compose -f docker-compose.internal.yml ps

# 检查资源使用
docker stats

# 检查日志
docker-compose -f docker-compose.internal.yml logs

# 检查网络连接
netstat -an | findstr ":8080\|:8000\|:5432"

# 检查磁盘空间
dir /s /-c

# 检查内存使用
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory
```

## 风险评估

### 高风险项
- [ ] 数据库连接失败
- [ ] 网络安全漏洞
- [ ] 性能瓶颈
- [ ] 数据丢失风险

### 中风险项
- [ ] 服务间通信异常
- [ ] 前端加载缓慢
- [ ] 用户体验问题
- [ ] 兼容性问题

### 低风险项
- [ ] 界面显示问题
- [ ] 非关键功能异常
- [ ] 日志记录问题
- [ ] 文档不完整

## 应急预案

### 服务异常
1. 立即检查服务状态
2. 查看错误日志
3. 尝试重启服务
4. 联系技术支持

### 性能问题
1. 检查系统资源
2. 分析性能瓶颈
3. 优化配置参数
4. 考虑扩容方案

### 安全事件
1. 立即隔离问题
2. 保存证据日志
3. 评估影响范围
4. 实施修复措施

### 数据问题
1. 停止相关操作
2. 评估数据损失
3. 从备份恢复
4. 验证数据完整性

---

## 签署确认

### 技术负责人确认
- 姓名：_______________
- 日期：_______________
- 签名：_______________

### 项目负责人确认
- 姓名：_______________
- 日期：_______________
- 签名：_______________

### 运维负责人确认
- 姓名：_______________
- 日期：_______________
- 签名：_______________

---

**注意：本检查清单必须在生产环境部署前完成所有检查项，确保系统稳定可靠运行。**
