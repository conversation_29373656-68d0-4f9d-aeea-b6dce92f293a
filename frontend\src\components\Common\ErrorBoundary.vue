<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    
    <!-- 错误显示界面 -->
    <div v-else class="error-display">
      <el-result
        icon="error"
        title="系统错误"
        :sub-title="errorMessage"
      >
        <template #extra>
          <el-button type="primary" @click="retry">
            <el-icon><Refresh /></el-icon>
            重试
          </el-button>
          <el-button @click="reportError">
            <el-icon><Warning /></el-icon>
            报告错误
          </el-button>
        </template>
      </el-result>
      
      <!-- 错误详情（开发模式） -->
      <el-collapse v-if="isDevelopment" class="error-details">
        <el-collapse-item title="错误详情" name="details">
          <pre class="error-stack">{{ errorStack }}</pre>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Warning } from '@element-plus/icons-vue'

const hasError = ref(false)
const errorMessage = ref('')
const errorStack = ref('')
const isDevelopment = ref(import.meta.env.DEV)

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  console.error('ErrorBoundary caught error:', error)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorStack.value = error.stack || ''
  
  // 发送错误报告到后端（可选）
  if (!isDevelopment.value) {
    sendErrorReport(error, info)
  }
  
  return false // 阻止错误继续传播
})

// 重试功能
const retry = async () => {
  hasError.value = false
  errorMessage.value = ''
  errorStack.value = ''
  
  await nextTick()
  ElMessage.success('已重新加载组件')
}

// 报告错误
const reportError = () => {
  const errorInfo = {
    message: errorMessage.value,
    stack: errorStack.value,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  }
  
  // 复制错误信息到剪贴板
  navigator.clipboard.writeText(JSON.stringify(errorInfo, null, 2)).then(() => {
    ElMessage.success('错误信息已复制到剪贴板')
  }).catch(() => {
    ElMessage.warning('无法复制错误信息')
  })
}

// 发送错误报告到后端
const sendErrorReport = async (error: Error, info: string) => {
  try {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentInfo: info,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    // 这里可以发送到后端API
    // await apiClient.post('/api/error-report', errorReport)
    console.log('Error report:', errorReport)
  } catch (reportError) {
    console.error('Failed to send error report:', reportError)
  }
}
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-display {
  padding: 20px;
  text-align: center;
}

.error-details {
  margin-top: 20px;
  text-align: left;
}

.error-stack {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
