# 完整的数据库连接测试流程
Write-Host "========================================" -ForegroundColor Green
Write-Host "MTDT 数据库连接完整测试" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 步骤1: 登录获取token
Write-Host "步骤1: 用户登录..." -ForegroundColor Yellow

$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.access_token
    Write-Host "✅ 登录成功" -ForegroundColor Green
    Write-Host "Token: $($token.Substring(0,20))..." -ForegroundColor Gray
} catch {
    Write-Host "❌ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 步骤2: 测试数据库连接
Write-Host "步骤2: 测试数据库连接..." -ForegroundColor Yellow

$connectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $connectionData
    Write-Host "✅ 数据库连接测试成功" -ForegroundColor Green
    Write-Host "消息: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS版本: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 数据库连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试获取详细错误信息
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody" -ForegroundColor Red
    }
    exit 1
}

Write-Host ""

# 步骤3: 获取数据库表列表
Write-Host "步骤3: 获取数据库表列表..." -ForegroundColor Yellow

try {
    $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $connectionData
    
    if ($tablesResponse -and $tablesResponse.Count -gt 0) {
        Write-Host "✅ 成功获取 $($tablesResponse.Count) 个表" -ForegroundColor Green
        Write-Host ""
        Write-Host "数据库表列表:" -ForegroundColor Cyan
        Write-Host "─────────────────────────────────────" -ForegroundColor Gray
        
        $tablesResponse | ForEach-Object {
            Write-Host "📋 $($_.schema).$($_.name) ($($_.type))" -ForegroundColor White
            if ($_.geometryColumns -and $_.geometryColumns.Count -gt 0) {
                $geomCols = $_.geometryColumns | ForEach-Object { $_.columnName }
                Write-Host "   几何列: $($geomCols -join ', ')" -ForegroundColor Gray
            }
            if ($_.totalRows -ne $null) {
                Write-Host "   记录数: $($_.totalRows)" -ForegroundColor Gray
            }
            Write-Host ""
        }
    } else {
        Write-Host "⚠️ 没有找到包含几何列的表" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 获取表列表失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试获取详细错误信息
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误详情: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""

# 步骤4: 测试健康检查API
Write-Host "步骤4: 测试健康检查API..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/health" -Method GET
    Write-Host "✅ 健康检查通过" -ForegroundColor Green
    Write-Host "状态: $($healthResponse.status)" -ForegroundColor Gray
    Write-Host "时间戳: $($healthResponse.timestamp)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "测试完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "💡 提示:" -ForegroundColor Cyan
Write-Host "如果所有测试都通过，您现在可以在MTDT系统中:" -ForegroundColor White
Write-Host "1. 打开图层管理面板" -ForegroundColor White
Write-Host "2. 选择'添加数据库图层'" -ForegroundColor White
Write-Host "3. 选择'WebGIS主数据库(容器)'连接" -ForegroundColor White
Write-Host "4. 测试连接并加载表列表" -ForegroundColor White
Write-Host ""
Write-Host "或者按 Ctrl+Shift+D 打开数据库测试面板进行详细测试" -ForegroundColor White
