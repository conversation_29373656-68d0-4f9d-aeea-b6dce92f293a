<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis version="3.40.5-Bratislava">
  <identifier>保护站点位_Merge</identifier>
  <parentidentifier></parentidentifier>
  <language>ZHO</language>
  <type>dataset</type>
  <title>保护站点位_Merge</title>
  <abstract></abstract>
  <links/>
  <dates/>
  <fees></fees>
  <encoding></encoding>
  <crs>
    <spatialrefsys nativeFormat="Wkt">
      <wkt>PROJCRS["Beijing 1954 / Gauss-Kruger zone 18",BASEGEOGCRS["Beijing 1954",DATUM["Beijing 1954",ELLIPSOID["Krassowsky 1940",6378245,298.3,LENGTHUNIT["metre",1]]],PRIMEM["Greenwich",0,ANGLEUNIT["degree",0.0174532925199433]],ID["EPSG",4214]],CONVERSION["6-degree Gauss-Kruger zone 18",METHOD["Transverse Mercator",ID["EPSG",9807]],PARAMETER["Latitude of natural origin",0,ANG<PERSON>UN<PERSON>["degree",0.0174532925199433],ID["EPSG",8801]],PARAMETER["Longitude of natural origin",105,ANGLEUNIT["degree",0.0174532925199433],ID["EPSG",8802]],PARAMETER["Scale factor at natural origin",1,SCALEUNIT["unity",1],ID["EPSG",8805]],PARAMETER["False easting",18500000,LENGTHUNIT["metre",1],ID["EPSG",8806]],PARAMETER["False northing",0,LENGTHUNIT["metre",1],ID["EPSG",8807]]],CS[Cartesian,2],AXIS["northing (X)",north,ORDER[1],LENGTHUNIT["metre",1]],AXIS["easting (Y)",east,ORDER[2],LENGTHUNIT["metre",1]],USAGE[SCOPE["Topographic mapping (medium scale)."],AREA["China - onshore between 102°E and 108°E."],BBOX[21.53,102,42.47,108]],ID["EPSG",21418]]</wkt>
      <proj4>+proj=tmerc +lat_0=0 +lon_0=105 +k=1 +x_0=18500000 +y_0=0 +ellps=krass +units=m +no_defs</proj4>
      <srsid>1889</srsid>
      <srid>21418</srid>
      <authid>EPSG:21418</authid>
      <description>Beijing 1954 / Gauss-Kruger zone 18</description>
      <projectionacronym>tmerc</projectionacronym>
      <ellipsoidacronym>EPSG:7024</ellipsoidacronym>
      <geographicflag>false</geographicflag>
    </spatialrefsys>
  </crs>
  <extent/>
</qgis>
