# MTDT 山思数字平台

## 项目简介

MTDT（Mountain Thinking Digital Twin）是一个基于现代Web技术栈构建的数字孪生平台，集成了GIS地理信息系统、3D可视化、空间分析等功能。

### 主要特性

- 🗺️ **3D地图可视化** - 基于Cesium的三维地球展示
- 📊 **空间分析** - 缓冲区分析、叠加分析、统计分析
- 🎨 **绘图工具** - 点、线、面、圆形、矩形绘制
- 📏 **测量工具** - 距离测量、面积测量、高程测量
- 🗄️ **数据管理** - 数据导入导出、图层管理
- 👥 **用户管理** - 邮箱验证、权限控制
- 🔧 **系统监控** - 性能监控、错误处理

## 技术架构

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **Element Plus** - Vue 3组件库
- **Cesium** - 3D地球和地图引擎
- **Pinia** - 状态管理
- **Turf.js** - 地理空间分析

### 后端技术栈
- **NestJS** - Node.js企业级框架
- **TypeScript** - 类型安全的JavaScript
- **PostgreSQL + PostGIS** - 空间数据库
- **Redis** - 缓存和会话存储
- **JWT** - 身份认证
- **Nodemailer** - 邮件服务

### 部署技术
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排
- **Nginx** - 反向代理和静态文件服务

## 快速开始

### 系统要求

- Windows 10/11 或 Linux
- Docker Desktop 4.0+
- Node.js 16+ (仅开发环境需要)
- 8GB+ RAM
- 20GB+ 可用磁盘空间

### 一键部署（推荐）

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd MTDT-V107
   ```

2. **运行部署脚本**
   ```bash
   # Windows
   deploy-enhanced.bat
   
   # 或使用原始脚本
   deploy-internal.bat
   ```

3. **访问系统**
   - 生产环境: http://localhost:8080
   - 开发环境: http://localhost:5174
   - 后端API: http://localhost:8000/api

### 手动部署

1. **环境配置**
   ```bash
   # 复制环境配置文件
   cp .env.example .env
   # 编辑配置文件
   notepad .env
   ```

2. **构建前端**
   ```bash
   cd frontend
   npm install
   npm run build
   cd ..
   ```

3. **启动服务**
   ```bash
   docker-compose -f docker-compose.internal.yml up -d
   ```

## 开发指南

### 开发环境设置

1. **启动基础服务**
   ```bash
   docker-compose -f docker-compose.internal.yml up -d postgres redis backend
   ```

2. **启动前端开发服务器**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **访问开发环境**
   - 前端: http://localhost:5174
   - 后端: http://localhost:8000

### 项目结构

```
MTDT-V107/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/      # Vue组件
│   │   ├── views/          # 页面视图
│   │   ├── stores/         # 状态管理
│   │   ├── utils/          # 工具函数
│   │   └── assets/         # 静态资源
│   ├── public/             # 公共文件
│   └── dist/               # 构建产物
├── backend/                  # 后端项目
│   ├── src/
│   │   ├── auth/           # 认证模块
│   │   ├── database/       # 数据库模块
│   │   ├── users/          # 用户模块
│   │   └── common/         # 公共模块
│   └── uploads/            # 文件上传目录
├── docker-compose.*.yml      # Docker编排文件
├── deploy-*.bat             # 部署脚本
├── manage-system.bat        # 系统管理脚本
└── README.md               # 项目文档
```

### 开发工作流

1. **功能开发**
   - 使用混合开发模式（容器后端 + 本地前端）
   - 实时热重载，提高开发效率

2. **测试验证**
   ```bash
   # 运行集成测试
   quick-test.bat
   
   # 运行性能测试
   performance-test.bat
   
   # 运行健康检查
   health-check.bat
   ```

3. **部署更新**
   ```bash
   # 构建并部署
   docker-compose -f docker-compose.internal.yml up --build -d
   ```

## 系统管理

### 管理控制台

运行系统管理控制台：
```bash
manage-system.bat
```

提供以下功能：
- 查看系统状态
- 启动/停止/重启服务
- 查看日志
- 系统更新
- 数据库备份/恢复
- 性能测试
- 系统清理
- 开发模式切换

### 常用命令

```bash
# 查看服务状态
docker-compose -f docker-compose.internal.yml ps

# 查看日志
docker-compose -f docker-compose.internal.yml logs -f [service]

# 重启服务
docker-compose -f docker-compose.internal.yml restart [service]

# 更新系统
docker-compose -f docker-compose.internal.yml up --build -d

# 数据库备份
docker-compose -f docker-compose.internal.yml exec postgres pg_dump -U mtdt_user webgis_db > backup.sql

# 系统清理
docker system prune -f
```

## 功能说明

### 地图功能
- 3D地球展示
- 多种地图底图
- 图层管理
- 视角控制

### 测量工具
- 距离测量：支持多段线测量
- 面积测量：支持多边形面积计算
- 高程测量：获取地面高程信息

### 绘图工具
- 点标记：添加点位标注
- 线条绘制：绘制路径和边界
- 多边形：绘制区域范围
- 圆形和矩形：快速绘制规则图形

### 空间分析
- 缓冲区分析：生成指定距离的缓冲区
- 叠加分析：多图层空间叠加
- 统计分析：属性统计和空间统计

### 数据管理
- 支持格式：GeoJSON、KML、CSV、Excel
- 数据导入：文件上传和解析
- 数据导出：多格式导出
- 图层管理：图层显示控制

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查Docker Desktop是否运行
   - 检查端口是否被占用
   - 查看错误日志

2. **前端无法访问**
   - 检查防火墙设置
   - 确认端口配置正确
   - 检查网络连接

3. **数据库连接失败**
   - 检查数据库容器状态
   - 验证连接参数
   - 查看数据库日志

4. **性能问题**
   - 检查系统资源使用
   - 优化数据库查询
   - 清理Docker缓存

### 获取帮助

- 运行健康检查：`health-check.bat`
- 查看系统日志：`docker-compose -f docker-compose.internal.yml logs`
- 使用管理控制台：`manage-system.bat`

## 更新日志

### v1.0.7 (当前版本)
- ✅ 完善核心GIS功能
- ✅ 实现数据库图层功能
- ✅ 开发空间分析工具
- ✅ 优化用户体验
- ✅ 添加高级功能扩展
- ✅ 完善部署和管理工具

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交问题和功能请求。在提交代码前，请确保：

1. 代码符合项目规范
2. 通过所有测试
3. 更新相关文档
4. 提供清晰的提交信息

## 联系方式

- 项目维护者：MTDT开发团队
- 邮箱：<EMAIL>
- 项目地址：MTDT山思数字平台
