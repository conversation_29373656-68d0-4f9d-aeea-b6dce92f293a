{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.15513767302036285, "root": {"boundingVolume": {"box": [-109.2472915649414, 15.757158279418945, -2.8270015716552734, 18.16327667236328, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 11.554740905761719]}, "children": [{"boundingVolume": {"box": [-118.24725341796875, 15.757158279418945, -2.866084575653076, 9.05996322631836, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 11.515657424926758]}, "children": [{"boundingVolume": {"box": [-116.76058959960938, 8.42563533782959, -2.866084575653076, 7.57330322265625, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 11.515657424926758]}, "children": [{"boundingVolume": {"box": [-116.12684631347656, 8.42563533782959, -7.664275646209717, 6.939556121826172, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 6.717466831207275]}, "children": [{"boundingVolume": {"box": [-116.1265869140625, 8.42563533782959, -7.664275646209717, 6.939304351806641, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 6.717466831207275]}, "content": {"uri": "Block_L23_946.b3dm"}, "geometricError": 0.00950537621974945, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1674.b3dm"}, "geometricError": 0.019070519134402275, "refine": "REPLACE"}, {"boundingVolume": {"box": [-117.2548599243164, 8.42563533782959, 3.851382255554199, 7.0724334716796875, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 4.798191070556641]}, "children": [{"boundingVolume": {"box": [-117.25709533691406, 4.759873867034912, 3.851382255554199, 7.069866180419922, 0.0, 0.0, 0.0, 3.6657614707946777, 0.0, 0.0, 0.0, 4.798191070556641]}, "content": {"uri": "Block_L22_1180.b3dm"}, "geometricError": 0.012908183969557285, "refine": "REPLACE"}, {"boundingVolume": {"box": [-116.64208984375, 12.09139633178711, 3.03275203704834, 6.425045013427734, 0.0, 0.0, 0.0, 3.6657614707946777, 0.0, 0.0, 0.0, 3.9795608520507812]}, "content": {"uri": "Block_L23_945.b3dm"}, "geometricError": 0.007898821495473385, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1673.b3dm"}, "geometricError": 0.02115459181368351, "refine": "REPLACE"}], "content": {"uri": "Block_L21_939.b3dm"}, "geometricError": 0.04057174175977707, "refine": "REPLACE"}, {"boundingVolume": {"box": [-118.23892211914062, 23.088680267333984, -4.073997497558594, 9.051631927490234, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 10.307744979858398]}, "children": [{"boundingVolume": {"box": [-115.72628784179688, 23.088680267333984, -7.510874271392822, 6.539005279541016, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 6.87086820602417]}, "children": [{"boundingVolume": {"box": [-115.72569274902344, 19.42292022705078, -7.510874271392822, 6.538410186767578, 0.0, 0.0, 0.0, 3.6657609939575195, 0.0, 0.0, 0.0, 6.87086820602417]}, "content": {"uri": "Block_L23_944.b3dm"}, "geometricError": 0.00810677744448185, "refine": "REPLACE"}, {"boundingVolume": {"box": [-115.3617172241211, 26.75444221496582, -7.510874271392822, 6.174430847167969, 0.0, 0.0, 0.0, 3.665761947631836, 0.0, 0.0, 0.0, 6.87086820602417]}, "content": {"uri": "Block_L23_943.b3dm"}, "geometricError": 0.008322461508214474, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1672.b3dm"}, "geometricError": 0.016428248956799507, "refine": "REPLACE"}, {"boundingVolume": {"box": [-119.37452697753906, 23.088680267333984, 2.7954280376434326, 7.916027069091797, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 3.435434103012085]}, "children": [{"boundingVolume": {"box": [-122.67831420898438, 23.088680267333984, 3.680753707885742, 4.612232208251953, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 2.5501084327697754]}, "content": {"uri": "Block_L23_942.b3dm"}, "geometricError": 0.005630940664559603, "refine": "REPLACE"}, {"boundingVolume": {"box": [-114.77163696289062, 23.088680267333984, 2.6103358268737793, 3.2944488525390625, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 3.2503418922424316]}, "content": {"uri": "Block_L23_941.b3dm"}, "geometricError": 0.0062018223106861115, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1671.b3dm"}, "geometricError": 0.012046363204717636, "refine": "REPLACE"}], "content": {"uri": "Block_L21_938.b3dm"}, "geometricError": 0.028051119297742844, "refine": "REPLACE"}], "content": {"uri": "Block_L20_493.b3dm"}, "geometricError": 0.06903671473264694, "refine": "REPLACE"}, {"boundingVolume": {"box": [-100.13565063476562, 15.757158279418945, -8.348893165588379, 9.0516357421875, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 6.032849311828613]}, "children": [{"boundingVolume": {"box": [-100.13565063476562, 8.42563533782959, -8.714888572692871, 9.0516357421875, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 5.666853427886963]}, "children": [{"boundingVolume": {"box": [-105.415771484375, 8.42563533782959, -8.714888572692871, 3.771514892578125, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 5.666853427886963]}, "children": [{"boundingVolume": {"box": [-105.415771484375, 4.759873867034912, -8.714888572692871, 3.771514892578125, 0.0, 0.0, 0.0, 3.6657614707946777, 0.0, 0.0, 0.0, 5.666853427886963]}, "content": {"uri": "Block_L22_1179.b3dm"}, "geometricError": 0.011914110742509365, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.415771484375, 12.09139633178711, -11.103583335876465, 3.771514892578125, 0.0, 0.0, 0.0, 3.6657614707946777, 0.0, 0.0, 0.0, 3.2781591415405273]}, "content": {"uri": "Block_L23_940.b3dm"}, "geometricError": 0.010105098597705364, "refine": "REPLACE"}], "content": {"uri": "Block_L21_696.b3dm"}, "geometricError": 0.0222004447132349, "refine": "REPLACE"}, {"boundingVolume": {"box": [-96.3641357421875, 7.43403959274292, -12.022285461425781, 5.280120849609375, 0.0, 0.0, 0.0, 6.3399271965026855, 0.0, 0.0, 0.0, 2.359457492828369]}, "children": [{"boundingVolume": {"box": [-96.3641357421875, 7.422378063201904, -12.02338981628418, 5.280120849609375, 0.0, 0.0, 0.0, 6.32826566696167, 0.0, 0.0, 0.0, 2.3583521842956543]}, "content": {"uri": "Block_L22_1178.b3dm"}, "geometricError": 0.012029372155666351, "refine": "REPLACE"}], "content": {"uri": "Block_L21_695.b3dm"}, "geometricError": 0.024065915495157242, "refine": "REPLACE"}], "content": {"uri": "Block_L20_378.b3dm"}, "geometricError": 0.04594769701361656, "refine": "REPLACE"}, {"boundingVolume": {"box": [-101.92044067382812, 23.088680267333984, -8.35779094696045, 7.266841888427734, 0.0, 0.0, 0.0, 7.3315229415893555, 0.0, 0.0, 0.0, 6.023951530456543]}, "children": [{"boundingVolume": {"box": [-104.31033325195312, 20.033878326416016, -8.358053207397461, 4.876953125, 0.0, 0.0, 0.0, 4.276721000671387, 0.0, 0.0, 0.0, 6.023689270019531]}, "children": [{"boundingVolume": {"box": [-104.31098937988281, 20.033878326416016, -11.884577751159668, 4.8762969970703125, 0.0, 0.0, 0.0, 4.276721000671387, 0.0, 0.0, 0.0, 2.497164726257324]}, "content": {"uri": "Block_L23_939.b3dm"}, "geometricError": 0.010638928972184658, "refine": "REPLACE"}, {"boundingVolume": {"box": [-106.47884368896484, 20.033878326416016, -5.891382217407227, 2.7084426879882812, 0.0, 0.0, 0.0, 4.276721000671387, 0.0, 0.0, 0.0, 3.496030807495117]}, "content": {"uri": "Block_L23_938.b3dm"}, "geometricError": 0.009391332045197487, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1670.b3dm"}, "geometricError": 0.020221754908561707, "refine": "REPLACE"}, {"boundingVolume": {"box": [-101.99020385742188, 27.365402221679688, -9.684903144836426, 7.19708251953125, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 4.696839332580566]}, "children": [{"boundingVolume": {"box": [-106.18850708007812, 27.365402221679688, -9.68657112121582, 2.9987831115722656, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 4.695170879364014]}, "content": {"uri": "Block_L23_937.b3dm"}, "geometricError": 0.009844202548265457, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.99142456054688, 27.365402221679688, -11.858750343322754, 4.198299407958984, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 2.5229921340942383]}, "content": {"uri": "Block_L23_936.b3dm"}, "geometricError": 0.009896133095026016, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1669.b3dm"}, "geometricError": 0.019764026626944542, "refine": "REPLACE"}], "content": {"uri": "Block_L21_937.b3dm"}, "geometricError": 0.040028586983680725, "refine": "REPLACE"}], "content": {"uri": "Block_L20_492.b3dm"}, "geometricError": 0.08577104657888412, "refine": "REPLACE"}], "content": {"uri": "Block_L19_250.b3dm"}, "geometricError": 0.15513767302036285, "refine": "REPLACE"}}