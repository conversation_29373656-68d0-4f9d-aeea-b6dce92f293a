@echo off
title MTDT System Enhanced Deployment
color 0A

echo.
echo ========================================
echo    MTDT System Enhanced Deployment
echo ========================================
echo.

REM 检查必要的工具
echo [1/8] Checking prerequisites...
where docker >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker is not installed or not in PATH
    echo Please install Docker Desktop and try again.
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Docker Compose is not installed or not in PATH
    echo Please install Docker Compose and try again.
    pause
    exit /b 1
)

echo [OK] Docker and Docker Compose are available
echo.

REM 检查配置文件
echo [2/8] Checking configuration files...
if not exist "docker-compose.internal.yml" (
    echo [ERROR] docker-compose.internal.yml not found
    pause
    exit /b 1
)

if not exist ".env" (
    echo [WARNING] .env file not found, creating default...
    echo NODE_ENV=production > .env
    echo DB_HOST=postgres >> .env
    echo DB_PORT=5432 >> .env
    echo DB_NAME=webgis_db >> .env
    echo DB_USER=mtdt_user >> .env
    echo DB_PASSWORD=mtdt_password >> .env
    echo REDIS_HOST=redis >> .env
    echo REDIS_PORT=6379 >> .env
    echo JWT_SECRET=your-super-secret-jwt-key-change-this-in-production >> .env
    echo MAIL_HOST=smtp.pfi.org.cn >> .env
    echo MAIL_PORT=465 >> .env
    echo MAIL_USER=<EMAIL> >> .env
    echo MAIL_PASS=Welcome@314 >> .env
    echo [OK] Default .env file created
) else (
    echo [OK] .env file exists
)
echo.

REM 构建前端生产版本
echo [3/8] Building frontend for production...
if exist "frontend\dist" (
    echo [INFO] Frontend build already exists, skipping...
) else (
    echo [INFO] Building frontend...
    cd frontend
    call npm run build
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Frontend build failed
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [OK] Frontend build completed
)
echo.

REM 停止现有服务
echo [4/8] Stopping existing services...
docker-compose -f docker-compose.internal.yml down
echo [OK] Services stopped
echo.

REM 清理旧的镜像（可选）
echo [5/8] Cleaning up old images...
set /p cleanup="Do you want to clean up old Docker images? (y/N): "
if /i "%cleanup%"=="y" (
    docker system prune -f
    echo [OK] Old images cleaned up
) else (
    echo [SKIP] Image cleanup skipped
)
echo.

REM 构建和启动服务
echo [6/8] Building and starting services...
docker-compose -f docker-compose.internal.yml up --build -d
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to start services
    pause
    exit /b 1
)
echo [OK] Services started
echo.

REM 等待服务启动
echo [7/8] Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo [8/8] Checking service status...
docker-compose -f docker-compose.internal.yml ps
echo.

REM 获取本机IP地址
echo Detecting local IP address...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found_ip
    )
)
:found_ip

REM 运行快速测试
echo Running quick health check...
call quick-test.bat >nul 2>&1

echo.
echo ========================================
echo    Deployment Completed Successfully!
echo ========================================
echo.
echo System Access URLs:
echo   Internal Network: http://%LOCAL_IP%:8080
echo   Local Access:     http://localhost:8080
echo   Backend API:      http://%LOCAL_IP%:8000/api
echo   Development:      http://localhost:5174 (if dev server running)
echo.
echo Database Connection:
echo   Host: %LOCAL_IP%:5432
echo   Database: webgis_db
echo   Username: mtdt_user
echo.
echo Management Commands:
echo   View logs:        docker-compose -f docker-compose.internal.yml logs -f
echo   Stop services:    docker-compose -f docker-compose.internal.yml down
echo   Restart services: docker-compose -f docker-compose.internal.yml restart
echo   Update system:    docker-compose -f docker-compose.internal.yml up --build -d
echo.
echo Troubleshooting:
echo   1. If services fail to start, check Docker Desktop is running
echo   2. If ports are in use, stop conflicting services
echo   3. For detailed logs: docker-compose -f docker-compose.internal.yml logs [service]
echo   4. For system status: docker-compose -f docker-compose.internal.yml ps
echo.
echo Next Steps:
echo   1. Access the system using the URLs above
echo   2. Test all functionality
echo   3. Configure additional settings as needed
echo   4. Set up regular backups
echo   5. Monitor system performance
echo.
echo ========================================
echo    Thank you for using MTDT System!
echo ========================================

pause
