{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.18821367621421814, "root": {"boundingVolume": {"box": [-54.003108978271484, -24.83512306213379, -22.517330169677734, 11.132854461669922, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 7.993220806121826]}, "children": [{"boundingVolume": {"box": [-60.497276306152344, -24.83512306213379, -22.517330169677734, 4.638689041137695, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 7.993220806121826]}, "children": [{"boundingVolume": {"box": [-60.497276306152344, -29.744140625, -22.517330169677734, 4.638689041137695, 0.0, 0.0, 0.0, 4.909018516540527, 0.0, 0.0, 0.0, 7.993220806121826]}, "children": [{"boundingVolume": {"box": [-59.93048858642578, -29.744140625, -26.51707649230957, 4.071901321411133, 0.0, 0.0, 0.0, 4.909018516540527, 0.0, 0.0, 0.0, 3.9934749603271484]}, "children": [{"boundingVolume": {"box": [-59.93048858642578, -32.19865036010742, -25.9735164642334, 4.071901321411133, 0.0, 0.0, 0.0, 2.454509735107422, 0.0, 0.0, 0.0, 3.4499149322509766]}, "content": {"uri": "Block_L22_1016.b3dm"}, "geometricError": 0.012492068111896515, "refine": "REPLACE"}, {"boundingVolume": {"box": [-59.8039436340332, -27.289630889892578, -26.51707649230957, 3.9453582763671875, 0.0, 0.0, 0.0, 2.4545087814331055, 0.0, 0.0, 0.0, 3.9934749603271484]}, "content": {"uri": "Block_L22_1015.b3dm"}, "geometricError": 0.013025530613958836, "refine": "REPLACE"}], "content": {"uri": "Block_L21_611.b3dm"}, "geometricError": 0.02556535229086876, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.497276306152344, -29.744140625, -18.530126571655273, 4.638689041137695, 0.0, 0.0, 0.0, 4.909018516540527, 0.0, 0.0, 0.0, 3.9934754371643066]}, "children": [{"boundingVolume": {"box": [-60.497276306152344, -32.19865036010742, -18.530126571655273, 4.638689041137695, 0.0, 0.0, 0.0, 2.454509735107422, 0.0, 0.0, 0.0, 3.9934754371643066]}, "content": {"uri": "Block_L23_847.b3dm"}, "geometricError": 0.01098749227821827, "refine": "REPLACE"}, {"boundingVolume": {"box": [-63.54350280761719, -27.289630889892578, -18.747764587402344, 1.592458724975586, 0.0, 0.0, 0.0, 2.4545087814331055, 0.0, 0.0, 0.0, 3.7758374214172363]}, "content": {"uri": "Block_L23_846.b3dm"}, "geometricError": 0.010940071195363998, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1620.b3dm"}, "geometricError": 0.02194899134337902, "refine": "REPLACE"}], "content": {"uri": "Block_L20_330.b3dm"}, "geometricError": 0.04809577763080597, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.497276306152344, -19.926105499267578, -25.28324317932129, 4.638689041137695, 0.0, 0.0, 0.0, 4.909018516540527, 0.0, 0.0, 0.0, 5.22730827331543]}, "children": [{"boundingVolume": {"box": [-60.497276306152344, -22.380615234375, -25.997894287109375, 4.638689041137695, 0.0, 0.0, 0.0, 2.4545087814331055, 0.0, 0.0, 0.0, 4.512656211853027]}, "children": [{"boundingVolume": {"box": [-62.816619873046875, -22.380615234375, -24.755128860473633, 2.3193435668945312, 0.0, 0.0, 0.0, 2.4545087814331055, 0.0, 0.0, 0.0, 3.2698898315429688]}, "content": {"uri": "Block_L22_1014.b3dm"}, "geometricError": 0.011261524632573128, "refine": "REPLACE"}, {"boundingVolume": {"box": [-58.17793273925781, -22.380615234375, -26.941417694091797, 2.319345474243164, 0.0, 0.0, 0.0, 2.4545087814331055, 0.0, 0.0, 0.0, 3.5691347122192383]}, "content": {"uri": "Block_L22_1013.b3dm"}, "geometricError": 0.0121846254914999, "refine": "REPLACE"}], "content": {"uri": "Block_L21_610.b3dm"}, "geometricError": 0.02358992211520672, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.497276306152344, -17.471595764160156, -26.457033157348633, 4.638689041137695, 0.0, 0.0, 0.0, 2.454509735107422, 0.0, 0.0, 0.0, 4.053518295288086]}, "children": [{"boundingVolume": {"box": [-62.816619873046875, -17.471595764160156, -26.453794479370117, 2.3193435668945312, 0.0, 0.0, 0.0, 2.454509735107422, 0.0, 0.0, 0.0, 4.05027961730957]}, "content": {"uri": "Block_L22_1012.b3dm"}, "geometricError": 0.011917960830032825, "refine": "REPLACE"}, {"boundingVolume": {"box": [-58.17793273925781, -17.471595764160156, -27.068763732910156, 2.319345474243164, 0.0, 0.0, 0.0, 2.454509735107422, 0.0, 0.0, 0.0, 3.4417877197265625]}, "content": {"uri": "Block_L22_1011.b3dm"}, "geometricError": 0.012120380066335201, "refine": "REPLACE"}], "content": {"uri": "Block_L21_609.b3dm"}, "geometricError": 0.024027720093727112, "refine": "REPLACE"}], "content": {"uri": "Block_L20_329.b3dm"}, "geometricError": 0.04776406288146973, "refine": "REPLACE"}], "content": {"uri": "Block_L19_174.b3dm"}, "geometricError": 0.09596244990825653, "refine": "REPLACE"}, {"boundingVolume": {"box": [-49.364418029785156, -24.83512306213379, -25.25613021850586, 6.494165420532227, 0.0, 0.0, 0.0, 9.818037033081055, 0.0, 0.0, 0.0, 5.254420280456543]}, "children": [{"boundingVolume": {"box": [-49.364418029785156, -30.56231117248535, -25.261234283447266, 6.494165420532227, 0.0, 0.0, 0.0, 4.090848922729492, 0.0, 0.0, 0.0, 5.249317169189453]}, "children": [{"boundingVolume": {"box": [-52.61150360107422, -30.56231117248535, -25.261234283447266, 3.2470836639404297, 0.0, 0.0, 0.0, 4.090848922729492, 0.0, 0.0, 0.0, 5.249317169189453]}, "children": [{"boundingVolume": {"box": [-52.61150360107422, -30.56231117248535, -27.885892868041992, 3.2470836639404297, 0.0, 0.0, 0.0, 4.090848922729492, 0.0, 0.0, 0.0, 2.6246585845947266]}, "content": {"uri": "Block_L22_1010.b3dm"}, "geometricError": 0.01283140480518341, "refine": "REPLACE"}, {"boundingVolume": {"box": [-52.61150360107422, -31.94937515258789, -22.63657569885254, 3.2470836639404297, 0.0, 0.0, 0.0, 2.7037858963012695, 0.0, 0.0, 0.0, 2.6246585845947266]}, "content": {"uri": "Block_L22_1009.b3dm"}, "geometricError": 0.011990158818662167, "refine": "REPLACE"}], "content": {"uri": "Block_L21_608.b3dm"}, "geometricError": 0.024867333471775055, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.11733627319336, -30.56231117248535, -27.378501892089844, 3.247081756591797, 0.0, 0.0, 0.0, 4.090848922729492, 0.0, 0.0, 0.0, 3.1320486068725586]}, "children": [{"boundingVolume": {"box": [-46.11733627319336, -32.60773468017578, -27.37942123413086, 3.247081756591797, 0.0, 0.0, 0.0, 2.045424461364746, 0.0, 0.0, 0.0, 3.1311302185058594]}, "content": {"uri": "Block_L22_1008.b3dm"}, "geometricError": 0.011182966642081738, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.11733627319336, -28.516887664794922, -27.998470306396484, 3.247081756591797, 0.0, 0.0, 0.0, 2.045424461364746, 0.0, 0.0, 0.0, 2.5120811462402344]}, "content": {"uri": "Block_L23_845.b3dm"}, "geometricError": 0.010733505710959435, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1619.b3dm"}, "geometricError": 0.02191999740898609, "refine": "REPLACE"}], "content": {"uri": "Block_L20_328.b3dm"}, "geometricError": 0.04671300947666168, "refine": "REPLACE"}, {"boundingVolume": {"box": [-49.364418029785156, -20.744274139404297, -28.769193649291992, 6.494165420532227, 0.0, 0.0, 0.0, 5.7271881103515625, 0.0, 0.0, 0.0, 1.7413578033447266]}, "children": [{"boundingVolume": {"box": [-49.364418029785156, -20.744274139404297, -28.769193649291992, 6.494165420532227, 0.0, 0.0, 0.0, 5.7271881103515625, 0.0, 0.0, 0.0, 1.7413578033447266]}, "children": [{"boundingVolume": {"box": [-52.61150360107422, -20.744274139404297, -28.8245792388916, 3.2470836639404297, 0.0, 0.0, 0.0, 5.7271881103515625, 0.0, 0.0, 0.0, 1.6859722137451172]}, "content": {"uri": "Block_L22_1007.b3dm"}, "geometricError": 0.011116182431578636, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.11733627319336, -20.755062103271484, -29.306705474853516, 3.247081756591797, 0.0, 0.0, 0.0, 5.716400146484375, 0.0, 0.0, 0.0, 1.2038469314575195]}, "content": {"uri": "Block_L23_844.b3dm"}, "geometricError": 0.01081283949315548, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1618.b3dm"}, "geometricError": 0.022031322121620178, "refine": "REPLACE"}], "content": {"uri": "Block_L21_913.b3dm"}, "geometricError": 0.04410001263022423, "refine": "REPLACE"}], "content": {"uri": "Block_L19_173.b3dm"}, "geometricError": 0.0920039564371109, "refine": "REPLACE"}], "content": {"uri": "Block_L18_92.b3dm"}, "geometricError": 0.18821367621421814, "refine": "REPLACE"}}