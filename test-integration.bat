@echo off
echo ========================================
echo MTDT System Integration Test
echo ========================================
echo.

set PASSED=0
set FAILED=0
set TOTAL=0

echo 1. Basic Service Test
echo =================
echo.

echo Testing: Docker Container Status
docker-compose -f docker-compose.internal.yml ps
if %ERRORLEVEL% EQU 0 (
    echo ✅ Docker服务正常
    set /a PASSED+=1
) else (
    echo ❌ Docker服务异常
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo 2. 网络连接测试
echo =================
echo.

echo 测试: 前端开发服务器 (localhost:5174)
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5174' -Method HEAD -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 前端开发服务器正常' -ForegroundColor Green; exit 0 } else { Write-Host '❌ 前端开发服务器异常' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ 前端开发服务器无法访问' -ForegroundColor Red; exit 1 }"
if %ERRORLEVEL% EQU 0 (
    set /a PASSED+=1
) else (
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo 测试: 生产前端服务 (localhost:8080)
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080' -Method HEAD -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 生产前端服务正常' -ForegroundColor Green; exit 0 } else { Write-Host '❌ 生产前端服务异常' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ 生产前端服务无法访问' -ForegroundColor Red; exit 1 }"
if %ERRORLEVEL% EQU 0 (
    set /a PASSED+=1
) else (
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo 测试: 后端API服务 (localhost:8000)
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/health' -Method HEAD -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '✅ 后端API服务正常' -ForegroundColor Green; exit 0 } else { Write-Host '❌ 后端API服务异常' -ForegroundColor Red; exit 1 } } catch { Write-Host '❌ 后端API服务无法访问' -ForegroundColor Red; exit 1 }"
if %ERRORLEVEL% EQU 0 (
    set /a PASSED+=1
) else (
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo 3. 文件系统测试
echo =================
echo.

echo 测试: 关键文件和目录
if exist "frontend\dist" (
    echo ✅ 前端构建产物目录存在
    set /a PASSED+=1
) else (
    echo ❌ 前端构建产物目录不存在
    set /a FAILED+=1
)
set /a TOTAL+=1

if exist "backend\uploads" (
    echo ✅ 文件上传目录存在
    set /a PASSED+=1
) else (
    echo ❌ 文件上传目录不存在
    set /a FAILED+=1
)
set /a TOTAL+=1

if exist ".env" (
    echo ✅ 环境配置文件存在
    set /a PASSED+=1
) else (
    echo ❌ 环境配置文件不存在
    set /a FAILED+=1
)
set /a TOTAL+=1

if exist "docker-compose.internal.yml" (
    echo ✅ 内网部署配置文件存在
    set /a PASSED+=1
) else (
    echo ❌ 内网部署配置文件不存在
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo 4. 功能模块测试
echo =================
echo.

echo 测试: 前端页面加载
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5174' -TimeoutSec 10; if ($response.Content -like '*山思数字平台*' -or $response.Content -like '*MTDT*') { Write-Host '✅ 前端页面内容正常' -ForegroundColor Green; exit 0 } else { Write-Host '✅ 前端页面可访问' -ForegroundColor Green; exit 0 } } catch { Write-Host '❌ 前端页面加载失败' -ForegroundColor Red; exit 1 }"
if %ERRORLEVEL% EQU 0 (
    set /a PASSED+=1
) else (
    set /a FAILED+=1
)
set /a TOTAL+=1
echo.

echo ========================================
echo 测试结果汇总
echo ========================================
echo.
echo 总测试数: %TOTAL%
echo 通过: %PASSED%
echo 失败: %FAILED%

set /a SUCCESS_RATE=(%PASSED% * 100) / %TOTAL%
echo 成功率: %SUCCESS_RATE%%%
echo.

if %FAILED% EQU 0 (
    echo 🎉 所有测试通过！系统运行正常。
    echo.
    echo 系统访问地址:
    echo - 开发环境: http://localhost:5174
    echo - 生产环境: http://localhost:8080
    echo - 后端API: http://localhost:8000/api
    echo.
    echo 建议下一步:
    echo 1. 进行性能测试
    echo 2. 完善文档
    echo 3. 准备生产环境部署
) else (
    echo ⚠️ 有 %FAILED% 个测试失败，请检查系统状态。
    echo.
    echo 故障排除建议:
    echo 1. 检查Docker容器状态: docker-compose -f docker-compose.internal.yml ps
    echo 2. 查看服务日志: docker-compose -f docker-compose.internal.yml logs
    echo 3. 重启服务: docker-compose -f docker-compose.internal.yml restart
)

echo.
echo ========================================
echo 集成测试完成
echo ========================================

pause
