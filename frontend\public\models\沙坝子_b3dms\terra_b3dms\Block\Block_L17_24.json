{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.35644611716270447, "root": {"boundingVolume": {"box": [-37.7677001953125, 161.12411499023438, -62.299072265625, 16.23541259765625, 0.0, 0.0, 0.0, 22.620758056640625, 0.0, 0.0, 0.0, 12.582160949707031]}, "children": [{"boundingVolume": {"box": [-45.885406494140625, 147.92868041992188, -61.545257568359375, 8.117706298828125, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 11.828346252441406]}, "content": {"uri": "Block_L19_230.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-29.649993896484375, 147.92868041992188, -65.62696075439453, 8.117706298828125, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 9.186544418334961]}, "content": {"uri": "Block_L19_229.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-37.7677001953125, 170.5494384765625, -65.15208435058594, 16.23541259765625, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 9.58004379272461]}, "children": [{"boundingVolume": {"box": [-47.23835754394531, 162.85208129882812, -64.93563842773438, 6.7647552490234375, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 8.436506271362305]}, "content": {"uri": "Block_L20_437.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-47.23835754394531, 176.0475311279297, -64.51944732666016, 6.7647552490234375, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 8.928041458129883]}, "children": [{"boundingVolume": {"box": [-47.23835754394531, 176.0475311279297, -64.52193450927734, 6.7647552490234375, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 8.924598693847656]}, "children": [{"boundingVolume": {"box": [-47.23835754394531, 172.1988525390625, -70.39503479003906, 6.7647552490234375, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.852497100830078]}, "children": [{"boundingVolume": {"box": [-50.62073516845703, 172.1988525390625, -69.69877624511719, 3.3823776245117188, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.1562347412109375]}, "content": {"uri": "Block_L22_477.b3dm"}, "geometricError": 0.011427904479205608, "refine": "REPLACE"}, {"boundingVolume": {"box": [-43.855979919433594, 172.1988525390625, -70.92208862304688, 3.3823776245117188, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.3254432678222656]}, "content": {"uri": "Block_L22_476.b3dm"}, "geometricError": 0.01249025296419859, "refine": "REPLACE"}], "content": {"uri": "Block_L21_340.b3dm"}, "geometricError": 0.024203108623623848, "refine": "REPLACE"}, {"boundingVolume": {"box": [-47.23835754394531, 179.89620971679688, -70.49174499511719, 6.7647552490234375, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.949207305908203]}, "children": [{"boundingVolume": {"box": [-50.62073516845703, 179.89620971679688, -70.16395568847656, 3.3823776245117188, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.621417999267578]}, "content": {"uri": "Block_L23_491.b3dm"}, "geometricError": 0.010785499587655067, "refine": "REPLACE"}, {"boundingVolume": {"box": [-43.855979919433594, 179.89620971679688, -71.31495666503906, 3.3823776245117188, 0.0, 0.0, 0.0, 3.8486709594726562, 0.0, 0.0, 0.0, 2.1229400634765625]}, "content": {"uri": "Block_L22_475.b3dm"}, "geometricError": 0.011771457269787788, "refine": "REPLACE"}], "content": {"uri": "Block_L21_339.b3dm"}, "geometricError": 0.02242095395922661, "refine": "REPLACE"}, {"boundingVolume": {"box": [-50.539329528808594, 176.0475311279297, -61.647186279296875, 3.4637832641601562, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 5.895355224609375]}, "children": [{"boundingVolume": {"box": [-50.539329528808594, 171.5574188232422, -61.647186279296875, 3.4637832641601562, 0.0, 0.0, 0.0, 3.2072296142578125, 0.0, 0.0, 0.0, 5.895355224609375]}, "content": {"uri": "Block_L23_490.b3dm"}, "geometricError": 0.010270844213664532, "refine": "REPLACE"}, {"boundingVolume": {"box": [-50.683048248291016, 179.2547607421875, -65.33013153076172, 3.3200645446777344, 0.0, 0.0, 0.0, 4.4901123046875, 0.0, 0.0, 0.0, 2.2124099731445312]}, "content": {"uri": "Block_L23_489.b3dm"}, "geometricError": 0.009898895397782326, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1444.b3dm"}, "geometricError": 0.020287983119487762, "refine": "REPLACE"}], "content": {"uri": "Block_L20_187.b3dm"}, "geometricError": 0.044423311948776245, "refine": "REPLACE"}], "content": {"uri": "Block_L19_101.b3dm"}, "geometricError": 0.08877722918987274, "refine": "REPLACE"}, {"boundingVolume": {"box": [-31.002944946289062, 170.5494384765625, -67.58753204345703, 9.470657348632812, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 7.144601821899414]}, "content": {"uri": "Block_L19_100.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_52.b3dm"}, "geometricError": 0.18525618314743042, "refine": "REPLACE"}], "content": {"uri": "Block_L17_24.b3dm"}, "geometricError": 0.35644611716270447, "refine": "REPLACE"}}