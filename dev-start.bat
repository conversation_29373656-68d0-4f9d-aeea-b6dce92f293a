@echo off
echo Starting MTDT Development Environment...

REM 确保生产服务运行
echo Ensuring production services are running...
docker-compose -f docker-compose.internal.yml up -d

REM 等待服务启动
echo Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM 启动前端开发服务器
echo Starting frontend development server...
cd frontend
start cmd /k "npm run dev"

echo.
echo ========================================
echo Development Environment Ready!
echo ========================================
echo.
echo Production (Internal Users): http://[YOUR_IP]:8080
echo Development (Your Work): http://localhost:5173
echo Backend API: http://localhost:8000/api
echo.
echo Press any key to exit...
pause >nul
