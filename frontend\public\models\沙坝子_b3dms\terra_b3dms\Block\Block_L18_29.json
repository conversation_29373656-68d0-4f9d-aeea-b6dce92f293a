{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.19112397730350494, "root": {"boundingVolume": {"box": [34.51526641845703, 104.22779083251953, -69.16361236572266, 8.645816802978516, 0.0, 0.0, 0.0, 12.943473815917969, 0.0, 0.0, 0.0, 9.221725463867188]}, "children": [{"boundingVolume": {"box": [34.51526641845703, 98.83467102050781, -69.16361236572266, 8.645816802978516, 0.0, 0.0, 0.0, 7.550357818603516, 0.0, 0.0, 0.0, 9.221725463867188]}, "children": [{"boundingVolume": {"box": [34.51526641845703, 98.83467102050781, -73.77421569824219, 8.645816802978516, 0.0, 0.0, 0.0, 7.550357818603516, 0.0, 0.0, 0.0, 4.611122131347656]}, "children": [{"boundingVolume": {"box": [29.471872329711914, 98.83467102050781, -73.77369689941406, 3.6024227142333984, 0.0, 0.0, 0.0, 7.550357818603516, 0.0, 0.0, 0.0, 4.610603332519531]}, "children": [{"boundingVolume": {"box": [29.471872329711914, 95.05949401855469, -73.77369689941406, 3.6024227142333984, 0.0, 0.0, 0.0, 3.775177001953125, 0.0, 0.0, 0.0, 4.610603332519531]}, "content": {"uri": "Block_L22_165.b3dm"}, "geometricError": 0.01351651456207037, "refine": "REPLACE"}, {"boundingVolume": {"box": [29.471872329711914, 102.60984802246094, -73.3575668334961, 3.6024227142333984, 0.0, 0.0, 0.0, 3.7751808166503906, 0.0, 0.0, 0.0, 4.1944732666015625]}, "content": {"uri": "Block_L22_164.b3dm"}, "geometricError": 0.013864976353943348, "refine": "REPLACE"}], "content": {"uri": "Block_L21_183.b3dm"}, "geometricError": 0.02739931456744671, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.11769104003906, 98.83467102050781, -73.58741760253906, 5.043394088745117, 0.0, 0.0, 0.0, 7.550357818603516, 0.0, 0.0, 0.0, 4.424327850341797]}, "children": [{"boundingVolume": {"box": [35.81367492675781, 95.68869018554688, -73.58741760253906, 2.739381790161133, 0.0, 0.0, 0.0, 4.4043731689453125, 0.0, 0.0, 0.0, 4.424327850341797]}, "content": {"uri": "Block_L22_163.b3dm"}, "geometricError": 0.01290314830839634, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.11769104003906, 103.23904418945312, -73.03076171875, 5.043394088745117, 0.0, 0.0, 0.0, 3.145984649658203, 0.0, 0.0, 0.0, 3.8676719665527344]}, "content": {"uri": "Block_L22_162.b3dm"}, "geometricError": 0.013378689996898174, "refine": "REPLACE"}], "content": {"uri": "Block_L21_182.b3dm"}, "geometricError": 0.026334520429372787, "refine": "REPLACE"}], "content": {"uri": "Block_L20_106.b3dm"}, "geometricError": 0.05387352779507637, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.14384841918945, 98.83467102050781, -64.552490234375, 7.01723575592041, 0.0, 0.0, 0.0, 7.550357818603516, 0.0, 0.0, 0.0, 4.610603332519531]}, "children": [{"boundingVolume": {"box": [34.30526351928711, 95.05949401855469, -64.552490234375, 5.102424621582031, 0.0, 0.0, 0.0, 3.775177001953125, 0.0, 0.0, 0.0, 4.610603332519531]}, "children": [{"boundingVolume": {"box": [31.328548431396484, 95.22463989257812, -64.552490234375, 2.1257095336914062, 0.0, 0.0, 0.0, 3.610034942626953, 0.0, 0.0, 0.0, 4.610603332519531]}, "content": {"uri": "Block_L23_224.b3dm"}, "geometricError": 0.010363982990384102, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.43025207519531, 95.05949401855469, -64.552490234375, 2.975992202758789, 0.0, 0.0, 0.0, 3.775177001953125, 0.0, 0.0, 0.0, 4.610603332519531]}, "content": {"uri": "Block_L23_223.b3dm"}, "geometricError": 0.01081114262342453, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1313.b3dm"}, "geometricError": 0.021096719428896904, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.14384841918945, 102.60984802246094, -64.552490234375, 7.01723575592041, 0.0, 0.0, 0.0, 3.7751808166503906, 0.0, 0.0, 0.0, 4.610603332519531]}, "children": [{"boundingVolume": {"box": [32.05046081542969, 101.34849548339844, -64.552490234375, 2.9238481521606445, 0.0, 0.0, 0.0, 2.5138206481933594, 0.0, 0.0, 0.0, 4.610603332519531]}, "content": {"uri": "Block_L23_222.b3dm"}, "geometricError": 0.010446837171912193, "refine": "REPLACE"}, {"boundingVolume": {"box": [39.06769561767578, 102.60984802246094, -64.552490234375, 4.093387603759766, 0.0, 0.0, 0.0, 3.7751808166503906, 0.0, 0.0, 0.0, 4.610603332519531]}, "content": {"uri": "Block_L23_221.b3dm"}, "geometricError": 0.01066612545400858, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1312.b3dm"}, "geometricError": 0.02113792859017849, "refine": "REPLACE"}], "content": {"uri": "Block_L21_764.b3dm"}, "geometricError": 0.04233165457844734, "refine": "REPLACE"}], "content": {"uri": "Block_L19_56.b3dm"}, "geometricError": 0.09437286853790283, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.51526641845703, 111.77815246582031, -68.24224853515625, 8.645816802978516, 0.0, 0.0, 0.0, 5.393115997314453, 0.0, 0.0, 0.0, 8.300357818603516]}, "content": {"uri": "Block_L19_55.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_29.b3dm"}, "geometricError": 0.19112397730350494, "refine": "REPLACE"}}