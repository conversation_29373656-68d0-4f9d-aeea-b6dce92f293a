import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Pool, PoolClient } from 'pg';
import {
  TestConnectionDto,
  QueryLayerDto,
  DatabaseTableInfo,
  GeometryColumnInfo,
  ConnectionTestResult,
  LayerQueryResult,
} from './dto/database-connection.dto';

@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);
  private connections = new Map<string, Pool>();

  /**
   * 测试数据库连接
   */
  async testConnection(testDto: TestConnectionDto): Promise<ConnectionTestResult> {
    let client: PoolClient | undefined;
    let pool: Pool | undefined;

    try {
      // 创建临时连接池
      pool = new Pool({
        host: testDto.host,
        port: testDto.port,
        database: testDto.database,
        user: testDto.username,
        password: testDto.password,
        max: 1,
        connectionTimeoutMillis: 5000,
        idleTimeoutMillis: 5000,
      });

      // 获取连接并测试
      client = await pool.connect();
      
      // 测试基本连接
      const versionResult = await client.query('SELECT version()');
      const version = versionResult.rows[0].version;

      // 检查PostGIS扩展
      let postgisVersion: string | undefined;
      try {
        const postgisResult = await client.query('SELECT PostGIS_Version()');
        postgisVersion = postgisResult.rows[0].postgis_version;
      } catch (error) {
        this.logger.warn('PostGIS extension not found or not accessible');
      }

      return {
        success: true,
        message: '数据库连接成功',
        version,
        postgisVersion,
      };

    } catch (error) {
      this.logger.error('Database connection test failed:', error);

      let errorMessage = '连接失败';
      if (error.code === 'ECONNREFUSED') {
        errorMessage = '连接被拒绝，请检查数据库服务是否启动和网络连接';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = '无法找到主机，请检查主机地址是否正确';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = '连接超时，请检查网络连接和防火墙设置';
      } else if (error.message.includes('password authentication failed')) {
        errorMessage = '用户名或密码错误';
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        errorMessage = '数据库不存在';
      } else {
        errorMessage = `连接失败: ${error.message}`;
      }

      return {
        success: false,
        message: errorMessage,
      };
    } finally {
      if (client) {
        client.release();
      }
      if (pool) {
        await pool.end();
      }
    }
  }

  /**
   * 获取数据库中的空间表列表
   */
  async getTables(testDto: TestConnectionDto): Promise<DatabaseTableInfo[]> {
    let client: PoolClient | undefined;
    let pool: Pool | undefined;

    try {
      pool = new Pool({
        host: testDto.host,
        port: testDto.port,
        database: testDto.database,
        user: testDto.username,
        password: testDto.password,
        max: 1,
        connectionTimeoutMillis: 10000,
      });

      client = await pool.connect();

      // 查询所有表和视图
      const tablesQuery = `
        SELECT 
          t.table_name,
          t.table_schema,
          t.table_type
        FROM information_schema.tables t
        WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        ORDER BY t.table_schema, t.table_name
      `;

      const tablesResult = await client.query(tablesQuery);
      const tables: DatabaseTableInfo[] = [];

      for (const row of tablesResult.rows) {
        const tableName = row.table_name;
        const schema = row.table_schema;
        const tableType = row.table_type === 'VIEW' ? 'view' : 'table';

        // 查询几何列信息
        const geometryColumns = await this.getGeometryColumns(client, schema, tableName);

        // 只包含有几何列的表
        if (geometryColumns.length > 0) {
          // 获取行数（可选，对大表可能较慢）
          let totalRows: number | undefined;
          try {
            const countResult = await client.query(
              `SELECT COUNT(*) as count FROM "${schema}"."${tableName}"`
            );
            totalRows = parseInt(countResult.rows[0].count);
          } catch (error) {
            this.logger.warn(`Failed to get row count for ${schema}.${tableName}:`, error.message);
          }

          tables.push({
            name: tableName,
            schema,
            type: tableType,
            geometryColumns,
            totalRows,
          });
        }
      }

      return tables;

    } catch (error) {
      this.logger.error('Failed to get tables:', error);
      throw new BadRequestException(`获取表列表失败: ${error.message}`);
    } finally {
      if (client) {
        client.release();
      }
      if (pool) {
        await pool.end();
      }
    }
  }

  /**
   * 查询空间数据并返回GeoJSON
   */
  async queryLayer(queryDto: QueryLayerDto, connectionConfig: TestConnectionDto): Promise<LayerQueryResult> {
    let client: PoolClient | undefined;
    let pool: Pool | undefined;

    try {
      pool = new Pool({
        host: connectionConfig.host,
        port: connectionConfig.port,
        database: connectionConfig.database,
        user: connectionConfig.username,
        password: connectionConfig.password,
        max: 1,
        connectionTimeoutMillis: 30000,
      });

      client = await pool.connect();

      // 解析表名和模式
      const [schema, tableName] = this.parseTableName(queryDto.tableName);
      
      // 获取几何列信息
      const geometryColumns = await this.getGeometryColumns(client, schema, tableName);
      
      if (geometryColumns.length === 0) {
        throw new BadRequestException('表中没有找到几何列');
      }

      // 使用指定的几何列或第一个几何列
      const geometryColumn = queryDto.geometryColumn || geometryColumns[0].columnName;

      // 构建查询语句
      const whereClause = queryDto.whereClause ? `WHERE ${queryDto.whereClause}` : '';
      const limitClause = queryDto.limit ? `LIMIT ${queryDto.limit}` : '';

      const query = `
        SELECT 
          ST_AsGeoJSON(ST_Transform("${geometryColumn}", 4326)) as geometry,
          *
        FROM "${schema}"."${tableName}"
        ${whereClause}
        ${limitClause}
      `;

      this.logger.log(`Executing query: ${query}`);
      const result = await client.query(query);

      // 转换为GeoJSON格式
      const features = result.rows.map(row => {
        const { geometry, ...properties } = row;
        
        // 移除几何列的原始数据
        delete properties[geometryColumn];
        
        return {
          type: 'Feature',
          geometry: JSON.parse(geometry),
          properties,
        };
      });

      const geojson = {
        type: 'FeatureCollection',
        features,
      };

      // 计算边界（可选）
      let bounds: [number, number, number, number] | undefined;
      if (features.length > 0) {
        bounds = this.calculateBounds(features);
      }

      return {
        success: true,
        data: geojson,
        totalFeatures: features.length,
        bounds,
      };

    } catch (error) {
      this.logger.error('Failed to query layer:', error);
      return {
        success: false,
        error: `查询图层失败: ${error.message}`,
      };
    } finally {
      if (client) {
        client.release();
      }
      if (pool) {
        await pool.end();
      }
    }
  }

  /**
   * 获取表的几何列信息
   */
  private async getGeometryColumns(
    client: PoolClient,
    schema: string,
    tableName: string
  ): Promise<GeometryColumnInfo[]> {
    try {
      // 查询PostGIS几何列信息
      const query = `
        SELECT 
          f_geometry_column as column_name,
          type as geometry_type,
          srid,
          coord_dimension as dimension
        FROM geometry_columns 
        WHERE f_table_schema = $1 AND f_table_name = $2
      `;

      const result = await client.query(query, [schema, tableName]);
      
      return result.rows.map(row => ({
        columnName: row.column_name,
        geometryType: row.geometry_type,
        srid: row.srid,
        dimension: row.dimension,
      }));

    } catch (error) {
      // 如果geometry_columns表不存在，尝试从information_schema查询
      this.logger.warn('geometry_columns table not accessible, trying alternative method');
      
      try {
        const fallbackQuery = `
          SELECT column_name
          FROM information_schema.columns 
          WHERE table_schema = $1 
            AND table_name = $2 
            AND udt_name = 'geometry'
        `;
        
        const result = await client.query(fallbackQuery, [schema, tableName]);
        
        return result.rows.map(row => ({
          columnName: row.column_name,
          geometryType: 'UNKNOWN',
          srid: 4326,
          dimension: 2,
        }));
      } catch (fallbackError) {
        this.logger.warn('Failed to get geometry columns:', fallbackError.message);
        return [];
      }
    }
  }

  /**
   * 解析表名，支持 schema.table 格式
   */
  private parseTableName(fullTableName: string): [string, string] {
    const parts = fullTableName.split('.');
    if (parts.length === 2) {
      return [parts[0], parts[1]];
    }
    return ['public', fullTableName];
  }

  /**
   * 计算要素集合的边界
   */
  private calculateBounds(features: any[]): [number, number, number, number] {
    let minLng = Infinity;
    let minLat = Infinity;
    let maxLng = -Infinity;
    let maxLat = -Infinity;

    features.forEach(feature => {
      if (feature.geometry && feature.geometry.coordinates) {
        this.extractCoordinates(feature.geometry.coordinates).forEach(([lng, lat]) => {
          minLng = Math.min(minLng, lng);
          minLat = Math.min(minLat, lat);
          maxLng = Math.max(maxLng, lng);
          maxLat = Math.max(maxLat, lat);
        });
      }
    });

    return [minLng, minLat, maxLng, maxLat];
  }

  /**
   * 获取表的详细信息
   */
  async getTableInfo(connectionConfig: TestConnectionDto, tableName: string): Promise<any> {
    let client: PoolClient | undefined;
    let pool: Pool | undefined;

    try {
      pool = new Pool({
        host: connectionConfig.host,
        port: connectionConfig.port,
        database: connectionConfig.database,
        user: connectionConfig.username,
        password: connectionConfig.password,
        max: 1,
        connectionTimeoutMillis: 10000,
      });

      client = await pool.connect();
      const [schema, table] = this.parseTableName(tableName);

      // 获取表的基本信息
      const tableInfoQuery = `
        SELECT
          t.table_name,
          t.table_schema,
          t.table_type,
          obj_description(c.oid) as table_comment
        FROM information_schema.tables t
        LEFT JOIN pg_class c ON c.relname = t.table_name
        LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.table_schema
        WHERE t.table_schema = $1 AND t.table_name = $2
      `;

      const tableInfo = await client.query(tableInfoQuery, [schema, table]);

      if (tableInfo.rows.length === 0) {
        throw new BadRequestException(`表 ${tableName} 不存在`);
      }

      // 获取列信息
      const columnsQuery = `
        SELECT
          column_name,
          data_type,
          is_nullable,
          column_default,
          character_maximum_length,
          numeric_precision,
          numeric_scale
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = $2
        ORDER BY ordinal_position
      `;

      const columns = await client.query(columnsQuery, [schema, table]);

      // 获取几何列信息
      const geometryColumns = await this.getGeometryColumns(client, schema, table);

      // 获取行数
      let totalRows: number | undefined;
      try {
        const countResult = await client.query(`SELECT COUNT(*) as count FROM "${schema}"."${table}"`);
        totalRows = parseInt(countResult.rows[0].count);
      } catch (error) {
        this.logger.warn(`Failed to get row count for ${schema}.${table}:`, error.message);
      }

      return {
        table: tableInfo.rows[0],
        columns: columns.rows,
        geometryColumns,
        totalRows,
      };

    } catch (error) {
      this.logger.error('Failed to get table info:', error);
      throw new BadRequestException(`获取表信息失败: ${error.message}`);
    } finally {
      if (client) {
        client.release();
      }
      if (pool) {
        await pool.end();
      }
    }
  }

  /**
   * 递归提取坐标
   */
  private extractCoordinates(coords: any): number[][] {
    if (typeof coords[0] === 'number') {
      return [coords];
    }

    const result: number[][] = [];
    coords.forEach((coord: any) => {
      result.push(...this.extractCoordinates(coord));
    });

    return result;
  }
}
