{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.08915498107671738, "root": {"boundingVolume": {"box": [-74.87721252441406, 149.81373596191406, -50.078330993652344, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 5.206487655639648]}, "children": [{"boundingVolume": {"box": [-74.87721252441406, 144.15853881835938, -51.592376708984375, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 3.692441940307617]}, "children": [{"boundingVolume": {"box": [-77.77639770507812, 144.15853881835938, -51.592376708984375, 4.0588531494140625, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 3.692441940307617]}, "children": [{"boundingVolume": {"box": [-77.77639770507812, 142.2734832763672, -51.99454879760742, 4.0588531494140625, 0.0, 0.0, 0.0, 3.7701263427734375, 0.0, 0.0, 0.0, 3.290271759033203]}, "content": {"uri": "Block_L22_600.b3dm"}, "geometricError": 0.01190864760428667, "refine": "REPLACE"}, {"boundingVolume": {"box": [-77.77639770507812, 147.92868041992188, -51.592376708984375, 4.0588531494140625, 0.0, 0.0, 0.0, 1.8850631713867188, 0.0, 0.0, 0.0, 3.692441940307617]}, "content": {"uri": "Block_L22_599.b3dm"}, "geometricError": 0.01155612152069807, "refine": "REPLACE"}], "content": {"uri": "Block_L21_400.b3dm"}, "geometricError": 0.023419925943017006, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.818359375, 144.15853881835938, -51.79503631591797, 2.899181365966797, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 3.489786148071289]}, "children": [{"boundingVolume": {"box": [-70.818359375, 141.33094787597656, -52.436790466308594, 2.899181365966797, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.8480300903320312]}, "content": {"uri": "Block_L22_598.b3dm"}, "geometricError": 0.011737430468201637, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.818359375, 146.98614501953125, -53.83074188232422, 2.899181365966797, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 1.4540767669677734]}, "content": {"uri": "Block_L22_597.b3dm"}, "geometricError": 0.011492995545268059, "refine": "REPLACE"}, {"boundingVolume": {"box": [-70.818359375, 146.98614501953125, -50.34095764160156, 2.899181365966797, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.0357093811035156]}, "content": {"uri": "Block_L23_588.b3dm"}, "geometricError": 0.010962801985442638, "refine": "REPLACE"}], "content": {"uri": "Block_L21_399.b3dm"}, "geometricError": 0.02294514887034893, "refine": "REPLACE"}], "content": {"uri": "Block_L20_219.b3dm"}, "geometricError": 0.04638379439711571, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.35623168945312, 155.46893310546875, -50.078330993652344, 3.4790191650390625, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 5.206487655639648]}, "children": [{"boundingVolume": {"box": [-78.35623168945312, 152.64132690429688, -50.08131790161133, 3.4790191650390625, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 5.203502655029297]}, "children": [{"boundingVolume": {"box": [-78.35623168945312, 152.64132690429688, -53.116695404052734, 3.4790191650390625, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.1681251525878906]}, "content": {"uri": "Block_L22_596.b3dm"}, "geometricError": 0.011208420619368553, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.65666198730469, 152.64132690429688, -47.91319274902344, 3.1785850524902344, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 3.0353775024414062]}, "content": {"uri": "Block_L23_587.b3dm"}, "geometricError": 0.010608742013573647, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1488.b3dm"}, "geometricError": 0.02208424173295498, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.35623168945312, 158.29652404785156, -51.24945831298828, 3.4790191650390625, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 4.035360336303711]}, "children": [{"boundingVolume": {"box": [-78.35623168945312, 158.29652404785156, -53.9683952331543, 3.4790191650390625, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 1.3164253234863281]}, "content": {"uri": "Block_L23_586.b3dm"}, "geometricError": 0.010336214676499367, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.35623168945312, 158.29652404785156, -50.01911926269531, 3.4790191650390625, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.6328506469726562]}, "content": {"uri": "Block_L23_585.b3dm"}, "geometricError": 0.010567414574325085, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1487.b3dm"}, "geometricError": 0.020730959251523018, "refine": "REPLACE"}], "content": {"uri": "Block_L21_851.b3dm"}, "geometricError": 0.04293113574385643, "refine": "REPLACE"}, {"boundingVolume": {"box": [-71.398193359375, 155.46893310546875, -52.92455291748047, 3.479015350341797, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 2.360269546508789]}, "children": [{"boundingVolume": {"box": [-71.398193359375, 152.64132690429688, -52.933250427246094, 3.479015350341797, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.351572036743164]}, "children": [{"boundingVolume": {"box": [-73.13770294189453, 152.64132690429688, -52.93952941894531, 1.7395095825195312, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.3452911376953125]}, "content": {"uri": "Block_L23_584.b3dm"}, "geometricError": 0.010614750906825066, "refine": "REPLACE"}, {"boundingVolume": {"box": [-69.65869140625, 152.64132690429688, -53.736793518066406, 1.7395057678222656, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 1.5480270385742188]}, "content": {"uri": "Block_L23_583.b3dm"}, "geometricError": 0.011033831164240837, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1486.b3dm"}, "geometricError": 0.021647894755005836, "refine": "REPLACE"}, {"boundingVolume": {"box": [-71.398193359375, 158.29652404785156, -53.68706512451172, 3.479015350341797, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 1.597757339477539]}, "children": [{"boundingVolume": {"box": [-71.398193359375, 158.29652404785156, -53.68706512451172, 3.479015350341797, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 1.597757339477539]}, "content": {"uri": "Block_L23_582.b3dm"}, "geometricError": 0.01061700377613306, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1485.b3dm"}, "geometricError": 0.02122829668223858, "refine": "REPLACE"}], "content": {"uri": "Block_L21_850.b3dm"}, "geometricError": 0.04297521710395813, "refine": "REPLACE"}], "content": {"uri": "Block_L19_113.b3dm"}, "geometricError": 0.08915498107671738, "refine": "REPLACE"}}