{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.1726873368024826, "root": {"boundingVolume": {"box": [81.53939056396484, -15.415153503417969, -70.95889282226562, 13.637748718261719, 0.0, 0.0, 0.0, 20.20313262939453, 0.0, 0.0, 0.0, 12.401655197143555]}, "children": [{"boundingVolume": {"box": [81.53939056396484, -23.833126068115234, -74.63900756835938, 13.637748718261719, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 8.721542358398438]}, "children": [{"boundingVolume": {"box": [74.72052001953125, -23.833126068115234, -75.50595092773438, 6.818874359130859, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 7.849311828613281]}, "children": [{"boundingVolume": {"box": [74.72052001953125, -28.743610382080078, -77.12825012207031, 6.818874359130859, 0.0, 0.0, 0.0, 6.874676704406738, 0.0, 0.0, 0.0, 6.21685791015625]}, "content": {"uri": "Block_L21_38.b3dm"}, "geometricError": 0.023652823641896248, "refine": "REPLACE"}, {"boundingVolume": {"box": [74.72052001953125, -16.958450317382812, -75.10865783691406, 6.818874359130859, 0.0, 0.0, 0.0, 4.910483360290527, 0.0, 0.0, 0.0, 7.444980621337891]}, "content": {"uri": "Block_L21_37.b3dm"}, "geometricError": 0.022208258509635925, "refine": "REPLACE"}], "content": {"uri": "Block_L20_38.b3dm"}, "geometricError": 0.04577552154660225, "refine": "REPLACE"}, {"boundingVolume": {"box": [88.35826110839844, -29.725706100463867, -74.36619567871094, 6.818874359130859, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 8.436710357666016]}, "children": [{"boundingVolume": {"box": [88.35826110839844, -29.725706100463867, -77.88309478759766, 6.818874359130859, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 4.9198150634765625]}, "content": {"uri": "Block_L21_36.b3dm"}, "geometricError": 0.02460680529475212, "refine": "REPLACE"}, {"boundingVolume": {"box": [88.35826110839844, -29.725706100463867, -69.44912719726562, 6.818874359130859, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 3.5141525268554688]}, "content": {"uri": "Block_L21_35.b3dm"}, "geometricError": 0.02241555228829384, "refine": "REPLACE"}], "content": {"uri": "Block_L20_37.b3dm"}, "geometricError": 0.04676001891493797, "refine": "REPLACE"}, {"boundingVolume": {"box": [88.35826110839844, -17.9405460357666, -72.9041519165039, 6.818874359130859, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 6.554725646972656]}, "children": [{"boundingVolume": {"box": [84.38058471679688, -17.9405460357666, -72.89541625976562, 2.841197967529297, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 6.545986175537109]}, "content": {"uri": "Block_L21_34.b3dm"}, "geometricError": 0.022434435784816742, "refine": "REPLACE"}, {"boundingVolume": {"box": [91.199462890625, -17.9405460357666, -71.56849670410156, 3.9776763916015625, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 5.193641662597656]}, "content": {"uri": "Block_L21_33.b3dm"}, "geometricError": 0.022865522652864456, "refine": "REPLACE"}], "content": {"uri": "Block_L20_36.b3dm"}, "geometricError": 0.045274168252944946, "refine": "REPLACE"}], "content": {"uri": "Block_L19_19.b3dm"}, "geometricError": 0.09200754016637802, "refine": "REPLACE"}, {"boundingVolume": {"box": [81.53939056396484, -3.629993438720703, -70.26535034179688, 13.637748718261719, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 11.708112716674805]}, "children": [{"boundingVolume": {"box": [73.58403778076172, -7.137482166290283, -76.66189575195312, 5.682395935058594, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 5.311573028564453]}, "children": [{"boundingVolume": {"box": [71.21636962890625, -7.137482166290283, -79.13845825195312, 3.3147315979003906, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 2.8323402404785156]}, "content": {"uri": "Block_L22_350.b3dm"}, "geometricError": 0.019235284999012947, "refine": "REPLACE"}, {"boundingVolume": {"box": [76.89877319335938, -7.137482166290283, -75.24626159667969, 2.367664337158203, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 3.893299102783203]}, "content": {"uri": "Block_L22_349.b3dm"}, "geometricError": 0.021870050579309464, "refine": "REPLACE"}], "content": {"uri": "Block_L21_275.b3dm"}, "geometricError": 0.04129650071263313, "refine": "REPLACE"}, {"boundingVolume": {"box": [73.58403778076172, 1.2804903984069824, -74.03526306152344, 5.682395935058594, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 6.170734405517578]}, "children": [{"boundingVolume": {"box": [73.58403778076172, 1.2804903984069824, -77.61707305908203, 5.682395935058594, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 2.5664596557617188]}, "content": {"uri": "Block_L22_348.b3dm"}, "geometricError": 0.01684350147843361, "refine": "REPLACE"}, {"boundingVolume": {"box": [75.09033966064453, 1.2804903984069824, -71.45757293701172, 4.176094055175781, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 3.5930404663085938]}, "content": {"uri": "Block_L22_347.b3dm"}, "geometricError": 0.01655912585556507, "refine": "REPLACE"}], "content": {"uri": "Block_L21_274.b3dm"}, "geometricError": 0.03349505737423897, "refine": "REPLACE"}, {"boundingVolume": {"box": [87.22178649902344, -3.629993438720703, -68.46428680419922, 7.955352783203125, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 9.907045364379883]}, "children": [{"boundingVolume": {"box": [87.22178649902344, -7.838979721069336, -73.21949768066406, 7.955352783203125, 0.0, 0.0, 0.0, 4.208986282348633, 0.0, 0.0, 0.0, 4.8820343017578125]}, "content": {"uri": "Block_L21_32.b3dm"}, "geometricError": 0.023313699290156364, "refine": "REPLACE"}, {"boundingVolume": {"box": [84.31672668457031, 0.5789928436279297, -72.20004272460938, 5.050289154052734, 0.0, 0.0, 0.0, 4.208986282348633, 0.0, 0.0, 0.0, 3.8625831604003906]}, "content": {"uri": "Block_L22_346.b3dm"}, "geometricError": 0.020931504666805267, "refine": "REPLACE"}, {"boundingVolume": {"box": [87.22178649902344, -2.4619174003601074, -63.45542907714844, 7.955352783203125, 0.0, 0.0, 0.0, 7.24989652633667, 0.0, 0.0, 0.0, 4.88203239440918]}, "content": {"uri": "Block_L22_345.b3dm"}, "geometricError": 0.021215416491031647, "refine": "REPLACE"}], "content": {"uri": "Block_L21_273.b3dm"}, "geometricError": 0.043574538081884384, "refine": "REPLACE"}], "content": {"uri": "Block_L20_151.b3dm"}, "geometricError": 0.08086259663105011, "refine": "REPLACE"}], "content": {"uri": "Block_L19_81.b3dm"}, "geometricError": 0.1726873368024826, "refine": "REPLACE"}}