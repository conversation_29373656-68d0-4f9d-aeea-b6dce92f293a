$body = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -ContentType "application/json" -Body $body
    Write-Host "Success: $($response.success)"
    Write-Host "Message: $($response.message)"
    if ($response.postgisVersion) {
        Write-Host "PostGIS Version: $($response.postgisVersion)"
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Status: $($_.Exception.Response.StatusCode)"
}
