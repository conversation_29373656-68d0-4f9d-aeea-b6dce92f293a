<template>
  <teleport to="body">
    <transition name="loading-fade">
      <div v-if="showLoading" class="global-loading-overlay">
        <div class="loading-container">
          <!-- 主加载指示器 -->
          <div class="loading-spinner">
            <el-icon class="is-loading" :size="40">
              <Loading />
            </el-icon>
          </div>
          
          <!-- 加载消息 -->
          <div class="loading-message">
            {{ currentMessage }}
          </div>
          
          <!-- 进度条（如果有进度信息） -->
          <div v-if="hasProgress" class="loading-progress">
            <el-progress 
              :percentage="currentProgress" 
              :show-text="false"
              :stroke-width="4"
            />
            <div class="progress-text">{{ currentProgress }}%</div>
          </div>
          
          <!-- 多个加载任务列表 -->
          <div v-if="loadingTasks.length > 1" class="loading-tasks">
            <div class="tasks-header">正在执行的任务：</div>
            <div 
              v-for="task in loadingTasks" 
              :key="task.id"
              class="task-item"
            >
              <el-icon class="task-icon is-loading" :size="16">
                <Loading />
              </el-icon>
              <span class="task-message">{{ task.message }}</span>
              <div v-if="task.progress !== undefined" class="task-progress">
                {{ task.progress }}%
              </div>
            </div>
          </div>
          
          <!-- 取消按钮（可选） -->
          <div v-if="showCancelButton" class="loading-actions">
            <el-button size="small" @click="handleCancel">
              取消
            </el-button>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { useUIStore } from '../../stores/ui'

const uiStore = useUIStore()

// 定义props
interface Props {
  showCancelButton?: boolean
  cancelable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showCancelButton: false,
  cancelable: false
})

// 定义事件
const emit = defineEmits<{
  cancel: []
}>()

// 计算属性
const showLoading = computed(() => uiStore.globalLoading)

const loadingTasks = computed(() => {
  return Array.from(uiStore.loadingStates.values())
})

const currentMessage = computed(() => {
  if (loadingTasks.value.length === 0) return '加载中...'
  if (loadingTasks.value.length === 1) return loadingTasks.value[0].message
  return `正在执行 ${loadingTasks.value.length} 个任务...`
})

const hasProgress = computed(() => {
  return loadingTasks.value.some(task => task.progress !== undefined)
})

const currentProgress = computed(() => {
  const tasksWithProgress = loadingTasks.value.filter(task => task.progress !== undefined)
  if (tasksWithProgress.length === 0) return 0
  
  const totalProgress = tasksWithProgress.reduce((sum, task) => sum + (task.progress || 0), 0)
  return Math.round(totalProgress / tasksWithProgress.length)
})

// 处理取消
const handleCancel = () => {
  if (props.cancelable) {
    emit('cancel')
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.cancelable) {
    handleCancel()
  }
}

// 监听键盘事件
if (props.cancelable) {
  document.addEventListener('keydown', handleKeydown)
}
</script>

<style scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-container {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  text-align: center;
  min-width: 280px;
  max-width: 400px;
}

.loading-spinner {
  margin-bottom: 16px;
}

.loading-spinner .el-icon {
  color: #409EFF;
}

.loading-message {
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
  font-weight: 500;
}

.loading-progress {
  margin-bottom: 16px;
}

.progress-text {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}

.loading-tasks {
  margin-top: 16px;
  text-align: left;
}

.tasks-header {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-size: 13px;
}

.task-icon {
  margin-right: 8px;
  color: #409EFF;
}

.task-message {
  flex: 1;
  color: #333;
}

.task-progress {
  margin-left: 8px;
  color: #666;
  font-size: 12px;
}

.loading-actions {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

/* 过渡动画 */
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: opacity 0.3s ease;
}

.loading-fade-enter-from,
.loading-fade-leave-to {
  opacity: 0;
}

.loading-fade-enter-to,
.loading-fade-leave-from {
  opacity: 1;
}
</style>
