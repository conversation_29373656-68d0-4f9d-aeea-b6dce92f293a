import type { GeoFeature, GeoPoint, FeatureType } from './types'
import type { Feature, FeatureCollection, Geometry } from 'geojson'

/**
 * 要素格式转换工具
 * 用于在GeoFeature和GeoJSON格式之间进行转换
 */
export class FeatureConverter {
  
  /**
   * 将GeoFeature数组转换为GeoJSON FeatureCollection
   */
  static convertToGeoJSON(features: GeoFeature[]): FeatureCollection {
    if (!features || features.length === 0) {
      return {
        type: 'FeatureCollection',
        features: []
      }
    }

    const geoJsonFeatures: Feature[] = features.map(feature => {
      return this.convertSingleFeatureToGeoJSON(feature)
    }).filter(feature => feature !== null) as Feature[]

    return {
      type: 'FeatureCollection',
      features: geoJsonFeatures
    }
  }

  /**
   * 将单个GeoFeature转换为GeoJSON Feature
   */
  static convertSingleFeatureToGeoJSON(feature: GeoFeature): Feature | null {
    try {
      const geometry = this.convertGeometry(feature.type, feature.points)
      if (!geometry) {
        console.warn(`无法转换要素几何: ${feature.id}, 类型: ${feature.type}`)
        return null
      }

      return {
        type: 'Feature',
        id: feature.id,
        geometry,
        properties: {
          ...feature.properties,
          originalType: feature.type,
          timestamp: feature.timestamp.toISOString(),
          featureId: feature.id
        }
      }
    } catch (error) {
      console.error(`转换要素失败: ${feature.id}`, error)
      return null
    }
  }

  /**
   * 根据要素类型和点数组转换几何体
   */
  private static convertGeometry(type: FeatureType, points: GeoPoint[]): Geometry | null {
    if (!points || points.length === 0) {
      return null
    }

    switch (type) {
      case 'point':
        return this.convertPointGeometry(points)
      
      case 'line':
        return this.convertLineGeometry(points)
      
      case 'polygon':
      case 'rectangle':
      case 'circle':
        return this.convertPolygonGeometry(points)
      
      default:
        console.warn(`不支持的要素类型: ${type}`)
        return null
    }
  }

  /**
   * 转换点几何体
   */
  private static convertPointGeometry(points: GeoPoint[]): Geometry {
    const point = points[0]
    return {
      type: 'Point',
      coordinates: [point.longitude, point.latitude]
    }
  }

  /**
   * 转换线几何体
   */
  private static convertLineGeometry(points: GeoPoint[]): Geometry {
    const coordinates = points.map(point => [point.longitude, point.latitude])
    return {
      type: 'LineString',
      coordinates
    }
  }

  /**
   * 转换面几何体
   */
  private static convertPolygonGeometry(points: GeoPoint[]): Geometry {
    let coordinates = points.map(point => [point.longitude, point.latitude])
    
    // 确保多边形是闭合的
    if (coordinates.length > 0) {
      const firstPoint = coordinates[0]
      const lastPoint = coordinates[coordinates.length - 1]
      
      if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
        coordinates.push([firstPoint[0], firstPoint[1]])
      }
    }

    return {
      type: 'Polygon',
      coordinates: [coordinates]
    }
  }

  /**
   * 验证转换后的GeoJSON是否有效
   */
  static validateGeoJSON(geojson: FeatureCollection): boolean {
    try {
      if (!geojson || geojson.type !== 'FeatureCollection') {
        return false
      }

      if (!Array.isArray(geojson.features)) {
        return false
      }

      // 检查每个要素
      for (const feature of geojson.features) {
        if (!feature || feature.type !== 'Feature') {
          return false
        }

        if (!feature.geometry || !feature.geometry.type) {
          return false
        }

        if (!Array.isArray(feature.geometry.coordinates)) {
          return false
        }
      }

      return true
    } catch (error) {
      console.error('GeoJSON验证失败:', error)
      return false
    }
  }

  /**
   * 获取要素统计信息
   */
  static getFeatureStatistics(features: GeoFeature[]): {
    total: number
    byType: Record<FeatureType, number>
    types: FeatureType[]
  } {
    const stats = {
      total: features.length,
      byType: {} as Record<FeatureType, number>,
      types: [] as FeatureType[]
    }

    // 统计各类型数量
    features.forEach(feature => {
      if (!stats.byType[feature.type]) {
        stats.byType[feature.type] = 0
        stats.types.push(feature.type)
      }
      stats.byType[feature.type]++
    })

    return stats
  }

  /**
   * 创建要素信息摘要
   */
  static createFeatureSummary(features: GeoFeature[]): string {
    const stats = this.getFeatureStatistics(features)
    
    if (stats.total === 0) {
      return '未选择要素'
    }

    const typeSummary = stats.types.map(type => {
      const count = stats.byType[type]
      const typeName = this.getFeatureTypeName(type)
      return `${typeName}: ${count}个`
    }).join(', ')

    return `共${stats.total}个要素 (${typeSummary})`
  }

  /**
   * 获取要素类型的中文名称
   */
  private static getFeatureTypeName(type: FeatureType): string {
    const typeNames: Record<FeatureType, string> = {
      point: '点',
      line: '线',
      polygon: '面',
      circle: '圆',
      rectangle: '矩形'
    }
    return typeNames[type] || type
  }
}
