@echo off
title MTDT System Health Check
color 0E

echo ========================================
echo      MTDT System Health Check
echo ========================================
echo.

set TOTAL_CHECKS=0
set PASSED_CHECKS=0
set FAILED_CHECKS=0

REM Function to perform a check
goto :start_checks

:check
set /a TOTAL_CHECKS+=1
echo [CHECK %TOTAL_CHECKS%] %~1
%~2 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [PASS] %~1
    set /a PASSED_CHECKS+=1
) else (
    echo [FAIL] %~1
    set /a FAILED_CHECKS+=1
)
echo.
goto :eof

:start_checks

echo 1. Infrastructure Checks
echo =========================
echo.

call :check "Docker service availability" "docker --version"
call :check "Docker Compose availability" "docker-compose --version"
call :check "Configuration file exists" "if exist docker-compose.internal.yml (exit 0) else (exit 1)"
call :check "Environment file exists" "if exist .env (exit 0) else (exit 1)"

echo 2. Container Health Checks
echo ===========================
echo.

call :check "PostgreSQL container running" "docker-compose -f docker-compose.internal.yml ps postgres | findstr Up"
call :check "Redis container running" "docker-compose -f docker-compose.internal.yml ps redis | findstr Up"
call :check "Backend container running" "docker-compose -f docker-compose.internal.yml ps backend | findstr Up"
call :check "Frontend container running" "docker-compose -f docker-compose.internal.yml ps frontend | findstr Up"

echo 3. Network Connectivity Checks
echo ===============================
echo.

call :check "Frontend port 8080 accessible" "powershell -Command \"try { $r = Invoke-WebRequest -Uri 'http://localhost:8080' -Method HEAD -TimeoutSec 5; exit 0 } catch { exit 1 }\""
call :check "Backend port 8000 accessible" "powershell -Command \"try { $r = Invoke-WebRequest -Uri 'http://localhost:8000/api/health' -Method HEAD -TimeoutSec 5; exit 0 } catch { exit 1 }\""
call :check "Database port 5432 accessible" "powershell -Command \"try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 5432); $tcp.Close(); exit 0 } catch { exit 1 }\""
call :check "Redis port 6379 accessible" "powershell -Command \"try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 6379); $tcp.Close(); exit 0 } catch { exit 1 }\""

echo 4. Application Health Checks
echo =============================
echo.

call :check "Database connection test" "docker-compose -f docker-compose.internal.yml exec -T postgres psql -U mtdt_user -d webgis_db -c \"SELECT 1\""
call :check "Redis connection test" "docker-compose -f docker-compose.internal.yml exec -T redis redis-cli ping"

echo 5. File System Checks
echo =====================
echo.

call :check "Frontend build directory exists" "if exist frontend\dist (exit 0) else (exit 1)"
call :check "Backend uploads directory exists" "if exist backend\uploads (exit 0) else (exit 1)"
call :check "Docker volumes exist" "docker volume ls | findstr v107"

echo 6. Performance Checks
echo =====================
echo.

REM Check response times
powershell -Command "try { $start = Get-Date; $r = Invoke-WebRequest -Uri 'http://localhost:8080' -Method HEAD -TimeoutSec 5; $time = (Get-Date) - $start; if ($time.TotalMilliseconds -lt 1000) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    call :check "Frontend response time < 1s" "exit 0"
) else (
    call :check "Frontend response time < 1s" "exit 1"
)

powershell -Command "try { $start = Get-Date; $r = Invoke-WebRequest -Uri 'http://localhost:8000/api/health' -Method HEAD -TimeoutSec 5; $time = (Get-Date) - $start; if ($time.TotalMilliseconds -lt 1000) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    call :check "Backend response time < 1s" "exit 0"
) else (
    call :check "Backend response time < 1s" "exit 1"
)

echo ========================================
echo           Health Check Summary
echo ========================================
echo.
echo Total Checks: %TOTAL_CHECKS%
echo Passed: %PASSED_CHECKS%
echo Failed: %FAILED_CHECKS%

set /a SUCCESS_RATE=(%PASSED_CHECKS% * 100) / %TOTAL_CHECKS%
echo Success Rate: %SUCCESS_RATE%%%
echo.

if %FAILED_CHECKS% EQU 0 (
    echo [OVERALL STATUS] HEALTHY - All systems operational
    echo.
    echo System is running optimally. All services are accessible
    echo and responding within acceptable time limits.
) else if %SUCCESS_RATE% GEQ 80 (
    echo [OVERALL STATUS] WARNING - Minor issues detected
    echo.
    echo System is mostly functional but some issues were found.
    echo Please review the failed checks above.
) else (
    echo [OVERALL STATUS] CRITICAL - Major issues detected
    echo.
    echo System has significant problems that need immediate attention.
    echo Please check the failed items and restart services if needed.
)

echo.
echo Recommendations:
if %FAILED_CHECKS% GTR 0 (
    echo - Review failed checks above
    echo - Check Docker Desktop is running
    echo - Verify network connectivity
    echo - Restart services: docker-compose -f docker-compose.internal.yml restart
    echo - Check logs: docker-compose -f docker-compose.internal.yml logs
)
echo - Run this health check regularly
echo - Monitor system resources
echo - Keep backups up to date
echo - Update system components regularly

echo.
echo For detailed troubleshooting, run: manage-system.bat
echo.

REM Generate timestamp for logging
echo Health check completed at %date% %time%

pause
