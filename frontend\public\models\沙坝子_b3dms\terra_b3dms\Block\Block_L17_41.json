{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.38356268405914307, "root": {"boundingVolume": {"box": [-76.26881408691406, 32.67052459716797, -32.90349578857422, 11.132854461669922, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 8.4911527633667]}, "children": [{"boundingVolume": {"box": [-76.26881408691406, 22.571971893310547, -33.22258377075195, 11.132854461669922, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 8.172065734863281]}, "content": {"uri": "Block_L18_84.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-76.26881408691406, 42.769081115722656, -32.90349578857422, 11.132854461669922, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 8.4911527633667]}, "children": [{"boundingVolume": {"box": [-82.76298522949219, 42.769081115722656, -32.91547393798828, 4.6386871337890625, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 8.479175567626953]}, "content": {"uri": "Block_L19_153.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-71.630126953125, 42.769081115722656, -36.20900344848633, 6.494167327880859, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 5.18564510345459]}, "children": [{"boundingVolume": {"box": [-71.630126953125, 38.56134796142578, -36.26428985595703, 6.494167327880859, 0.0, 0.0, 0.0, 5.8908233642578125, 0.0, 0.0, 0.0, 5.13035774230957]}, "children": [{"boundingVolume": {"box": [-71.630126953125, 38.56134796142578, -36.26654052734375, 6.494167327880859, 0.0, 0.0, 0.0, 5.8908233642578125, 0.0, 0.0, 0.0, 5.128108978271484]}, "children": [{"boundingVolume": {"box": [-75.41839599609375, 35.615936279296875, -38.216094970703125, 2.705902099609375, 0.0, 0.0, 0.0, 2.9454116821289062, 0.0, 0.0, 0.0, 3.1785545349121094]}, "content": {"uri": "Block_L22_883.b3dm"}, "geometricError": 0.012171697802841663, "refine": "REPLACE"}, {"boundingVolume": {"box": [-75.41839599609375, 41.50675964355469, -36.26713562011719, 2.705902099609375, 0.0, 0.0, 0.0, 2.9454116821289062, 0.0, 0.0, 0.0, 5.1275129318237305]}, "content": {"uri": "Block_L22_882.b3dm"}, "geometricError": 0.012044861912727356, "refine": "REPLACE"}, {"boundingVolume": {"box": [-68.92422485351562, 38.56134796142578, -39.033172607421875, 3.7882652282714844, 0.0, 0.0, 0.0, 5.8908233642578125, 0.0, 0.0, 0.0, 2.3614749908447266]}, "content": {"uri": "Block_L22_881.b3dm"}, "geometricError": 0.012826215475797653, "refine": "REPLACE"}], "content": {"uri": "Block_L21_541.b3dm"}, "geometricError": 0.02440408058464527, "refine": "REPLACE"}], "content": {"uri": "Block_L20_291.b3dm"}, "geometricError": 0.04891425743699074, "refine": "REPLACE"}, {"boundingVolume": {"box": [-71.630126953125, 48.65990447998047, -36.20900344848633, 6.494167327880859, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 5.18564510345459]}, "children": [{"boundingVolume": {"box": [-75.41839599609375, 48.65990447998047, -36.20989227294922, 2.705902099609375, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 5.184757232666016]}, "children": [{"boundingVolume": {"box": [-75.41839599609375, 48.65990447998047, -38.802268981933594, 2.705902099609375, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 2.592378616333008]}, "content": {"uri": "Block_L22_880.b3dm"}, "geometricError": 0.012683098204433918, "refine": "REPLACE"}, {"boundingVolume": {"box": [-75.41839599609375, 48.65990447998047, -33.617515563964844, 2.705902099609375, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 2.592378616333008]}, "content": {"uri": "Block_L22_879.b3dm"}, "geometricError": 0.011423913761973381, "refine": "REPLACE"}], "content": {"uri": "Block_L21_540.b3dm"}, "geometricError": 0.0239337719976902, "refine": "REPLACE"}, {"boundingVolume": {"box": [-68.92422485351562, 48.65990447998047, -38.31279754638672, 3.7882652282714844, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 3.0818519592285156]}, "children": [{"boundingVolume": {"box": [-68.92422485351562, 48.65990447998047, -38.31407165527344, 3.7882652282714844, 0.0, 0.0, 0.0, 4.207731246948242, 0.0, 0.0, 0.0, 3.080575942993164]}, "content": {"uri": "Block_L22_878.b3dm"}, "geometricError": 0.011911030858755112, "refine": "REPLACE"}], "content": {"uri": "Block_L21_539.b3dm"}, "geometricError": 0.023838873952627182, "refine": "REPLACE"}], "content": {"uri": "Block_L20_290.b3dm"}, "geometricError": 0.04781026020646095, "refine": "REPLACE"}], "content": {"uri": "Block_L19_152.b3dm"}, "geometricError": 0.09652696549892426, "refine": "REPLACE"}], "content": {"uri": "Block_L18_83.b3dm"}, "geometricError": 0.18463851511478424, "refine": "REPLACE"}], "content": {"uri": "Block_L17_41.b3dm"}, "geometricError": 0.38356268405914307, "refine": "REPLACE"}}