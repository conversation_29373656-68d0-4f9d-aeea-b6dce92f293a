{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.18188709020614624, "root": {"boundingVolume": {"box": [-95.7513198852539, 147.92868041992188, -56.615169525146484, 13.916069030761719, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 9.825862884521484]}, "children": [{"boundingVolume": {"box": [-100.39000701904297, 147.92868041992188, -54.20671844482422, 9.277381896972656, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 7.417409896850586]}, "children": [{"boundingVolume": {"box": [-100.39000701904297, 147.92868041992188, -54.16824722290039, 9.277381896972656, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 7.378940582275391]}, "children": [{"boundingVolume": {"box": [-100.39000701904297, 143.2160186767578, -54.05371856689453, 9.277381896972656, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 7.264413833618164]}, "children": [{"boundingVolume": {"box": [-103.48246765136719, 143.2160186767578, -54.05371856689453, 6.1849212646484375, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 7.264413833618164]}, "content": {"uri": "Block_L23_623.b3dm"}, "geometricError": 0.01080551277846098, "refine": "REPLACE"}, {"boundingVolume": {"box": [-94.20508575439453, 143.2160186767578, -53.749244689941406, 3.0924606323242188, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 2.865856170654297]}, "content": {"uri": "Block_L22_617.b3dm"}, "geometricError": 0.011982262134552002, "refine": "REPLACE"}, {"boundingVolume": {"box": [-94.20508575439453, 143.2160186767578, -48.83634948730469, 3.0924606323242188, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 2.0470409393310547]}, "content": {"uri": "Block_L22_616.b3dm"}, "geometricError": 0.01131904125213623, "refine": "REPLACE"}], "content": {"uri": "Block_L21_410.b3dm"}, "geometricError": 0.022562528029084206, "refine": "REPLACE"}, {"boundingVolume": {"box": [-100.39000701904297, 152.64132690429688, -50.16839599609375, 9.277381896972656, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 3.379091262817383]}, "children": [{"boundingVolume": {"box": [-104.25558471679688, 152.64132690429688, -49.04401397705078, 5.41180419921875, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 2.2547073364257812]}, "content": {"uri": "Block_L23_622.b3dm"}, "geometricError": 0.009811262600123882, "refine": "REPLACE"}, {"boundingVolume": {"box": [-94.97820281982422, 152.64132690429688, -50.16839599609375, 3.8655776977539062, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 3.379091262817383]}, "content": {"uri": "Block_L23_621.b3dm"}, "geometricError": 0.01013063546270132, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1501.b3dm"}, "geometricError": 0.019975997507572174, "refine": "REPLACE"}], "content": {"uri": "Block_L21_858.b3dm"}, "geometricError": 0.042698901146650314, "refine": "REPLACE"}], "content": {"uri": "Block_L20_453.b3dm"}, "geometricError": 0.08542434126138687, "refine": "REPLACE"}, {"boundingVolume": {"box": [-86.47393798828125, 147.92868041992188, -56.615169525146484, 4.6386871337890625, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 9.825862884521484]}, "children": [{"boundingVolume": {"box": [-86.47393798828125, 147.92868041992188, -59.89045715332031, 4.6386871337890625, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 6.550575256347656]}, "children": [{"boundingVolume": {"box": [-86.47393798828125, 147.92868041992188, -59.89045715332031, 4.6386871337890625, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 6.550575256347656]}, "children": [{"boundingVolume": {"box": [-86.47393798828125, 141.64512634277344, -59.49636459350586, 4.6386871337890625, 0.0, 0.0, 0.0, 3.1417694091796875, 0.0, 0.0, 0.0, 6.156482696533203]}, "content": {"uri": "Block_L22_615.b3dm"}, "geometricError": 0.013699648901820183, "refine": "REPLACE"}, {"boundingVolume": {"box": [-84.67988586425781, 151.0704345703125, -59.89045715332031, 2.844635009765625, 0.0, 0.0, 0.0, 6.283546447753906, 0.0, 0.0, 0.0, 6.550575256347656]}, "content": {"uri": "Block_L22_614.b3dm"}, "geometricError": 0.012781321071088314, "refine": "REPLACE"}], "content": {"uri": "Block_L21_409.b3dm"}, "geometricError": 0.026807397603988647, "refine": "REPLACE"}], "content": {"uri": "Block_L20_225.b3dm"}, "geometricError": 0.05361068248748779, "refine": "REPLACE"}, {"boundingVolume": {"box": [-86.47393798828125, 147.92868041992188, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 9.425315856933594, 0.0, 0.0, 0.0, 3.275287628173828]}, "children": [{"boundingVolume": {"box": [-86.47393798828125, 143.2160186767578, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 4.7126617431640625, 0.0, 0.0, 0.0, 3.275287628173828]}, "children": [{"boundingVolume": {"box": [-86.48008728027344, 140.85968017578125, -50.06459426879883, 4.632541656494141, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 3.275287628173828]}, "content": {"uri": "Block_L22_613.b3dm"}, "geometricError": 0.01209370605647564, "refine": "REPLACE"}, {"boundingVolume": {"box": [-86.47393798828125, 145.57235717773438, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 3.275287628173828]}, "content": {"uri": "Block_L22_612.b3dm"}, "geometricError": 0.011553686112165451, "refine": "REPLACE"}], "content": {"uri": "Block_L21_408.b3dm"}, "geometricError": 0.02360469289124012, "refine": "REPLACE"}, {"boundingVolume": {"box": [-86.47393798828125, 152.64132690429688, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 4.712654113769531, 0.0, 0.0, 0.0, 3.275287628173828]}, "children": [{"boundingVolume": {"box": [-86.47393798828125, 150.28500366210938, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 2.3563232421875, 0.0, 0.0, 0.0, 3.275287628173828]}, "content": {"uri": "Block_L23_620.b3dm"}, "geometricError": 0.010908536612987518, "refine": "REPLACE"}, {"boundingVolume": {"box": [-86.47393798828125, 154.99765014648438, -50.06459426879883, 4.6386871337890625, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 3.275287628173828]}, "content": {"uri": "Block_L23_619.b3dm"}, "geometricError": 0.010265638120472431, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1500.b3dm"}, "geometricError": 0.021286267787218094, "refine": "REPLACE"}], "content": {"uri": "Block_L20_224.b3dm"}, "geometricError": 0.045013245195150375, "refine": "REPLACE"}], "content": {"uri": "Block_L19_116.b3dm"}, "geometricError": 0.09501584619283676, "refine": "REPLACE"}], "content": {"uri": "Block_L18_60.b3dm"}, "geometricError": 0.18188709020614624, "refine": "REPLACE"}}