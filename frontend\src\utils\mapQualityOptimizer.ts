/**
 * 地图质量优化工具
 * 专门用于优化Cesium地图的渲染质量和性能
 */

export interface QualitySettings {
  resolutionScale: number
  maximumScreenSpaceError: number
  enableFXAA: boolean
  enableLighting: boolean
  tileCacheSize: number
  enableAtmosphere: boolean
  enableFog: boolean
}

export class MapQualityOptimizer {
  private viewer: any
  private originalSettings: QualitySettings | null = null

  constructor(viewer: any) {
    this.viewer = viewer
    this.saveOriginalSettings()
  }

  /**
   * 保存原始设置
   */
  private saveOriginalSettings(): void {
    if (!this.viewer) return

    this.originalSettings = {
      resolutionScale: this.viewer.resolutionScale,
      maximumScreenSpaceError: this.viewer.scene.globe.maximumScreenSpaceError,
      enableFXAA: this.viewer.scene.postProcessStages.fxaa.enabled,
      enableLighting: this.viewer.scene.globe.enableLighting,
      tileCacheSize: this.viewer.scene.globe.tileCacheSize,
      enableAtmosphere: this.viewer.scene.skyAtmosphere.show,
      enableFog: this.viewer.scene.fog.enabled
    }
  }

  /**
   * 应用高质量设置（清晰但可能较慢）
   */
  applyHighQuality(): void {
    if (!this.viewer) return

    // 最高分辨率
    this.viewer.resolutionScale = 1.0

    // 最低屏幕空间误差（最高质量）
    this.viewer.scene.globe.maximumScreenSpaceError = 1

    // 启用抗锯齿
    this.viewer.scene.postProcessStages.fxaa.enabled = true

    // 增大瓦片缓存
    this.viewer.scene.globe.tileCacheSize = 2000

    // 启用大气效果
    this.viewer.scene.skyAtmosphere.show = true

    // 禁用雾效（提升清晰度）
    this.viewer.scene.fog.enabled = false

    // 优化影像图层
    this.optimizeImageryLayers()

    // 强制重新渲染
    this.viewer.scene.requestRender()

    console.log('已应用高质量设置')
  }

  /**
   * 应用平衡设置（质量和性能平衡）
   */
  applyBalanced(): void {
    if (!this.viewer) return

    // 标准分辨率
    this.viewer.resolutionScale = 1.0

    // 中等屏幕空间误差
    this.viewer.scene.globe.maximumScreenSpaceError = 2

    // 启用抗锯齿
    this.viewer.scene.postProcessStages.fxaa.enabled = true

    // 标准瓦片缓存
    this.viewer.scene.globe.tileCacheSize = 1000

    // 启用大气效果
    this.viewer.scene.skyAtmosphere.show = true

    // 禁用雾效
    this.viewer.scene.fog.enabled = false

    // 优化影像图层
    this.optimizeImageryLayers()

    // 强制重新渲染
    this.viewer.scene.requestRender()

    console.log('已应用平衡设置')
  }

  /**
   * 应用高性能设置（快速但质量较低）
   */
  applyHighPerformance(): void {
    if (!this.viewer) return

    // 降低分辨率
    this.viewer.resolutionScale = 0.8

    // 较高屏幕空间误差（较低质量）
    this.viewer.scene.globe.maximumScreenSpaceError = 4

    // 禁用抗锯齿
    this.viewer.scene.postProcessStages.fxaa.enabled = false

    // 减小瓦片缓存
    this.viewer.scene.globe.tileCacheSize = 500

    // 禁用大气效果
    this.viewer.scene.skyAtmosphere.show = false

    // 禁用雾效
    this.viewer.scene.fog.enabled = false

    // 强制重新渲染
    this.viewer.scene.requestRender()

    console.log('已应用高性能设置')
  }

  /**
   * 优化影像图层设置
   */
  private optimizeImageryLayers(): void {
    if (!this.viewer) return

    const imageryLayers = this.viewer.imageryLayers
    for (let i = 0; i < imageryLayers.length; i++) {
      const layer = imageryLayers.get(i)
      
      // 确保图层完全不透明（除非特意设置透明度）
      if (layer.alpha === undefined) {
        layer.alpha = 1.0
      }
      
      // 优化亮度、对比度、伽马值
      layer.brightness = 1.0
      layer.contrast = 1.0
      layer.gamma = 1.0
      layer.hue = 0.0
      layer.saturation = 1.0
    }
  }

  /**
   * 恢复原始设置
   */
  restoreOriginal(): void {
    if (!this.viewer || !this.originalSettings) return

    this.viewer.resolutionScale = this.originalSettings.resolutionScale
    this.viewer.scene.globe.maximumScreenSpaceError = this.originalSettings.maximumScreenSpaceError
    this.viewer.scene.postProcessStages.fxaa.enabled = this.originalSettings.enableFXAA
    this.viewer.scene.globe.enableLighting = this.originalSettings.enableLighting
    this.viewer.scene.globe.tileCacheSize = this.originalSettings.tileCacheSize
    this.viewer.scene.skyAtmosphere.show = this.originalSettings.enableAtmosphere
    this.viewer.scene.fog.enabled = this.originalSettings.enableFog

    this.viewer.scene.requestRender()

    console.log('已恢复原始设置')
  }

  /**
   * 获取当前质量设置
   */
  getCurrentSettings(): QualitySettings {
    if (!this.viewer) {
      throw new Error('Viewer not available')
    }

    return {
      resolutionScale: this.viewer.resolutionScale,
      maximumScreenSpaceError: this.viewer.scene.globe.maximumScreenSpaceError,
      enableFXAA: this.viewer.scene.postProcessStages.fxaa.enabled,
      enableLighting: this.viewer.scene.globe.enableLighting,
      tileCacheSize: this.viewer.scene.globe.tileCacheSize,
      enableAtmosphere: this.viewer.scene.skyAtmosphere.show,
      enableFog: this.viewer.scene.fog.enabled
    }
  }

  /**
   * 应用自定义设置
   */
  applyCustomSettings(settings: Partial<QualitySettings>): void {
    if (!this.viewer) return

    if (settings.resolutionScale !== undefined) {
      this.viewer.resolutionScale = settings.resolutionScale
    }

    if (settings.maximumScreenSpaceError !== undefined) {
      this.viewer.scene.globe.maximumScreenSpaceError = settings.maximumScreenSpaceError
    }

    if (settings.enableFXAA !== undefined) {
      this.viewer.scene.postProcessStages.fxaa.enabled = settings.enableFXAA
    }

    if (settings.enableLighting !== undefined) {
      this.viewer.scene.globe.enableLighting = settings.enableLighting
    }

    if (settings.tileCacheSize !== undefined) {
      this.viewer.scene.globe.tileCacheSize = settings.tileCacheSize
    }

    if (settings.enableAtmosphere !== undefined) {
      this.viewer.scene.skyAtmosphere.show = settings.enableAtmosphere
    }

    if (settings.enableFog !== undefined) {
      this.viewer.scene.fog.enabled = settings.enableFog
    }

    this.viewer.scene.requestRender()

    console.log('已应用自定义设置:', settings)
  }

  /**
   * 检测并应用最佳设置（基于设备性能）
   */
  applyOptimalSettings(): void {
    if (!this.viewer) return

    // 简单的性能检测
    const canvas = this.viewer.canvas
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    
    if (!gl) {
      // 如果不支持WebGL，使用最低设置
      this.applyHighPerformance()
      return
    }

    // 检测GPU信息
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info')
    const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : ''
    
    // 基于渲染器信息判断性能级别
    if (renderer.includes('Intel') || renderer.includes('integrated')) {
      // 集成显卡，使用高性能设置
      this.applyHighPerformance()
    } else if (renderer.includes('NVIDIA') || renderer.includes('AMD') || renderer.includes('Radeon')) {
      // 独立显卡，使用高质量设置
      this.applyHighQuality()
    } else {
      // 未知设备，使用平衡设置
      this.applyBalanced()
    }
  }
}

/**
 * 创建地图质量优化器实例
 */
export function createMapQualityOptimizer(viewer: any): MapQualityOptimizer {
  return new MapQualityOptimizer(viewer)
}
