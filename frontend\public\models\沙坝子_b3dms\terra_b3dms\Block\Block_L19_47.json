{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.0906471461057663, "root": {"boundingVolume": {"box": [52.24707794189453, 93.13338470458984, -38.47798156738281, 9.085994720458984, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 8.224428176879883]}, "children": [{"boundingVolume": {"box": [49.81071472167969, 88.51071166992188, -42.59019470214844, 6.649631500244141, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112213134765625]}, "children": [{"boundingVolume": {"box": [49.81071472167969, 88.51071166992188, -42.59019470214844, 6.649631500244141, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112213134765625]}, "children": [{"boundingVolume": {"box": [45.3761100769043, 86.19937896728516, -42.59019470214844, 2.21502685546875, 0.0, 0.0, 0.0, 2.3113327026367188, 0.0, 0.0, 0.0, 4.112213134765625]}, "content": {"uri": "Block_L23_129.b3dm"}, "geometricError": 0.010544645600020885, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.3761100769043, 90.82205200195312, -42.59019470214844, 2.21502685546875, 0.0, 0.0, 0.0, 2.3113365173339844, 0.0, 0.0, 0.0, 4.112213134765625]}, "content": {"uri": "Block_L23_128.b3dm"}, "geometricError": 0.010977491736412048, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.02118682861328, 88.51071166992188, -42.59019470214844, 4.430051803588867, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112213134765625]}, "content": {"uri": "Block_L22_82.b3dm"}, "geometricError": 0.012102032080292702, "refine": "REPLACE"}], "content": {"uri": "Block_L21_144.b3dm"}, "geometricError": 0.022265763953328133, "refine": "REPLACE"}], "content": {"uri": "Block_L20_89.b3dm"}, "geometricError": 0.04472774267196655, "refine": "REPLACE"}, {"boundingVolume": {"box": [49.761199951171875, 88.51071166992188, -34.36576843261719, 6.600114822387695, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "children": [{"boundingVolume": {"box": [45.893741607666016, 88.51071166992188, -34.36576843261719, 2.7326583862304688, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "children": [{"boundingVolume": {"box": [45.893741607666016, 85.81416320800781, -34.36576843261719, 2.7326583862304688, 0.0, 0.0, 0.0, 1.9261131286621094, 0.0, 0.0, 0.0, 4.112215042114258]}, "content": {"uri": "Block_L22_81.b3dm"}, "geometricError": 0.011896217241883278, "refine": "REPLACE"}, {"boundingVolume": {"box": [45.893741607666016, 90.43682861328125, -34.36576843261719, 2.7326583862304688, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 4.112215042114258]}, "content": {"uri": "Block_L22_80.b3dm"}, "geometricError": 0.01132233627140522, "refine": "REPLACE"}], "content": {"uri": "Block_L21_143.b3dm"}, "geometricError": 0.023224983364343643, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.45673751831055, 88.51071166992188, -34.36576843261719, 3.8303375244140625, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "children": [{"boundingVolume": {"box": [52.45212173461914, 88.51071166992188, -34.36576843261719, 3.8257217407226562, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "content": {"uri": "Block_L22_79.b3dm"}, "geometricError": 0.011544415727257729, "refine": "REPLACE"}], "content": {"uri": "Block_L21_142.b3dm"}, "geometricError": 0.023134561255574226, "refine": "REPLACE"}], "content": {"uri": "Block_L20_88.b3dm"}, "geometricError": 0.04641161859035492, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.05287170410156, 97.75605773925781, -38.47798156738281, 8.891790390014648, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 8.224428176879883]}, "children": [{"boundingVolume": {"box": [46.80243682861328, 97.75605773925781, -42.59019470214844, 3.6413516998291016, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112213134765625]}, "children": [{"boundingVolume": {"box": [46.80243682861328, 95.44471740722656, -43.074134826660156, 3.6413516998291016, 0.0, 0.0, 0.0, 2.3113365173339844, 0.0, 0.0, 0.0, 3.6282730102539062]}, "content": {"uri": "Block_L22_78.b3dm"}, "geometricError": 0.012015348300337791, "refine": "REPLACE"}, {"boundingVolume": {"box": [46.80243682861328, 100.06739044189453, -42.59019470214844, 3.6413516998291016, 0.0, 0.0, 0.0, 2.3113327026367188, 0.0, 0.0, 0.0, 4.112213134765625]}, "content": {"uri": "Block_L22_77.b3dm"}, "geometricError": 0.011735411360859871, "refine": "REPLACE"}], "content": {"uri": "Block_L21_141.b3dm"}, "geometricError": 0.02370050735771656, "refine": "REPLACE"}, {"boundingVolume": {"box": [46.80243682861328, 97.75605773925781, -34.36576843261719, 3.6413516998291016, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "children": [{"boundingVolume": {"box": [48.3972282409668, 95.44471740722656, -34.210533142089844, 2.046558380126953, 0.0, 0.0, 0.0, 2.3113365173339844, 0.0, 0.0, 0.0, 3.956981658935547]}, "content": {"uri": "Block_L23_127.b3dm"}, "geometricError": 0.0105744618922472, "refine": "REPLACE"}, {"boundingVolume": {"box": [46.80243682861328, 100.06739044189453, -34.36576843261719, 3.6413516998291016, 0.0, 0.0, 0.0, 2.3113327026367188, 0.0, 0.0, 0.0, 4.112215042114258]}, "content": {"uri": "Block_L23_126.b3dm"}, "geometricError": 0.010331204161047935, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1265.b3dm"}, "geometricError": 0.02090977318584919, "refine": "REPLACE"}, {"boundingVolume": {"box": [55.597557067871094, 97.75605773925781, -38.47798156738281, 5.153768539428711, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 8.224428176879883]}, "children": [{"boundingVolume": {"box": [53.22544479370117, 97.75605773925781, -42.59019470214844, 2.781658172607422, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112213134765625]}, "content": {"uri": "Block_L22_76.b3dm"}, "geometricError": 0.012360029853880405, "refine": "REPLACE"}, {"boundingVolume": {"box": [55.54167938232422, 97.75605773925781, -34.36576843261719, 5.097890853881836, 0.0, 0.0, 0.0, 4.622669219970703, 0.0, 0.0, 0.0, 4.112215042114258]}, "content": {"uri": "Block_L23_125.b3dm"}, "geometricError": 0.01063664723187685, "refine": "REPLACE"}], "content": {"uri": "Block_L21_140.b3dm"}, "geometricError": 0.02286241576075554, "refine": "REPLACE"}], "content": {"uri": "Block_L20_87.b3dm"}, "geometricError": 0.044900305569171906, "refine": "REPLACE"}], "content": {"uri": "Block_L19_47.b3dm"}, "geometricError": 0.0906471461057663, "refine": "REPLACE"}}