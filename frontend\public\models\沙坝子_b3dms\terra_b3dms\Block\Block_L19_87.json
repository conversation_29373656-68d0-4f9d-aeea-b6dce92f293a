{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.09759410470724106, "root": {"boundingVolume": {"box": [30.84799575805664, -23.833126068115234, -52.90434265136719, 9.704338073730469, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 5.277118682861328]}, "children": [{"boundingVolume": {"box": [30.811870574951172, -29.725706100463867, -52.90434265136719, 9.668212890625, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 5.277118682861328]}, "children": [{"boundingVolume": {"box": [25.15584945678711, -29.725706100463867, -52.906158447265625, 4.0121917724609375, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 5.275300979614258]}, "children": [{"boundingVolume": {"box": [25.15584945678711, -32.180946350097656, -54.02143096923828, 4.0121917724609375, 0.0, 0.0, 0.0, 3.437337875366211, 0.0, 0.0, 0.0, 4.160028457641602]}, "content": {"uri": "Block_L22_391.b3dm"}, "geometricError": 0.013566156849265099, "refine": "REPLACE"}, {"boundingVolume": {"box": [25.15584945678711, -26.288368225097656, -55.544715881347656, 4.0121917724609375, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 2.636747360229492]}, "content": {"uri": "Block_L22_390.b3dm"}, "geometricError": 0.012505858205258846, "refine": "REPLACE"}, {"boundingVolume": {"box": [25.75628662109375, -26.288368225097656, -50.271217346191406, 3.4117536544799805, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 2.636747360229492]}, "content": {"uri": "Block_L22_389.b3dm"}, "geometricError": 0.011631466448307037, "refine": "REPLACE"}], "content": {"uri": "Block_L21_297.b3dm"}, "geometricError": 0.025636393576860428, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.82406234741211, -29.725706100463867, -53.953460693359375, 5.6560211181640625, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 4.228002548217773]}, "children": [{"boundingVolume": {"box": [34.78511047363281, -32.180946350097656, -53.95411682128906, 5.617069244384766, 0.0, 0.0, 0.0, 3.437337875366211, 0.0, 0.0, 0.0, 4.227346420288086]}, "content": {"uri": "Block_L22_388.b3dm"}, "geometricError": 0.015161151997745037, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.36671447753906, -26.288368225097656, -54.054412841796875, 5.198671340942383, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 4.127046585083008]}, "content": {"uri": "Block_L22_387.b3dm"}, "geometricError": 0.011923174373805523, "refine": "REPLACE"}], "content": {"uri": "Block_L21_296.b3dm"}, "geometricError": 0.02681686542928219, "refine": "REPLACE"}], "content": {"uri": "Block_L20_162.b3dm"}, "geometricError": 0.05260765179991722, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.87421417236328, -17.9405460357666, -53.495296478271484, 3.7305564880371094, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 4.686164855957031]}, "children": [{"boundingVolume": {"box": [24.87421417236328, -20.886837005615234, -53.495460510253906, 3.7305564880371094, 0.0, 0.0, 0.0, 2.9462900161743164, 0.0, 0.0, 0.0, 4.686002731323242]}, "children": [{"boundingVolume": {"box": [24.87421417236328, -20.886837005615234, -55.838462829589844, 3.7305564880371094, 0.0, 0.0, 0.0, 2.9462900161743164, 0.0, 0.0, 0.0, 2.3430004119873047]}, "content": {"uri": "Block_L22_386.b3dm"}, "geometricError": 0.01143023930490017, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.87421417236328, -21.169078826904297, -51.15245819091797, 3.7305564880371094, 0.0, 0.0, 0.0, 2.664046287536621, 0.0, 0.0, 0.0, 2.3430023193359375]}, "content": {"uri": "Block_L23_431.b3dm"}, "geometricError": 0.01092652790248394, "refine": "REPLACE"}], "content": {"uri": "Block_L21_295.b3dm"}, "geometricError": 0.022595437243580818, "refine": "REPLACE"}, {"boundingVolume": {"box": [24.87421417236328, -14.994256019592285, -54.81812286376953, 3.7305564880371094, 0.0, 0.0, 0.0, 2.9462900161743164, 0.0, 0.0, 0.0, 3.363340377807617]}, "children": [{"boundingVolume": {"box": [24.87421417236328, -14.994256019592285, -54.8569221496582, 3.7305564880371094, 0.0, 0.0, 0.0, 2.9462900161743164, 0.0, 0.0, 0.0, 3.3245391845703125]}, "content": {"uri": "Block_L23_430.b3dm"}, "geometricError": 0.010545175522565842, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1413.b3dm"}, "geometricError": 0.02110355719923973, "refine": "REPLACE"}], "content": {"uri": "Block_L21_818.b3dm"}, "geometricError": 0.04406197369098663, "refine": "REPLACE"}, {"boundingVolume": {"box": [33.8304557800293, -17.9405460357666, -53.957130432128906, 5.225685119628906, 0.0, 0.0, 0.0, 5.892580032348633, 0.0, 0.0, 0.0, 4.224330902099609]}, "children": [{"boundingVolume": {"box": [33.8304557800293, -21.377883911132812, -53.957130432128906, 5.225685119628906, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 4.224330902099609]}, "children": [{"boundingVolume": {"box": [30.780929565429688, -21.377883911132812, -53.2968864440918, 2.176158905029297, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 3.5640869140625]}, "content": {"uri": "Block_L22_385.b3dm"}, "geometricError": 0.011575236916542053, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.00370788574219, -21.377883911132812, -55.864017486572266, 3.046621322631836, 0.0, 0.0, 0.0, 2.455242156982422, 0.0, 0.0, 0.0, 2.31744384765625]}, "content": {"uri": "Block_L22_384.b3dm"}, "geometricError": 0.012757527641952038, "refine": "REPLACE"}], "content": {"uri": "Block_L21_294.b3dm"}, "geometricError": 0.024226339533925056, "refine": "REPLACE"}, {"boundingVolume": {"box": [32.59082794189453, -15.48530387878418, -54.96306610107422, 3.9860591888427734, 0.0, 0.0, 0.0, 3.437337875366211, 0.0, 0.0, 0.0, 3.218393325805664]}, "children": [{"boundingVolume": {"box": [30.26152992248535, -15.48530387878418, -54.96306610107422, 1.656759262084961, 0.0, 0.0, 0.0, 3.437337875366211, 0.0, 0.0, 0.0, 3.218393325805664]}, "content": {"uri": "Block_L23_429.b3dm"}, "geometricError": 0.010816439054906368, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.23775100708008, -15.48530387878418, -55.46223449707031, 2.3194618225097656, 0.0, 0.0, 0.0, 3.437337875366211, 0.0, 0.0, 0.0, 2.719228744506836]}, "content": {"uri": "Block_L22_383.b3dm"}, "geometricError": 0.011661820113658905, "refine": "REPLACE"}], "content": {"uri": "Block_L21_293.b3dm"}, "geometricError": 0.02229924499988556, "refine": "REPLACE"}], "content": {"uri": "Block_L20_161.b3dm"}, "geometricError": 0.04679112136363983, "refine": "REPLACE"}], "content": {"uri": "Block_L19_87.b3dm"}, "geometricError": 0.09759410470724106, "refine": "REPLACE"}}