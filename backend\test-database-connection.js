const { Pool } = require('pg');

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('🔍 开始测试数据库连接...');
  
  const config = {
    host: 'localhost',
    port: 5432,
    database: 'webgis_db',
    user: 'testUser',
    password: '1234',
    max: 1,
    connectionTimeoutMillis: 5000,
  };

  let pool;
  let client;

  try {
    console.log(`📡 连接到: ${config.host}:${config.port}/${config.database}`);
    
    pool = new Pool(config);
    client = await pool.connect();
    
    console.log('✅ 数据库连接成功');
    
    // 测试基本查询
    const versionResult = await client.query('SELECT version()');
    console.log('📊 PostgreSQL版本:', versionResult.rows[0].version.split(' ')[0]);
    
    // 检查PostGIS扩展
    try {
      const postgisResult = await client.query('SELECT PostGIS_Version()');
      console.log('🗺️  PostGIS版本:', postgisResult.rows[0].postgis_version);
    } catch (error) {
      console.log('⚠️  PostGIS扩展未安装或不可访问');
    }
    
    // 查询空间表
    const tablesQuery = `
      SELECT 
        t.table_name,
        t.table_schema,
        t.table_type
      FROM information_schema.tables t
      WHERE t.table_schema NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        AND t.table_schema = 'public'
      ORDER BY t.table_schema, t.table_name
      LIMIT 10
    `;
    
    const tablesResult = await client.query(tablesQuery);
    console.log(`📋 找到 ${tablesResult.rows.length} 个表:`);
    
    for (const row of tablesResult.rows) {
      console.log(`   - ${row.table_schema}.${row.table_name} (${row.table_type})`);
      
      // 检查是否有几何列
      try {
        const geomQuery = `
          SELECT 
            f_geometry_column as column_name,
            type as geometry_type,
            srid
          FROM geometry_columns 
          WHERE f_table_schema = $1 AND f_table_name = $2
        `;
        
        const geomResult = await client.query(geomQuery, [row.table_schema, row.table_name]);
        
        if (geomResult.rows.length > 0) {
          console.log(`     🗺️  几何列: ${geomResult.rows.map(g => `${g.column_name}(${g.geometry_type})`).join(', ')}`);
        }
      } catch (error) {
        // 忽略几何列查询错误
      }
    }
    
    console.log('🎉 数据库连接测试完成');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 建议: 请检查PostgreSQL服务是否启动');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 建议: 请检查主机地址是否正确');
    } else if (error.message.includes('password authentication failed')) {
      console.log('💡 建议: 请检查用户名和密码是否正确');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('💡 建议: 请检查数据库名称是否正确，或创建数据库');
    }
    
  } finally {
    if (client) {
      client.release();
    }
    if (pool) {
      await pool.end();
    }
  }
}

// 运行测试
testDatabaseConnection().catch(console.error);
