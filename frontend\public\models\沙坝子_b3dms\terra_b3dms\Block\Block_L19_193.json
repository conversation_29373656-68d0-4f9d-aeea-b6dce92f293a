{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.11308441311120987, "root": {"boundingVolume": {"box": [-61.425010681152344, -73.30781555175781, -10.054553031921387, 25.976659774780273, 0.0, 0.0, 0.0, 19.329055786132812, 0.0, 0.0, 0.0, 15.428174018859863]}, "children": [{"boundingVolume": {"box": [-72.24861907958984, -62.950809478759766, -10.842860221862793, 15.153051376342773, 0.0, 0.0, 0.0, 8.972049713134766, 0.0, 0.0, 0.0, 14.437518119812012]}, "children": [{"boundingVolume": {"box": [-72.24861907958984, -62.950809478759766, -10.834932327270508, 15.153051376342773, 0.0, 0.0, 0.0, 8.972049713134766, 0.0, 0.0, 0.0, 14.429590225219727]}, "children": [{"boundingVolume": {"box": [-76.03688049316406, -57.26659393310547, -7.706019401550293, 11.364788055419922, 0.0, 0.0, 0.0, 3.287832260131836, 0.0, 0.0, 0.0, 11.290780067443848]}, "content": {"uri": "Block_L22_1145.b3dm"}, "geometricError": 0.016382556408643723, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.88383483886719, -62.950809478759766, -10.83988094329834, 3.7882633209228516, 0.0, 0.0, 0.0, 8.972049713134766, 0.0, 0.0, 0.0, 14.424641609191895]}, "content": {"uri": "Block_L22_1144.b3dm"}, "geometricError": 0.013079067692160606, "refine": "REPLACE"}], "content": {"uri": "Block_L21_678.b3dm"}, "geometricError": 0.028030473738908768, "refine": "REPLACE"}], "content": {"uri": "Block_L20_368.b3dm"}, "geometricError": 0.056445520371198654, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.2719612121582, -79.74919891357422, -9.180959701538086, 10.8236083984375, 0.0, 0.0, 0.0, 12.886711120605469, 0.0, 0.0, 0.0, 14.282831192016602]}, "children": [{"boundingVolume": {"box": [-46.2719612121582, -79.74848937988281, -9.180959701538086, 10.8236083984375, 0.0, 0.0, 0.0, 12.886001586914062, 0.0, 0.0, 0.0, 14.282831192016602]}, "children": [{"boundingVolume": {"box": [-45.52406692504883, -73.11195373535156, -15.125497817993164, 10.075714111328125, 0.0, 0.0, 0.0, 6.2494659423828125, 0.0, 0.0, 0.0, 8.328916549682617]}, "content": {"uri": "Block_L22_1143.b3dm"}, "geometricError": 0.016215922310948372, "refine": "REPLACE"}, {"boundingVolume": {"box": [-43.209083557128906, -85.11444854736328, -0.8473551273345947, 7.760732650756836, 0.0, 0.0, 0.0, 7.515510559082031, 0.0, 0.0, 0.0, 5.949226379394531]}, "content": {"uri": "Block_L22_1142.b3dm"}, "geometricError": 0.01477572787553072, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.2719612121582, -72.230712890625, -1.55882728099823, 10.8236083984375, 0.0, 0.0, 0.0, 5.36822509765625, 0.0, 0.0, 0.0, 5.237753868103027]}, "content": {"uri": "Block_L22_1141.b3dm"}, "geometricError": 0.015267519280314445, "refine": "REPLACE"}], "content": {"uri": "Block_L21_677.b3dm"}, "geometricError": 0.03082885779440403, "refine": "REPLACE"}], "content": {"uri": "Block_L20_367.b3dm"}, "geometricError": 0.061676204204559326, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.2719612121582, -60.420623779296875, -11.029203414916992, 10.8236083984375, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 14.441362380981445]}, "children": [{"boundingVolume": {"box": [-51.68376541137695, -60.420623779296875, -19.37969970703125, 5.41180419921875, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 5.884823799133301]}, "children": [{"boundingVolume": {"box": [-51.68376541137695, -63.64155578613281, -18.866012573242188, 5.41180419921875, 0.0, 0.0, 0.0, 3.2209320068359375, 0.0, 0.0, 0.0, 5.371137619018555]}, "content": {"uri": "Block_L22_1140.b3dm"}, "geometricError": 0.012969831004738808, "refine": "REPLACE"}, {"boundingVolume": {"box": [-51.68376541137695, -57.19969177246094, -19.37969970703125, 5.41180419921875, 0.0, 0.0, 0.0, 3.2209320068359375, 0.0, 0.0, 0.0, 5.884823799133301]}, "content": {"uri": "Block_L22_1139.b3dm"}, "geometricError": 0.013308139517903328, "refine": "REPLACE"}], "content": {"uri": "Block_L21_676.b3dm"}, "geometricError": 0.02620100788772106, "refine": "REPLACE"}, {"boundingVolume": {"box": [-40.86015701293945, -60.420623779296875, -19.473968505859375, 5.41180419921875, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 5.979094505310059]}, "children": [{"boundingVolume": {"box": [-40.86015701293945, -64.71520233154297, -19.471193313598633, 5.41180419921875, 0.0, 0.0, 0.0, 2.147287368774414, 0.0, 0.0, 0.0, 5.976318359375]}, "content": {"uri": "Block_L22_1138.b3dm"}, "geometricError": 0.015033332630991936, "refine": "REPLACE"}, {"boundingVolume": {"box": [-40.86015701293945, -58.273338317871094, -22.013477325439453, 5.41180419921875, 0.0, 0.0, 0.0, 4.294576644897461, 0.0, 0.0, 0.0, 3.115276336669922]}, "content": {"uri": "Block_L22_1137.b3dm"}, "geometricError": 0.013515306636691093, "refine": "REPLACE"}], "content": {"uri": "Block_L21_675.b3dm"}, "geometricError": 0.028752582147717476, "refine": "REPLACE"}, {"boundingVolume": {"box": [-46.2719612121582, -60.420623779296875, -5.080114841461182, 10.8236083984375, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 8.41476058959961]}, "children": [{"boundingVolume": {"box": [-53.487701416015625, -60.420623779296875, -5.128028869628906, 3.607870101928711, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 8.366846084594727]}, "content": {"uri": "Block_L22_1136.b3dm"}, "geometricError": 0.012153418734669685, "refine": "REPLACE"}, {"boundingVolume": {"box": [-42.664093017578125, -60.420623779296875, -8.663963317871094, 7.215738296508789, 0.0, 0.0, 0.0, 6.441864013671875, 0.0, 0.0, 0.0, 4.830911636352539]}, "content": {"uri": "Block_L22_1135.b3dm"}, "geometricError": 0.014578873291611671, "refine": "REPLACE"}], "content": {"uri": "Block_L21_674.b3dm"}, "geometricError": 0.026065219193696976, "refine": "REPLACE"}], "content": {"uri": "Block_L20_366.b3dm"}, "geometricError": 0.053881507366895676, "refine": "REPLACE"}], "content": {"uri": "Block_L19_193.b3dm"}, "geometricError": 0.11308441311120987, "refine": "REPLACE"}}