{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.34037259221076965, "root": {"boundingVolume": {"box": [24.354022979736328, 135.6619415283203, -36.27955627441406, 18.80706024169922, 0.0, 0.0, 0.0, 18.490676879882812, 0.0, 0.0, 0.0, 16.465866088867188]}, "children": [{"boundingVolume": {"box": [18.085002899169922, 136.0967559814453, -45.3201904296875, 12.538040161132812, 0.0, 0.0, 0.0, 18.055862426757812, 0.0, 0.0, 0.0, 7.425233840942383]}, "children": [{"boundingVolume": {"box": [18.085002899169922, 136.0967559814453, -45.3201904296875, 12.538040161132812, 0.0, 0.0, 0.0, 18.055862426757812, 0.0, 0.0, 0.0, 7.425233840942383]}, "content": {"uri": "Block_L20_406.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_212.b3dm"}, "geometricError": 0.1720213145017624, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.89206314086914, 126.4166030883789, -36.56206130981445, 6.269020080566406, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 16.183361053466797]}, "children": [{"boundingVolume": {"box": [36.89206314086914, 126.4166030883789, -46.05323028564453, 6.269020080566406, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 6.692192077636719]}, "content": {"uri": "Block_L20_405.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.194190979003906, 126.4166030883789, -29.89282989501953, 2.966890335083008, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 9.468208312988281]}, "children": [{"boundingVolume": {"box": [40.21353530883789, 126.4166030883789, -29.9327392578125, 2.9475479125976562, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 9.428298950195312]}, "children": [{"boundingVolume": {"box": [40.22309875488281, 121.02349090576172, -36.238014221191406, 2.9379844665527344, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.1230220794677734]}, "children": [{"boundingVolume": {"box": [40.73137283325195, 119.09738159179688, -36.837650299072266, 2.4297103881835938, 0.0, 0.0, 0.0, 1.9261131286621094, 0.0, 0.0, 0.0, 2.523387908935547]}, "content": {"uri": "Block_L23_168.b3dm"}, "geometricError": 0.008749294094741344, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.22309875488281, 122.94960021972656, -37.79952621459961, 2.9379844665527344, 0.0, 0.0, 0.0, 1.9261131286621094, 0.0, 0.0, 0.0, 1.5615119934082031]}, "content": {"uri": "Block_L23_167.b3dm"}, "geometricError": 0.008271886967122555, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.879722595214844, 122.94960021972656, -34.67650604248047, 2.281360626220703, 0.0, 0.0, 0.0, 1.9261131286621094, 0.0, 0.0, 0.0, 1.5615100860595703]}, "content": {"uri": "Block_L23_166.b3dm"}, "geometricError": 0.007763990201056004, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1284.b3dm"}, "geometricError": 0.016641151160001755, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.45417785644531, 130.26882934570312, -36.238014221191406, 2.7069053649902344, 0.0, 0.0, 0.0, 5.3931121826171875, 0.0, 0.0, 0.0, 3.1230220794677734]}, "children": [{"boundingVolume": {"box": [40.46942901611328, 127.12284851074219, -36.29319763183594, 2.6916542053222656, 0.0, 0.0, 0.0, 2.24713134765625, 0.0, 0.0, 0.0, 3.067840576171875]}, "content": {"uri": "Block_L23_165.b3dm"}, "geometricError": 0.00832855049520731, "refine": "REPLACE"}, {"boundingVolume": {"box": [41.77415466308594, 132.51596069335938, -36.238014221191406, 1.3869304656982422, 0.0, 0.0, 0.0, 3.1459808349609375, 0.0, 0.0, 0.0, 3.1230220794677734]}, "content": {"uri": "Block_L23_164.b3dm"}, "geometricError": 0.009558227844536304, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1283.b3dm"}, "geometricError": 0.01780267246067524, "refine": "REPLACE"}, {"boundingVolume": {"box": [41.51968765258789, 128.54913330078125, -26.829261779785156, 1.6413955688476562, 0.0, 0.0, 0.0, 7.112812042236328, 0.0, 0.0, 0.0, 6.285732269287109]}, "children": [{"boundingVolume": {"box": [41.99590301513672, 125.62043762207031, -27.11409568786621, 1.1651802062988281, 0.0, 0.0, 0.0, 4.134735107421875, 0.0, 0.0, 0.0, 6.000898361206055]}, "content": {"uri": "Block_L23_163.b3dm"}, "geometricError": 0.009349645115435123, "refine": "REPLACE"}, {"boundingVolume": {"box": [41.51968765258789, 132.70855712890625, -26.868947982788086, 1.6413955688476562, 0.0, 0.0, 0.0, 2.9533843994140625, 0.0, 0.0, 0.0, 6.24604606628418]}, "content": {"uri": "Block_L23_162.b3dm"}, "geometricError": 0.009318700060248375, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1282.b3dm"}, "geometricError": 0.01875356025993824, "refine": "REPLACE"}], "content": {"uri": "Block_L21_749.b3dm"}, "geometricError": 0.035189706832170486, "refine": "REPLACE"}], "content": {"uri": "Block_L20_404.b3dm"}, "geometricError": 0.07071865350008011, "refine": "REPLACE"}], "content": {"uri": "Block_L19_211.b3dm"}, "geometricError": 0.16152112185955048, "refine": "REPLACE"}, {"boundingVolume": {"box": [36.89206314086914, 144.90728759765625, -36.27955627441406, 6.269020080566406, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 16.465866088867188]}, "children": [{"boundingVolume": {"box": [36.89206314086914, 144.90728759765625, -45.89609909057617, 6.269020080566406, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 6.849323272705078]}, "content": {"uri": "Block_L20_403.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.4111328125, 144.90728759765625, -29.430233001708984, 4.749950408935547, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 9.616543769836426]}, "children": [{"boundingVolume": {"box": [38.4111328125, 144.90728759765625, -29.43577766418457, 4.749950408935547, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 9.610998153686523]}, "children": [{"boundingVolume": {"box": [40.82726287841797, 141.0550537109375, -35.850425720214844, 2.333822250366211, 0.0, 0.0, 0.0, 5.3931121826171875, 0.0, 0.0, 0.0, 3.196352005004883]}, "children": [{"boundingVolume": {"box": [41.14460372924805, 138.35848999023438, -35.850425720214844, 2.0164794921875, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 3.196352005004883]}, "content": {"uri": "Block_L23_154.b3dm"}, "geometricError": 0.010797298513352871, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.8292236328125, 143.75161743164062, -35.850425720214844, 2.331857681274414, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 3.196352005004883]}, "content": {"uri": "Block_L22_94.b3dm"}, "geometricError": 0.01153075322508812, "refine": "REPLACE"}], "content": {"uri": "Block_L21_152.b3dm"}, "geometricError": 0.022343840450048447, "refine": "REPLACE"}, {"boundingVolume": {"box": [38.4111328125, 150.30038452148438, -35.850425720214844, 4.749950408935547, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.196352005004883]}, "children": [{"boundingVolume": {"box": [36.036155700683594, 150.694580078125, -35.850425720214844, 2.3749752044677734, 0.0, 0.0, 0.0, 3.4580459594726562, 0.0, 0.0, 0.0, 3.196352005004883]}, "content": {"uri": "Block_L22_93.b3dm"}, "geometricError": 0.0121065778657794, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.786109924316406, 150.30038452148438, -35.850425720214844, 2.3749752044677734, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.196352005004883]}, "content": {"uri": "Block_L22_92.b3dm"}, "geometricError": 0.012691224925220013, "refine": "REPLACE"}], "content": {"uri": "Block_L21_151.b3dm"}, "geometricError": 0.024764597415924072, "refine": "REPLACE"}, {"boundingVolume": {"box": [40.0792236328125, 144.90728759765625, -26.261367797851562, 3.081857681274414, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 6.392703056335449]}, "children": [{"boundingVolume": {"box": [40.08819580078125, 144.90728759765625, -26.261367797851562, 3.0728893280029297, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 6.392703056335449]}, "content": {"uri": "Block_L22_91.b3dm"}, "geometricError": 0.011339854449033737, "refine": "REPLACE"}], "content": {"uri": "Block_L21_150.b3dm"}, "geometricError": 0.022716328501701355, "refine": "REPLACE"}], "content": {"uri": "Block_L20_92.b3dm"}, "geometricError": 0.04686354100704193, "refine": "REPLACE"}], "content": {"uri": "Block_L19_48.b3dm"}, "geometricError": 0.09383896738290787, "refine": "REPLACE"}], "content": {"uri": "Block_L18_25.b3dm"}, "geometricError": 0.1770816147327423, "refine": "REPLACE"}], "content": {"uri": "Block_L18_112.b3dm"}, "geometricError": 0.34037259221076965, "refine": "REPLACE"}}