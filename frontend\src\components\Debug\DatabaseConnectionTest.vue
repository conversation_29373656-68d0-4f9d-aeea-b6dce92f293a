<template>
  <div class="database-test-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>数据库连接测试</span>
          <el-button type="primary" size="small" @click="refreshConnections">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 连接列表 -->
      <div class="test-section">
        <h4>当前连接配置</h4>
        <el-table :data="connections" size="small" border>
          <el-table-column prop="name" label="连接名称" />
          <el-table-column prop="host" label="主机" />
          <el-table-column prop="port" label="端口" />
          <el-table-column prop="database" label="数据库" />
          <el-table-column prop="username" label="用户名" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button 
                size="small" 
                type="primary" 
                @click="testConnection(scope.row)"
                :loading="testingConnections.has(scope.row.id)"
              >
                测试连接
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="getTables(scope.row)"
                :loading="loadingTables.has(scope.row.id)"
              >
                获取表
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 测试结果 -->
      <div class="test-section" v-if="testResults.length > 0">
        <h4>测试结果</h4>
        <div v-for="result in testResults" :key="result.id" class="test-result">
          <el-alert
            :title="result.title"
            :type="result.type"
            :description="result.message"
            show-icon
            :closable="false"
          />
          <div v-if="result.details" class="result-details">
            <pre>{{ result.details }}</pre>
          </div>
        </div>
      </div>

      <!-- 表列表 -->
      <div class="test-section" v-if="tables.length > 0">
        <h4>数据库表列表</h4>
        <el-table :data="tables" size="small" border>
          <el-table-column prop="schema" label="模式" width="100" />
          <el-table-column prop="name" label="表名" />
          <el-table-column prop="type" label="类型" width="80" />
          <el-table-column label="几何列" width="150">
            <template #default="scope">
              <el-tag 
                v-for="col in scope.row.geometryColumns" 
                :key="col.columnName"
                size="small"
                style="margin: 2px;"
              >
                {{ col.columnName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalRows" label="记录数" width="100" />
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div class="test-section">
        <h4>维护操作</h4>
        <el-button type="warning" @click="clearAndReinitialize">
          <el-icon><Delete /></el-icon>
          清理并重新初始化连接
        </el-button>
        <el-button type="info" @click="showDebugInfo">
          <el-icon><InfoFilled /></el-icon>
          显示调试信息
        </el-button>
      </div>

      <!-- 调试信息 -->
      <div class="test-section" v-if="showDebug">
        <h4>调试信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前用户">{{ currentUser }}</el-descriptions-item>
          <el-descriptions-item label="JWT令牌">{{ hasToken ? '已设置' : '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="API基础URL">{{ apiBaseUrl }}</el-descriptions-item>
          <el-descriptions-item label="连接数量">{{ connections.length }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 10px;">
          <h5>localStorage内容:</h5>
          <pre>{{ localStorageContent }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Delete, InfoFilled } from '@element-plus/icons-vue'
import { DatabaseAPI, DatabaseConnectionManager } from '../../utils/databaseApi'
import { fixDatabaseConnections } from '../../utils/fixDatabaseConnections'

// 响应式数据
const connections = ref<any[]>([])
const testResults = ref<any[]>([])
const tables = ref<any[]>([])
const testingConnections = ref(new Set<string>())
const loadingTables = ref(new Set<string>())
const showDebug = ref(false)

// 调试信息
const currentUser = ref('')
const hasToken = ref(false)
const apiBaseUrl = ref('/api')
const localStorageContent = ref('')

// 刷新连接列表
const refreshConnections = () => {
  connections.value = DatabaseConnectionManager.getAllConnections()
  updateDebugInfo()
}

// 测试数据库连接
const testConnection = async (connection: any) => {
  testingConnections.value.add(connection.id)
  
  try {
    const result = await DatabaseAPI.testConnection({
      host: connection.host,
      port: connection.port,
      database: connection.database,
      username: connection.username,
      password: connection.password,
      schema: connection.schema
    })

    const testResult = {
      id: Date.now(),
      title: `连接测试 - ${connection.name}`,
      type: result.success ? 'success' : 'error',
      message: result.message,
      details: result.postgisVersion ? `PostGIS版本: ${result.postgisVersion}` : null
    }

    testResults.value.unshift(testResult)
    
    if (result.success) {
      ElMessage.success(`连接 ${connection.name} 测试成功`)
    } else {
      ElMessage.error(`连接 ${connection.name} 测试失败`)
    }
  } catch (error) {
    const testResult = {
      id: Date.now(),
      title: `连接测试 - ${connection.name}`,
      type: 'error',
      message: `请求失败: ${(error as Error).message}`,
      details: null
    }
    testResults.value.unshift(testResult)
    ElMessage.error(`连接测试失败: ${(error as Error).message}`)
  } finally {
    testingConnections.value.delete(connection.id)
  }
}

// 获取数据库表列表
const getTables = async (connection: any) => {
  loadingTables.value.add(connection.id)
  
  try {
    const result = await DatabaseAPI.getTables({
      host: connection.host,
      port: connection.port,
      database: connection.database,
      username: connection.username,
      password: connection.password,
      schema: connection.schema
    })

    tables.value = result
    
    const testResult = {
      id: Date.now(),
      title: `获取表列表 - ${connection.name}`,
      type: 'success',
      message: `成功获取 ${result.length} 个表`,
      details: result.map(t => `${t.schema}.${t.name}`).join('\n')
    }
    testResults.value.unshift(testResult)
    
    ElMessage.success(`成功获取 ${result.length} 个表`)
  } catch (error) {
    const testResult = {
      id: Date.now(),
      title: `获取表列表 - ${connection.name}`,
      type: 'error',
      message: `请求失败: ${(error as Error).message}`,
      details: null
    }
    testResults.value.unshift(testResult)
    ElMessage.error(`获取表列表失败: ${(error as Error).message}`)
  } finally {
    loadingTables.value.delete(connection.id)
  }
}

// 清理并重新初始化
const clearAndReinitialize = () => {
  try {
    fixDatabaseConnections()
    refreshConnections()
    testResults.value = []
    tables.value = []
    ElMessage.success('连接配置已清理并重新初始化')
  } catch (error) {
    ElMessage.error(`重新初始化失败: ${(error as Error).message}`)
  }
}

// 显示调试信息
const showDebugInfo = () => {
  showDebug.value = !showDebug.value
  if (showDebug.value) {
    updateDebugInfo()
  }
}

// 更新调试信息
const updateDebugInfo = () => {
  // 获取当前用户信息
  const userInfo = localStorage.getItem('user_info')
  currentUser.value = userInfo ? JSON.parse(userInfo).username || '未知' : '未登录'
  
  // 检查JWT令牌
  hasToken.value = !!localStorage.getItem('access_token')
  
  // 获取localStorage内容
  const lsContent: any = {}
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key) {
      lsContent[key] = localStorage.getItem(key)
    }
  }
  localStorageContent.value = JSON.stringify(lsContent, null, 2)
}

// 组件挂载
onMounted(() => {
  refreshConnections()
})
</script>

<style scoped>
.database-test-panel {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-section {
  margin: 20px 0;
}

.test-result {
  margin: 10px 0;
}

.result-details {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result-details pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
}
</style>
