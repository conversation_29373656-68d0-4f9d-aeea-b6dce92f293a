{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.2201082706451416, "root": {"boundingVolume": {"box": [48.419151306152344, -55.8214225769043, -35.8002815246582, 46.75798797607422, 0.0, 0.0, 0.0, 60.60940170288086, 0.0, 0.0, 0.0, 48.64400863647461]}, "children": [{"boundingVolume": {"box": [48.419151306152344, -76.02455139160156, -35.8002815246582, 46.75798797607422, 0.0, 0.0, 0.0, 40.40626907348633, 0.0, 0.0, 0.0, 48.64400863647461]}, "children": [{"boundingVolume": {"box": [21.143657684326172, -54.18930435180664, -58.73707580566406, 19.482494354248047, 0.0, 0.0, 0.0, 18.57101821899414, 0.0, 0.0, 0.0, 14.829460144042969]}, "content": {"uri": "Block_L18_47.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [67.90164184570312, -68.61257934570312, -64.17594909667969, 27.275493621826172, 0.0, 0.0, 0.0, 32.994293212890625, 0.0, 0.0, 0.0, 20.26833724975586]}, "children": [{"boundingVolume": {"box": [67.90164184570312, -68.61257934570312, -64.17594909667969, 27.275493621826172, 0.0, 0.0, 0.0, 32.994293212890625, 0.0, 0.0, 0.0, 20.26833724975586]}, "content": {"uri": "Block_L18_14.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_6.b3dm"}, "geometricError": 0.5020253658294678, "refine": "REPLACE"}, {"boundingVolume": {"box": [46.290367126464844, -80.47209167480469, -15.531944274902344, 44.62920379638672, 0.0, 0.0, 0.0, 35.958736419677734, 0.0, 0.0, 0.0, 28.37567138671875]}, "content": {"uri": "Block_L17_5.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L16_4.b3dm"}, "geometricError": 0.8331782221794128, "refine": "REPLACE"}, {"boundingVolume": {"box": [21.143657684326172, -23.833126068115234, -63.25722885131836, 19.482494354248047, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 16.22518539428711]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -23.833126068115234, -59.725982666015625, 9.741247177124023, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 12.693939208984375]}, "content": {"uri": "Block_L18_46.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.884904861450195, -23.833126068115234, -63.490936279296875, 9.741247177124023, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 15.863714218139648]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -23.833126068115234, -68.76805877685547, 9.741247177124023, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 10.58659553527832]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -23.833126068115234, -68.76300048828125, 9.741247177124023, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 10.581541061401367]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -28.743610382080078, -65.46928405761719, 9.741247177124023, 0.0, 0.0, 0.0, 6.874676704406738, 0.0, 0.0, 0.0, 7.287824630737305]}, "children": [{"boundingVolume": {"box": [24.39073944091797, -28.743610382080078, -65.44430541992188, 3.2470827102661133, 0.0, 0.0, 0.0, 6.874676704406738, 0.0, 0.0, 0.0, 7.262842178344727]}, "content": {"uri": "Block_L22_397.b3dm"}, "geometricError": 0.014601719565689564, "refine": "REPLACE"}, {"boundingVolume": {"box": [34.131988525390625, -28.743610382080078, -65.46791076660156, 6.49416446685791, 0.0, 0.0, 0.0, 6.874676704406738, 0.0, 0.0, 0.0, 7.286447525024414]}, "content": {"uri": "Block_L22_396.b3dm"}, "geometricError": 0.01653372123837471, "refine": "REPLACE"}], "content": {"uri": "Block_L21_299.b3dm"}, "geometricError": 0.030617201700806618, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.884904861450195, -16.958450317382812, -68.73773193359375, 9.741247177124023, 0.0, 0.0, 0.0, 4.910483360290527, 0.0, 0.0, 0.0, 10.556272506713867]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -16.958450317382812, -72.2441177368164, 9.741247177124023, 0.0, 0.0, 0.0, 4.910483360290527, 0.0, 0.0, 0.0, 7.0313262939453125]}, "content": {"uri": "Block_L22_395.b3dm"}, "geometricError": 0.014756913296878338, "refine": "REPLACE"}, {"boundingVolume": {"box": [27.6378231048584, -16.958450317382812, -61.69712829589844, 6.494165420532227, 0.0, 0.0, 0.0, 4.910483360290527, 0.0, 0.0, 0.0, 3.515665054321289]}, "content": {"uri": "Block_L22_394.b3dm"}, "geometricError": 0.012031570076942444, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, -19.004484176635742, -61.69712829589844, 3.247081756591797, 0.0, 0.0, 0.0, 2.8644485473632812, 0.0, 0.0, 0.0, 3.515665054321289]}, "content": {"uri": "Block_L22_393.b3dm"}, "geometricError": 0.013720568269491196, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, -14.094000816345215, -61.83735656738281, 3.247081756591797, 0.0, 0.0, 0.0, 2.046034812927246, 0.0, 0.0, 0.0, 3.375436782836914]}, "content": {"uri": "Block_L22_392.b3dm"}, "geometricError": 0.012770766392350197, "refine": "REPLACE"}], "content": {"uri": "Block_L21_298.b3dm"}, "geometricError": 0.026476087048649788, "refine": "REPLACE"}], "content": {"uri": "Block_L20_163.b3dm"}, "geometricError": 0.0569043904542923, "refine": "REPLACE"}], "content": {"uri": "Block_L19_88.b3dm"}, "geometricError": 0.11414285004138947, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.84799575805664, -23.833126068115234, -52.90434265136719, 9.704338073730469, 0.0, 0.0, 0.0, 11.785160064697266, 0.0, 0.0, 0.0, 5.277118682861328]}, "content": {"uri": "Block_L19_87.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_45.b3dm"}, "geometricError": 0.204672172665596, "refine": "REPLACE"}], "content": {"uri": "Block_L17_20.b3dm"}, "geometricError": 0.43055275082588196, "refine": "REPLACE"}, {"boundingVolume": {"box": [21.143657684326172, -3.629993438720703, -64.91272735595703, 19.482494354248047, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 14.722204208374023]}, "content": {"uri": "Block_L17_19.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [67.90164184570312, -15.415153503417969, -70.38159942626953, 27.275493621826172, 0.0, 0.0, 0.0, 20.20313262939453, 0.0, 0.0, 0.0, 12.978948593139648]}, "children": [{"boundingVolume": {"box": [67.90164184570312, -15.415153503417969, -70.83338165283203, 27.275493621826172, 0.0, 0.0, 0.0, 20.20313262939453, 0.0, 0.0, 0.0, 12.527166366577148]}, "content": {"uri": "Block_L18_42.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_18.b3dm"}, "geometricError": 0.6386911273002625, "refine": "REPLACE"}], "content": {"uri": "Block_L16_8.b3dm"}, "geometricError": 1.2201082706451416, "refine": "REPLACE"}}