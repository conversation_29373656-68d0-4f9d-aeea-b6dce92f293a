{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.1902594417333603, "root": {"boundingVolume": {"box": [60.31269073486328, 50.245025634765625, -69.12973022460938, 11.893539428710938, 0.0, 0.0, 0.0, 15.152349472045898, 0.0, 0.0, 0.0, 10.944416046142578]}, "children": [{"boundingVolume": {"box": [60.31269073486328, 41.406158447265625, -69.12820434570312, 11.893539428710938, 0.0, 0.0, 0.0, 6.313478469848633, 0.0, 0.0, 0.0, 10.942893981933594]}, "children": [{"boundingVolume": {"box": [53.369140625, 41.406158447265625, -69.12820434570312, 4.949987411499023, 0.0, 0.0, 0.0, 6.313478469848633, 0.0, 0.0, 0.0, 10.942893981933594]}, "children": [{"boundingVolume": {"box": [53.369140625, 41.406158447265625, -74.59944152832031, 4.949987411499023, 0.0, 0.0, 0.0, 6.313478469848633, 0.0, 0.0, 0.0, 5.471660614013672]}, "children": [{"boundingVolume": {"box": [53.369140625, 38.249420166015625, -74.59900665283203, 4.949987411499023, 0.0, 0.0, 0.0, 3.156740188598633, 0.0, 0.0, 0.0, 5.471229553222656]}, "content": {"uri": "Block_L22_229.b3dm"}, "geometricError": 0.014758746139705181, "refine": "REPLACE"}, {"boundingVolume": {"box": [53.369140625, 44.562896728515625, -74.46409606933594, 4.949987411499023, 0.0, 0.0, 0.0, 3.15673828125, 0.0, 0.0, 0.0, 5.336315155029297]}, "content": {"uri": "Block_L22_228.b3dm"}, "geometricError": 0.014205406419932842, "refine": "REPLACE"}], "content": {"uri": "Block_L21_216.b3dm"}, "geometricError": 0.02894992008805275, "refine": "REPLACE"}, {"boundingVolume": {"box": [55.486976623535156, 41.420249938964844, -63.65654373168945, 2.8321495056152344, 0.0, 0.0, 0.0, 6.299385070800781, 0.0, 0.0, 0.0, 5.471233367919922]}, "children": [{"boundingVolume": {"box": [55.62428283691406, 38.79550552368164, -63.65654373168945, 2.694843292236328, 0.0, 0.0, 0.0, 3.674640655517578, 0.0, 0.0, 0.0, 5.471233367919922]}, "content": {"uri": "Block_L22_227.b3dm"}, "geometricError": 0.011660999618470669, "refine": "REPLACE"}, {"boundingVolume": {"box": [55.486976623535156, 45.09489059448242, -66.39215850830078, 2.8321495056152344, 0.0, 0.0, 0.0, 2.624744415283203, 0.0, 0.0, 0.0, 2.735616683959961]}, "content": {"uri": "Block_L22_226.b3dm"}, "geometricError": 0.011689342558383942, "refine": "REPLACE"}, {"boundingVolume": {"box": [55.911643981933594, 45.09489059448242, -60.920928955078125, 2.4074840545654297, 0.0, 0.0, 0.0, 2.624744415283203, 0.0, 0.0, 0.0, 2.735616683959961]}, "content": {"uri": "Block_L23_397.b3dm"}, "geometricError": 0.01022197213023901, "refine": "REPLACE"}], "content": {"uri": "Block_L21_215.b3dm"}, "geometricError": 0.022382864728569984, "refine": "REPLACE"}], "content": {"uri": "Block_L20_121.b3dm"}, "geometricError": 0.052221305668354034, "refine": "REPLACE"}, {"boundingVolume": {"box": [65.25894165039062, 39.05646514892578, -68.79475402832031, 6.939817428588867, 0.0, 0.0, 0.0, 3.963785171508789, 0.0, 0.0, 0.0, 10.609443664550781]}, "children": [{"boundingVolume": {"box": [61.65100860595703, 38.76753234863281, -72.33123779296875, 3.331880569458008, 0.0, 0.0, 0.0, 3.674856185913086, 0.0, 0.0, 0.0, 7.072963714599609]}, "children": [{"boundingVolume": {"box": [61.65100860595703, 38.76753234863281, -72.33123779296875, 3.331880569458008, 0.0, 0.0, 0.0, 3.674856185913086, 0.0, 0.0, 0.0, 7.072963714599609]}, "content": {"uri": "Block_L22_225.b3dm"}, "geometricError": 0.012731579132378101, "refine": "REPLACE"}], "content": {"uri": "Block_L21_214.b3dm"}, "geometricError": 0.025479838252067566, "refine": "REPLACE"}, {"boundingVolume": {"box": [65.25483703613281, 39.05646514892578, -61.7217903137207, 6.935712814331055, 0.0, 0.0, 0.0, 3.963785171508789, 0.0, 0.0, 0.0, 3.536479949951172]}, "children": [{"boundingVolume": {"box": [61.20661926269531, 39.05646514892578, -61.7217903137207, 2.887491226196289, 0.0, 0.0, 0.0, 3.963785171508789, 0.0, 0.0, 0.0, 3.536479949951172]}, "content": {"uri": "Block_L23_396.b3dm"}, "geometricError": 0.01061436627060175, "refine": "REPLACE"}, {"boundingVolume": {"box": [68.1365966796875, 38.88773727416992, -61.7217903137207, 4.042491912841797, 0.0, 0.0, 0.0, 3.7950592041015625, 0.0, 0.0, 0.0, 3.536479949951172]}, "content": {"uri": "Block_L23_395.b3dm"}, "geometricError": 0.010341514833271503, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1395.b3dm"}, "geometricError": 0.021022126078605652, "refine": "REPLACE"}], "content": {"uri": "Block_L20_120.b3dm"}, "geometricError": 0.04531344026327133, "refine": "REPLACE"}], "content": {"uri": "Block_L19_66.b3dm"}, "geometricError": 0.09805436432361603, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.82295227050781, 56.55850601196289, -68.90129089355469, 4.403800964355469, 0.0, 0.0, 0.0, 8.838871002197266, 0.0, 0.0, 0.0, 10.715984344482422]}, "children": [{"boundingVolume": {"box": [51.468719482421875, 56.55850601196289, -73.36628723144531, 3.049570083618164, 0.0, 0.0, 0.0, 8.838871002197266, 0.0, 0.0, 0.0, 6.2509918212890625]}, "children": [{"boundingVolume": {"box": [51.468719482421875, 52.139068603515625, -73.36628723144531, 3.049570083618164, 0.0, 0.0, 0.0, 4.419435501098633, 0.0, 0.0, 0.0, 6.2509918212890625]}, "children": [{"boundingVolume": {"box": [50.99151611328125, 52.139068603515625, -76.49178314208984, 2.572366714477539, 0.0, 0.0, 0.0, 4.419435501098633, 0.0, 0.0, 0.0, 3.1254959106445312]}, "content": {"uri": "Block_L22_224.b3dm"}, "geometricError": 0.014931262470781803, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.11273956298828, 52.139068603515625, -70.24079132080078, 2.4055519104003906, 0.0, 0.0, 0.0, 4.419435501098633, 0.0, 0.0, 0.0, 3.1254959106445312]}, "content": {"uri": "Block_L22_223.b3dm"}, "geometricError": 0.012788473628461361, "refine": "REPLACE"}], "content": {"uri": "Block_L21_213.b3dm"}, "geometricError": 0.027245327830314636, "refine": "REPLACE"}, {"boundingVolume": {"box": [51.27996826171875, 60.977943420410156, -73.2172622680664, 2.860818862915039, 0.0, 0.0, 0.0, 4.419435501098633, 0.0, 0.0, 0.0, 6.101966857910156]}, "children": [{"boundingVolume": {"box": [51.27996826171875, 60.977943420410156, -73.20809173583984, 2.860818862915039, 0.0, 0.0, 0.0, 4.419435501098633, 0.0, 0.0, 0.0, 6.092796325683594]}, "content": {"uri": "Block_L22_222.b3dm"}, "geometricError": 0.012787369079887867, "refine": "REPLACE"}], "content": {"uri": "Block_L21_212.b3dm"}, "geometricError": 0.0255613811314106, "refine": "REPLACE"}], "content": {"uri": "Block_L20_119.b3dm"}, "geometricError": 0.05310728773474693, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.816566467285156, 56.55850601196289, -62.65030288696289, 4.39741325378418, 0.0, 0.0, 0.0, 8.838871002197266, 0.0, 0.0, 0.0, 4.464992523193359]}, "children": [{"boundingVolume": {"box": [54.17113494873047, 51.402496337890625, -62.65030288696289, 3.0319156646728516, 0.0, 0.0, 0.0, 3.682863235473633, 0.0, 0.0, 0.0, 4.464992523193359]}, "children": [{"boundingVolume": {"box": [54.08208465576172, 51.402496337890625, -64.51071166992188, 2.931039810180664, 0.0, 0.0, 0.0, 3.682863235473633, 0.0, 0.0, 0.0, 2.6045799255371094]}, "content": {"uri": "Block_L22_221.b3dm"}, "geometricError": 0.011151320300996304, "refine": "REPLACE"}, {"boundingVolume": {"box": [54.21836471557617, 51.402496337890625, -60.04572296142578, 2.9753074645996094, 0.0, 0.0, 0.0, 3.682863235473633, 0.0, 0.0, 0.0, 1.86041259765625]}, "content": {"uri": "Block_L23_394.b3dm"}, "geometricError": 0.009939977899193764, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1394.b3dm"}, "geometricError": 0.02103809081017971, "refine": "REPLACE"}, {"boundingVolume": {"box": [52.542381286621094, 60.241371154785156, -62.65030288696289, 4.123231887817383, 0.0, 0.0, 0.0, 5.156007766723633, 0.0, 0.0, 0.0, 4.464992523193359]}, "children": [{"boundingVolume": {"box": [53.923789978027344, 57.663368225097656, -62.65030288696289, 2.736734390258789, 0.0, 0.0, 0.0, 2.578004837036133, 0.0, 0.0, 0.0, 4.464992523193359]}, "content": {"uri": "Block_L23_393.b3dm"}, "geometricError": 0.010445518419146538, "refine": "REPLACE"}, {"boundingVolume": {"box": [51.561683654785156, 62.819374084472656, -62.65030288696289, 3.1425304412841797, 0.0, 0.0, 0.0, 2.5780029296875, 0.0, 0.0, 0.0, 4.464992523193359]}, "content": {"uri": "Block_L23_392.b3dm"}, "geometricError": 0.01058328989893198, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1393.b3dm"}, "geometricError": 0.020989004522562027, "refine": "REPLACE"}], "content": {"uri": "Block_L21_809.b3dm"}, "geometricError": 0.042036160826683044, "refine": "REPLACE"}], "content": {"uri": "Block_L19_65.b3dm"}, "geometricError": 0.09289142489433289, "refine": "REPLACE"}], "content": {"uri": "Block_L18_35.b3dm"}, "geometricError": 0.1902594417333603, "refine": "REPLACE"}}