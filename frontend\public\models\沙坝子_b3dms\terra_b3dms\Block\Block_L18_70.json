{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.17880000174045563, "root": {"boundingVolume": {"box": [-7.616215705871582, 84.84638977050781, -67.06905364990234, 9.277379035949707, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 9.797079086303711]}, "children": [{"boundingVolume": {"box": [-7.616215705871582, 84.84638977050781, -71.15020751953125, 9.277379035949707, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 5.715923309326172]}, "children": [{"boundingVolume": {"box": [-7.616215705871582, 84.84638977050781, -71.15020751953125, 9.277379035949707, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 5.715923309326172]}, "children": [{"boundingVolume": {"box": [-12.254905700683594, 84.84638977050781, -69.79913330078125, 4.6386895179748535, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 4.364841461181641]}, "children": [{"boundingVolume": {"box": [-12.254905700683594, 81.3399429321289, -68.98477935791016, 4.6386895179748535, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 3.5504913330078125]}, "content": {"uri": "Block_L22_726.b3dm"}, "geometricError": 0.012055190280079842, "refine": "REPLACE"}, {"boundingVolume": {"box": [-12.254905700683594, 89.75540161132812, -69.79913330078125, 4.6386895179748535, 0.0, 0.0, 0.0, 3.5064430236816406, 0.0, 0.0, 0.0, 4.364841461181641]}, "content": {"uri": "Block_L22_725.b3dm"}, "geometricError": 0.012405859306454659, "refine": "REPLACE"}], "content": {"uri": "Block_L21_465.b3dm"}, "geometricError": 0.02451220713555813, "refine": "REPLACE"}, {"boundingVolume": {"box": [-2.9775261878967285, 84.84638977050781, -71.14790344238281, 4.6386895179748535, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 5.713619232177734]}, "children": [{"boundingVolume": {"box": [-2.9775261878967285, 81.3399429321289, -70.86878967285156, 4.6386895179748535, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 5.434501647949219]}, "content": {"uri": "Block_L22_724.b3dm"}, "geometricError": 0.012547642923891544, "refine": "REPLACE"}, {"boundingVolume": {"box": [-2.9775261878967285, 89.75540161132812, -74.95698547363281, 4.6386895179748535, 0.0, 0.0, 0.0, 3.5064430236816406, 0.0, 0.0, 0.0, 1.904541015625]}, "content": {"uri": "Block_L22_723.b3dm"}, "geometricError": 0.01316493283957243, "refine": "REPLACE"}, {"boundingVolume": {"box": [-2.9775261878967285, 88.84184265136719, -69.24336242675781, 4.6386895179748535, 0.0, 0.0, 0.0, 2.592884063720703, 0.0, 0.0, 0.0, 3.8090782165527344]}, "content": {"uri": "Block_L22_722.b3dm"}, "geometricError": 0.011718456633388996, "refine": "REPLACE"}], "content": {"uri": "Block_L21_464.b3dm"}, "geometricError": 0.02548183687031269, "refine": "REPLACE"}], "content": {"uri": "Block_L20_250.b3dm"}, "geometricError": 0.049984872341156006, "refine": "REPLACE"}], "content": {"uri": "Block_L19_133.b3dm"}, "geometricError": 0.09982239454984665, "refine": "REPLACE"}, {"boundingVolume": {"box": [-7.616215705871582, 84.84638977050781, -61.35313415527344, 9.277379035949707, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-13.028019905090332, 84.84638977050781, -61.35313415527344, 3.865574836730957, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-13.028019905090332, 81.3399429321289, -61.35313415527344, 3.865574836730957, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-13.028019905090332, 81.3399429321289, -61.35313415527344, 3.865574836730957, 0.0, 0.0, 0.0, 4.909019470214844, 0.0, 0.0, 0.0, 4.081155776977539]}, "content": {"uri": "Block_L22_721.b3dm"}, "geometricError": 0.01116655021905899, "refine": "REPLACE"}], "content": {"uri": "Block_L21_463.b3dm"}, "geometricError": 0.02239947021007538, "refine": "REPLACE"}, {"boundingVolume": {"box": [-13.028019905090332, 89.75540161132812, -61.35313415527344, 3.865574836730957, 0.0, 0.0, 0.0, 3.5064430236816406, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-13.028019905090332, 89.75540161132812, -63.053611755371094, 3.865574836730957, 0.0, 0.0, 0.0, 3.5064430236816406, 0.0, 0.0, 0.0, 2.380674362182617]}, "content": {"uri": "Block_L23_731.b3dm"}, "geometricError": 0.010287855751812458, "refine": "REPLACE"}, {"boundingVolume": {"box": [-13.028019905090332, 89.75540161132812, -58.97245788574219, 3.865574836730957, 0.0, 0.0, 0.0, 3.5064430236816406, 0.0, 0.0, 0.0, 1.7004814147949219]}, "content": {"uri": "Block_L23_730.b3dm"}, "geometricError": 0.009881388396024704, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1558.b3dm"}, "geometricError": 0.020187653601169586, "refine": "REPLACE"}], "content": {"uri": "Block_L21_886.b3dm"}, "geometricError": 0.04210175201296806, "refine": "REPLACE"}, {"boundingVolume": {"box": [-3.750640869140625, 84.84638977050781, -61.35313415527344, 5.41180419921875, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-3.750640869140625, 84.84638977050781, -61.35313415527344, 5.41180419921875, 0.0, 0.0, 0.0, 8.415462493896484, 0.0, 0.0, 0.0, 4.081155776977539]}, "children": [{"boundingVolume": {"box": [-3.750640869140625, 82.04122924804688, -61.35313415527344, 5.41180419921875, 0.0, 0.0, 0.0, 5.610309600830078, 0.0, 0.0, 0.0, 4.081155776977539]}, "content": {"uri": "Block_L23_729.b3dm"}, "geometricError": 0.010064606554806232, "refine": "REPLACE"}, {"boundingVolume": {"box": [-6.45654296875, 90.45669555664062, -61.35313415527344, 2.705902099609375, 0.0, 0.0, 0.0, 2.8051528930664062, 0.0, 0.0, 0.0, 4.081155776977539]}, "content": {"uri": "Block_L23_728.b3dm"}, "geometricError": 0.009556643664836884, "refine": "REPLACE"}, {"boundingVolume": {"box": [-1.04473876953125, 90.2868881225586, -61.35313415527344, 2.705902099609375, 0.0, 0.0, 0.0, 2.635345458984375, 0.0, 0.0, 0.0, 4.081155776977539]}, "content": {"uri": "Block_L23_727.b3dm"}, "geometricError": 0.009603993035852909, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1557.b3dm"}, "geometricError": 0.01932167075574398, "refine": "REPLACE"}], "content": {"uri": "Block_L21_885.b3dm"}, "geometricError": 0.03867270424962044, "refine": "REPLACE"}], "content": {"uri": "Block_L20_467.b3dm"}, "geometricError": 0.08152364194393158, "refine": "REPLACE"}], "content": {"uri": "Block_L18_70.b3dm"}, "geometricError": 0.17880000174045563, "refine": "REPLACE"}}