import { apiClient } from './api'

export interface DatabaseConnection {
  id?: string
  name: string
  type: 'postgresql' | 'mysql' | 'sqlite'
  host: string
  port: number
  database: string
  username: string
  password: string
  schema?: string
}

export interface TestConnectionResult {
  success: boolean
  message: string
  version?: string
  postgisVersion?: string
}

export interface DatabaseTable {
  name: string
  schema: string
  type: 'table' | 'view'
  geometryColumns: GeometryColumn[]
  totalRows?: number
}

export interface GeometryColumn {
  columnName: string
  geometryType: string
  srid: number
  dimension: number
}

export interface LayerQueryParams {
  connectionId?: string
  tableName: string
  geometryColumn?: string
  whereClause?: string
  limit?: number
}

export interface LayerQueryResult {
  success: boolean
  data?: any
  totalFeatures?: number
  bounds?: [number, number, number, number]
  error?: string
}

export interface TableInfo {
  table: {
    table_name: string
    table_schema: string
    table_type: string
    table_comment?: string
  }
  columns: Array<{
    column_name: string
    data_type: string
    is_nullable: string
    column_default?: string
    character_maximum_length?: number
    numeric_precision?: number
    numeric_scale?: number
  }>
  geometryColumns: GeometryColumn[]
  totalRows?: number
}

/**
 * 数据库API客户端
 */
export class DatabaseAPI {
  /**
   * 测试数据库连接
   */
  static async testConnection(connection: Omit<DatabaseConnection, 'id' | 'name' | 'type'>): Promise<TestConnectionResult> {
    const response = await apiClient.post<TestConnectionResult>('/database/test-connection', connection)
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.error || '连接测试失败')
    }
  }

  /**
   * 获取数据库表列表
   */
  static async getTables(connection: Omit<DatabaseConnection, 'id' | 'name' | 'type'>): Promise<DatabaseTable[]> {
    const response = await apiClient.post<DatabaseTable[]>('/database/tables', connection)
    
    if (response.success) {
      return response.data || []
    } else {
      throw new Error(response.error || '获取表列表失败')
    }
  }

  /**
   * 获取表详细信息
   */
  static async getTableInfo(
    connection: Omit<DatabaseConnection, 'id' | 'name' | 'type'>, 
    tableName: string
  ): Promise<TableInfo> {
    const response = await apiClient.post<TableInfo>('/database/table-info', {
      connectionConfig: connection,
      tableName
    })
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.error || '获取表信息失败')
    }
  }

  /**
   * 查询图层数据
   */
  static async queryLayer(
    queryParams: LayerQueryParams,
    connection: Omit<DatabaseConnection, 'id' | 'name' | 'type'>
  ): Promise<LayerQueryResult> {
    const response = await apiClient.post<LayerQueryResult>('/database/query-layer', {
      queryDto: queryParams,
      connectionConfig: connection
    })
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.error || '查询图层失败')
    }
  }

  /**
   * 预览图层数据（限制100条）
   */
  static async previewLayer(
    queryParams: LayerQueryParams,
    connection: Omit<DatabaseConnection, 'id' | 'name' | 'type'>
  ): Promise<LayerQueryResult> {
    const response = await apiClient.post<LayerQueryResult>('/database/preview-layer', {
      queryDto: { ...queryParams, limit: Math.min(queryParams.limit || 100, 100) },
      connectionConfig: connection
    })
    
    if (response.success) {
      return response.data
    } else {
      throw new Error(response.error || '预览图层失败')
    }
  }
}

/**
 * 数据库连接管理器
 */
export class DatabaseConnectionManager {
  private static connections: Map<string, DatabaseConnection> = new Map()

  /**
   * 添加连接配置
   */
  static addConnection(connection: DatabaseConnection): string {
    const id = connection.id || `conn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const connectionWithId = { ...connection, id }
    this.connections.set(id, connectionWithId)
    
    // 保存到localStorage
    this.saveToStorage()
    
    return id
  }

  /**
   * 获取连接配置
   */
  static getConnection(id: string): DatabaseConnection | undefined {
    return this.connections.get(id)
  }

  /**
   * 获取所有连接
   */
  static getAllConnections(): DatabaseConnection[] {
    return Array.from(this.connections.values())
  }

  /**
   * 删除连接
   */
  static removeConnection(id: string): boolean {
    const result = this.connections.delete(id)
    this.saveToStorage()
    return result
  }

  /**
   * 更新连接
   */
  static updateConnection(id: string, connection: Partial<DatabaseConnection>): boolean {
    const existing = this.connections.get(id)
    if (existing) {
      this.connections.set(id, { ...existing, ...connection })
      this.saveToStorage()
      return true
    }
    return false
  }

  /**
   * 保存到本地存储
   */
  private static saveToStorage(): void {
    try {
      const connectionsArray = Array.from(this.connections.values())
      // 不保存密码到localStorage，出于安全考虑
      const connectionsWithoutPassword = connectionsArray.map(conn => ({
        ...conn,
        password: '' // 清空密码
      }))
      localStorage.setItem('database-connections', JSON.stringify(connectionsWithoutPassword))
    } catch (error) {
      console.warn('Failed to save connections to localStorage:', error)
    }
  }

  /**
   * 从本地存储加载
   */
  static loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('database-connections')
      if (stored) {
        const connections: DatabaseConnection[] = JSON.parse(stored)
        connections.forEach(conn => {
          if (conn.id) {
            this.connections.set(conn.id, conn)
          }
        })
      }
    } catch (error) {
      console.warn('Failed to load connections from localStorage:', error)
    }
  }

  /**
   * 初始化默认连接
   */
  static initializeDefaults(): void {
    // 添加默认的webgis_db连接
    if (this.connections.size === 0) {
      // 添加宿主机PostgreSQL连接（端口5432）
      this.addConnection({
        name: 'WebGIS主数据库(宿主机)',
        type: 'postgresql',
        host: '*************',
        port: 5432,
        database: 'webgis_db',
        username: 'testUser',
        password: '1234',
        schema: 'public'
      })

      // 添加容器PostgreSQL连接（端口5433）
      this.addConnection({
        name: 'WebGIS主数据库(容器)',
        type: 'postgresql',
        host: '*************',
        port: 5433,
        database: 'webgis_db',
        username: 'mtdt_user',
        password: 'mtdt_secure_password_2024',
        schema: 'public'
      })

      // 添加Docker内部连接（仅用于容器内部通信）
      this.addConnection({
        name: 'WebGIS数据库(Docker内部)',
        type: 'postgresql',
        host: 'host.docker.internal',
        port: 5432,
        database: 'webgis_db',
        username: 'testUser',
        password: '1234',
        schema: 'public'
      })
    }
  }
}

// 初始化连接管理器
DatabaseConnectionManager.loadFromStorage()
DatabaseConnectionManager.initializeDefaults()

export default DatabaseAPI
