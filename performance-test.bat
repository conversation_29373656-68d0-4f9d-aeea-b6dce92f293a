@echo off
echo ========================================
echo MTDT System Performance Test
echo ========================================
echo.

echo 1. System Resource Usage
echo =========================
echo.

echo Docker Container Resource Usage:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
echo.

echo 2. Response Time Test
echo ======================
echo.

echo Testing Frontend Response Time...
for /l %%i in (1,1,5) do (
    echo Test %%i:
    curl -w "Response Time: %%{time_total}s\n" -o nul -s http://localhost:5174
)
echo.

echo Testing Backend API Response Time...
for /l %%i in (1,1,5) do (
    echo Test %%i:
    curl -w "Response Time: %%{time_total}s\n" -o nul -s http://localhost:8000/api/health
)
echo.

echo 3. Database Performance
echo ========================
echo.

echo Testing Database Connection...
docker-compose -f docker-compose.internal.yml exec -T postgres psql -U mtdt_user -d webgis_db -c "\timing on" -c "SELECT COUNT(*) FROM system.users;" -c "\timing off"
echo.

echo 4. Memory Usage Analysis
echo =========================
echo.

echo System Memory Usage:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:table
echo.

echo Docker Memory Usage:
docker system df
echo.

echo 5. Disk I/O Performance
echo ========================
echo.

echo Disk Space Usage:
dir /s /-c | find "bytes"
echo.

echo Docker Volume Usage:
docker volume ls
echo.

echo 6. Network Performance
echo =======================
echo.

echo Testing Network Latency...
ping -n 4 localhost
echo.

echo Testing Port Connectivity...
netstat -an | findstr ":5174\|:8080\|:8000\|:5432\|:6379"
echo.

echo 7. Performance Recommendations
echo ===============================
echo.

echo Checking for performance issues...

REM Check if frontend build exists
if not exist "frontend\dist" (
    echo [RECOMMENDATION] Build frontend for production: npm run build
)

REM Check Docker image sizes
echo Docker Image Sizes:
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | findstr "v107\|postgres\|redis"
echo.

echo Performance Optimization Tips:
echo - Use production builds for better performance
echo - Enable gzip compression in nginx
echo - Optimize database queries
echo - Use Redis for caching
echo - Monitor resource usage regularly
echo.

echo ========================================
echo Performance Test Complete
echo ========================================
echo.

echo For continuous monitoring, consider:
echo 1. Setting up monitoring dashboards
echo 2. Implementing health checks
echo 3. Using performance profiling tools
echo 4. Regular performance testing
echo.

pause
