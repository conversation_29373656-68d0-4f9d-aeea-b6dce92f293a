# MTDT Internal Network Deployment Script
# PowerShell version for better compatibility

Write-Host "Starting MTDT Internal Network Deployment..." -ForegroundColor Green

# Check Docker installation
try {
    $dockerVersion = docker --version
    Write-Host "Docker found: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    $composeVersion = docker-compose --version
    Write-Host "Docker Compose found: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check environment file
if (-not (Test-Path ".env")) {
    Write-Host "Creating environment variables file..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
    }
    Write-Host "WARNING: Please edit .env file to configure your environment variables" -ForegroundColor Yellow
    Write-Host "Please update:" -ForegroundColor Yellow
    Write-Host "  - JWT_SECRET" -ForegroundColor Yellow
    Write-Host "  - POSTGRES_PASSWORD" -ForegroundColor Yellow
    Write-Host "  - Mail configuration" -ForegroundColor Yellow
    Read-Host "Press Enter to continue"
}

# Get local IP address
Write-Host "Detecting local IP address..." -ForegroundColor Cyan
$localIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "以太网*" | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"} | Select-Object -First 1).IPAddress

if (-not $localIP) {
    $localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -ne "127.0.0.1" -and $_.IPAddress -notlike "169.254.*"} | Select-Object -First 1).IPAddress
}

# Display network info
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MTDT Internal Network Deployment" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Local IP Address: $localIP" -ForegroundColor Cyan
Write-Host ""

# Stop existing containers
Write-Host "Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.internal.yml down

# Build and start services
Write-Host "Building and starting services for internal network..." -ForegroundColor Yellow
docker-compose -f docker-compose.internal.yml up --build -d

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service status
Write-Host "Checking service status..." -ForegroundColor Yellow
docker-compose -f docker-compose.internal.yml ps

# Display access information
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Internal Network Access URLs:" -ForegroundColor White
Write-Host "  Frontend (Local): http://localhost:8080" -ForegroundColor White
Write-Host "  Frontend (Network): http://$localIP:8080" -ForegroundColor White
Write-Host "  Backend API (Local): http://localhost:8000/api" -ForegroundColor White
Write-Host "  Backend API (Network): http://$localIP:8000/api" -ForegroundColor White
Write-Host ""
Write-Host "Database connection:" -ForegroundColor White
Write-Host "  Host: $localIP" -ForegroundColor White
Write-Host "  Port: 5432" -ForegroundColor White
Write-Host "  Database: webgis_db" -ForegroundColor White
Write-Host "  User: mtdt_user" -ForegroundColor White
Write-Host ""
Write-Host "Redis connection:" -ForegroundColor White
Write-Host "  Host: $localIP" -ForegroundColor White
Write-Host "  Port: 6379" -ForegroundColor White
Write-Host ""
Write-Host "Management commands:" -ForegroundColor Yellow
Write-Host "  View logs: docker-compose -f docker-compose.internal.yml logs -f" -ForegroundColor Yellow
Write-Host "  Stop services: docker-compose -f docker-compose.internal.yml down" -ForegroundColor Yellow
Write-Host "  Restart services: docker-compose -f docker-compose.internal.yml restart" -ForegroundColor Yellow
Write-Host ""
Write-Host "Network users can access the application at:" -ForegroundColor Green
Write-Host "  http://$localIP:8080" -ForegroundColor Green
Write-Host ""

Read-Host "Press Enter to exit"
