{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.18852806091308594, "root": {"boundingVolume": {"box": [-60.96114730834961, 149.81373596191406, -59.399566650390625, 6.958034515380859, 0.0, 0.0, 0.0, 11.310379028320312, 0.0, 0.0, 0.0, 10.464326858520508]}, "children": [{"boundingVolume": {"box": [-60.96114730834961, 144.15853881835938, -59.394927978515625, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 10.459692001342773]}, "children": [{"boundingVolume": {"box": [-60.96114730834961, 144.15853881835938, -62.88231658935547, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 6.9723052978515625]}, "children": [{"boundingVolume": {"box": [-60.96114730834961, 144.15853881835938, -62.88231658935547, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 6.9723052978515625]}, "children": [{"boundingVolume": {"box": [-60.96114730834961, 144.15853881835938, -62.88231658935547, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 6.9723052978515625]}, "content": {"uri": "Block_L22_595.b3dm"}, "geometricError": 0.012494985945522785, "refine": "REPLACE"}], "content": {"uri": "Block_L21_398.b3dm"}, "geometricError": 0.024995876476168633, "refine": "REPLACE"}], "content": {"uri": "Block_L20_218.b3dm"}, "geometricError": 0.04999532178044319, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.96114730834961, 144.15853881835938, -52.42262268066406, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 3.487386703491211]}, "children": [{"boundingVolume": {"box": [-64.4401626586914, 141.33094787597656, -53.74394607543945, 3.4790172576904297, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.166065216064453]}, "children": [{"boundingVolume": {"box": [-66.17967224121094, 141.33094787597656, -53.74394607543945, 1.7395095825195312, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.166065216064453]}, "content": {"uri": "Block_L22_594.b3dm"}, "geometricError": 0.01146533153951168, "refine": "REPLACE"}, {"boundingVolume": {"box": [-62.700653076171875, 141.33094787597656, -53.87142562866211, 1.7395076751708984, 0.0, 0.0, 0.0, 2.8275909423828125, 0.0, 0.0, 0.0, 2.038585662841797]}, "content": {"uri": "Block_L22_593.b3dm"}, "geometricError": 0.011540290899574757, "refine": "REPLACE"}], "content": {"uri": "Block_L21_397.b3dm"}, "geometricError": 0.023015493527054787, "refine": "REPLACE"}, {"boundingVolume": {"box": [-64.4401626586914, 146.98614501953125, -53.73830032348633, 3.4790172576904297, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.171710968017578]}, "children": [{"boundingVolume": {"box": [-66.17967224121094, 146.98614501953125, -53.73830032348633, 1.7395095825195312, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.171710968017578]}, "content": {"uri": "Block_L22_592.b3dm"}, "geometricError": 0.011855270713567734, "refine": "REPLACE"}, {"boundingVolume": {"box": [-62.700653076171875, 146.98614501953125, -53.88615417480469, 1.7395076751708984, 0.0, 0.0, 0.0, 2.8275985717773438, 0.0, 0.0, 0.0, 2.0238571166992188]}, "content": {"uri": "Block_L22_591.b3dm"}, "geometricError": 0.01174978818744421, "refine": "REPLACE"}], "content": {"uri": "Block_L21_396.b3dm"}, "geometricError": 0.02361760102212429, "refine": "REPLACE"}, {"boundingVolume": {"box": [-57.48213195800781, 144.15853881835938, -52.42262268066406, 3.4790172576904297, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 3.487386703491211]}, "children": [{"boundingVolume": {"box": [-57.48213195800781, 141.80221557617188, -54.135986328125, 3.4790172576904297, 0.0, 0.0, 0.0, 3.298858642578125, 0.0, 0.0, 0.0, 1.7740249633789062]}, "content": {"uri": "Block_L22_590.b3dm"}, "geometricError": 0.012116738595068455, "refine": "REPLACE"}, {"boundingVolume": {"box": [-57.48213195800781, 147.4573974609375, -52.423858642578125, 3.4790172576904297, 0.0, 0.0, 0.0, 2.3563308715820312, 0.0, 0.0, 0.0, 3.4861526489257812]}, "content": {"uri": "Block_L22_589.b3dm"}, "geometricError": 0.011705536395311356, "refine": "REPLACE"}], "content": {"uri": "Block_L21_395.b3dm"}, "geometricError": 0.02385692112147808, "refine": "REPLACE"}], "content": {"uri": "Block_L20_217.b3dm"}, "geometricError": 0.04720620810985565, "refine": "REPLACE"}], "content": {"uri": "Block_L19_112.b3dm"}, "geometricError": 0.0954638198018074, "refine": "REPLACE"}, {"boundingVolume": {"box": [-60.96114730834961, 155.46893310546875, -59.337066650390625, 6.958034515380859, 0.0, 0.0, 0.0, 5.655189514160156, 0.0, 0.0, 0.0, 10.401830673217773]}, "content": {"uri": "Block_L19_111.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_58.b3dm"}, "geometricError": 0.18852806091308594, "refine": "REPLACE"}}