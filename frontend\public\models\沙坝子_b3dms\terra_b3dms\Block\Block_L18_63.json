{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.1824650913476944, "root": {"boundingVolume": {"box": [-99.02471923828125, 125.30791473388672, -42.1064453125, 10.642669677734375, 0.0, 0.0, 0.0, 13.195442199707031, 0.0, 0.0, 0.0, 7.890092849731445]}, "children": [{"boundingVolume": {"box": [-101.63179016113281, 119.809814453125, -42.1064453125, 8.035598754882812, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 7.890092849731445]}, "children": [{"boundingVolume": {"box": [-106.33245849609375, 119.809814453125, -42.1064453125, 3.3349266052246094, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 7.890092849731445]}, "children": [{"boundingVolume": {"box": [-106.33245849609375, 116.60258483886719, -46.70899963378906, 3.3349266052246094, 0.0, 0.0, 0.0, 4.490116119384766, 0.0, 0.0, 0.0, 3.287538528442383]}, "children": [{"boundingVolume": {"box": [-106.33245849609375, 116.60258483886719, -46.70899963378906, 3.3349266052246094, 0.0, 0.0, 0.0, 4.490116119384766, 0.0, 0.0, 0.0, 3.287538528442383]}, "content": {"uri": "Block_L22_669.b3dm"}, "geometricError": 0.011247186921536922, "refine": "REPLACE"}], "content": {"uri": "Block_L21_437.b3dm"}, "geometricError": 0.0225115604698658, "refine": "REPLACE"}, {"boundingVolume": {"box": [-106.33245849609375, 124.2999267578125, -46.70899963378906, 3.3349266052246094, 0.0, 0.0, 0.0, 3.207225799560547, 0.0, 0.0, 0.0, 3.287538528442383]}, "children": [{"boundingVolume": {"box": [-106.33245849609375, 124.2999267578125, -46.70899963378906, 3.3349266052246094, 0.0, 0.0, 0.0, 3.207225799560547, 0.0, 0.0, 0.0, 3.287538528442383]}, "content": {"uri": "Block_L22_668.b3dm"}, "geometricError": 0.01166441198438406, "refine": "REPLACE"}], "content": {"uri": "Block_L21_436.b3dm"}, "geometricError": 0.02336481213569641, "refine": "REPLACE"}, {"boundingVolume": {"box": [-106.33245849609375, 119.809814453125, -38.818904876708984, 3.3349266052246094, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 4.6025543212890625]}, "children": [{"boundingVolume": {"box": [-106.33245849609375, 119.809814453125, -38.818904876708984, 3.3349266052246094, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 4.6025543212890625]}, "content": {"uri": "Block_L23_627.b3dm"}, "geometricError": 0.009947902522981167, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1503.b3dm"}, "geometricError": 0.019922900944948196, "refine": "REPLACE"}], "content": {"uri": "Block_L20_237.b3dm"}, "geometricError": 0.04422946646809578, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.32864379882812, 119.809814453125, -44.425235748291016, 4.668895721435547, 0.0, 0.0, 0.0, 7.6973419189453125, 0.0, 0.0, 0.0, 5.571300506591797]}, "children": [{"boundingVolume": {"box": [-98.32864379882812, 115.3197021484375, -46.62663269042969, 4.668895721435547, 0.0, 0.0, 0.0, 3.207225799560547, 0.0, 0.0, 0.0, 3.369905471801758]}, "children": [{"boundingVolume": {"box": [-101.05216217041016, 115.3197021484375, -46.62663269042969, 1.94537353515625, 0.0, 0.0, 0.0, 3.207225799560547, 0.0, 0.0, 0.0, 3.369905471801758]}, "content": {"uri": "Block_L22_667.b3dm"}, "geometricError": 0.011845456436276436, "refine": "REPLACE"}, {"boundingVolume": {"box": [-96.38327026367188, 113.96858215332031, -46.765472412109375, 2.723522186279297, 0.0, 0.0, 0.0, 1.8561058044433594, 0.0, 0.0, 0.0, 3.2310657501220703]}, "content": {"uri": "Block_L22_666.b3dm"}, "geometricError": 0.012246799655258656, "refine": "REPLACE"}], "content": {"uri": "Block_L21_435.b3dm"}, "geometricError": 0.02405824512243271, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.62051391601562, 123.01704406738281, -44.436195373535156, 4.377017974853516, 0.0, 0.0, 0.0, 4.490116119384766, 0.0, 0.0, 0.0, 5.560340881347656]}, "children": [{"boundingVolume": {"box": [-98.62237548828125, 123.01704406738281, -44.436195373535156, 4.375160217285156, 0.0, 0.0, 0.0, 4.490116119384766, 0.0, 0.0, 0.0, 5.560340881347656]}, "content": {"uri": "Block_L22_665.b3dm"}, "geometricError": 0.011923709884285927, "refine": "REPLACE"}], "content": {"uri": "Block_L21_434.b3dm"}, "geometricError": 0.02390275150537491, "refine": "REPLACE"}], "content": {"uri": "Block_L20_236.b3dm"}, "geometricError": 0.048040710389614105, "refine": "REPLACE"}], "content": {"uri": "Block_L19_123.b3dm"}, "geometricError": 0.091685451567173, "refine": "REPLACE"}, {"boundingVolume": {"box": [-99.08025360107422, 133.0052490234375, -42.231388092041016, 10.587135314941406, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 7.765148162841797]}, "children": [{"boundingVolume": {"box": [-105.2560806274414, 133.0052490234375, -42.233116149902344, 4.411308288574219, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 7.763418197631836]}, "children": [{"boundingVolume": {"box": [-105.2560806274414, 133.0052490234375, -46.76332473754883, 4.411308288574219, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 3.2332115173339844]}, "children": [{"boundingVolume": {"box": [-105.2560806274414, 130.25619506835938, -46.76332473754883, 4.411308288574219, 0.0, 0.0, 0.0, 2.7490463256835938, 0.0, 0.0, 0.0, 3.2332115173339844]}, "content": {"uri": "Block_L22_664.b3dm"}, "geometricError": 0.011944224126636982, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.2560806274414, 135.75430297851562, -46.76332473754883, 4.411308288574219, 0.0, 0.0, 0.0, 2.749053955078125, 0.0, 0.0, 0.0, 3.2332115173339844]}, "content": {"uri": "Block_L22_663.b3dm"}, "geometricError": 0.011155062355101109, "refine": "REPLACE"}], "content": {"uri": "Block_L21_433.b3dm"}, "geometricError": 0.02319703809916973, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.32947540283203, 133.0052490234375, -38.999908447265625, 4.337913513183594, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 4.530206680297852]}, "children": [{"boundingVolume": {"box": [-105.32947540283203, 129.79803466796875, -39.00361633300781, 4.337913513183594, 0.0, 0.0, 0.0, 2.2908782958984375, 0.0, 0.0, 0.0, 4.526498794555664]}, "content": {"uri": "Block_L23_626.b3dm"}, "geometricError": 0.010018902830779552, "refine": "REPLACE"}, {"boundingVolume": {"box": [-104.95034790039062, 135.296142578125, -39.29331588745117, 3.7599639892578125, 0.0, 0.0, 0.0, 3.2072219848632812, 0.0, 0.0, 0.0, 4.236797332763672]}, "content": {"uri": "Block_L23_625.b3dm"}, "geometricError": 0.009932039305567741, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1502.b3dm"}, "geometricError": 0.019991377368569374, "refine": "REPLACE"}], "content": {"uri": "Block_L21_859.b3dm"}, "geometricError": 0.04372772201895714, "refine": "REPLACE"}, {"boundingVolume": {"box": [-94.6689453125, 133.0052490234375, -47.398406982421875, 6.1758270263671875, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 2.5981292724609375]}, "children": [{"boundingVolume": {"box": [-98.27151489257812, 133.0052490234375, -47.404319763183594, 2.573261260986328, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 2.5922164916992188]}, "children": [{"boundingVolume": {"box": [-98.27151489257812, 130.71438598632812, -48.095767974853516, 2.573261260986328, 0.0, 0.0, 0.0, 3.2072219848632812, 0.0, 0.0, 0.0, 1.9007682800292969]}, "content": {"uri": "Block_L22_662.b3dm"}, "geometricError": 0.012412232346832752, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.27151489257812, 136.2124786376953, -47.404319763183594, 2.573261260986328, 0.0, 0.0, 0.0, 2.2908782958984375, 0.0, 0.0, 0.0, 2.5922164916992188]}, "content": {"uri": "Block_L22_661.b3dm"}, "geometricError": 0.01166432723402977, "refine": "REPLACE"}], "content": {"uri": "Block_L21_432.b3dm"}, "geometricError": 0.024029269814491272, "refine": "REPLACE"}, {"boundingVolume": {"box": [-92.09568786621094, 133.0052490234375, -47.43202209472656, 3.6025657653808594, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 2.564512252807617]}, "children": [{"boundingVolume": {"box": [-92.09568786621094, 133.0052490234375, -47.43202209472656, 3.6025657653808594, 0.0, 0.0, 0.0, 5.498100280761719, 0.0, 0.0, 0.0, 2.564512252807617]}, "content": {"uri": "Block_L22_660.b3dm"}, "geometricError": 0.011714682914316654, "refine": "REPLACE"}], "content": {"uri": "Block_L21_431.b3dm"}, "geometricError": 0.023461349308490753, "refine": "REPLACE"}], "content": {"uri": "Block_L20_235.b3dm"}, "geometricError": 0.04765927419066429, "refine": "REPLACE"}], "content": {"uri": "Block_L19_122.b3dm"}, "geometricError": 0.09066271781921387, "refine": "REPLACE"}], "content": {"uri": "Block_L18_63.b3dm"}, "geometricError": 0.1824650913476944, "refine": "REPLACE"}}