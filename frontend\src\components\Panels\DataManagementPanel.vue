<template>
  <div class="data-management-panel">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 数据导入 -->
      <el-tab-pane label="数据导入" name="import">
        <div class="import-section">
          <el-upload
            ref="uploadRef"
            class="upload-area"
            drag
            :auto-upload="false"
            :show-file-list="false"
            :accept="acceptedFormats"
            @change="handleFileSelect"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持 GeoJSON、KML、CSV、Excel 格式
            </div>
          </el-upload>

          <!-- 导入选项 -->
          <div v-if="selectedFile" class="import-options">
            <h4>导入选项</h4>
            <el-form :model="importOptions" label-width="100px" size="small">
              <el-form-item label="坐标系">
                <el-select v-model="importOptions.coordinateSystem">
                  <el-option label="WGS84" value="WGS84" />
                  <el-option label="GCJ02" value="GCJ02" />
                  <el-option label="BD09" value="BD09" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="编码格式">
                <el-select v-model="importOptions.encoding">
                  <el-option label="UTF-8" value="UTF-8" />
                  <el-option label="GBK" value="GBK" />
                  <el-option label="GB2312" value="GB2312" />
                </el-select>
              </el-form-item>

              <el-form-item v-if="needsCoordinateFields" label="经度字段">
                <el-input v-model="importOptions.lngField" placeholder="lng, longitude, x" />
              </el-form-item>

              <el-form-item v-if="needsCoordinateFields" label="纬度字段">
                <el-input v-model="importOptions.latField" placeholder="lat, latitude, y" />
              </el-form-item>
            </el-form>

            <div class="import-actions">
              <el-button type="primary" @click="executeImport" :loading="importing">
                <el-icon><Upload /></el-icon>
                导入数据
              </el-button>
              <el-button @click="clearSelection">
                取消
              </el-button>
            </div>
          </div>

          <!-- 导入结果 -->
          <div v-if="importResult" class="import-result">
            <el-alert
              :title="importResult.success ? '导入成功' : '导入失败'"
              :type="importResult.success ? 'success' : 'error'"
              :description="importResult.error || `成功导入 ${importResult.metadata?.recordCount} 条记录`"
              show-icon
              :closable="false"
            />
            
            <div v-if="importResult.success && importResult.metadata" class="result-details">
              <el-descriptions :column="2" size="small" border>
                <el-descriptions-item label="文件名">{{ importResult.metadata.filename }}</el-descriptions-item>
                <el-descriptions-item label="文件大小">{{ formatFileSize(importResult.metadata.fileSize) }}</el-descriptions-item>
                <el-descriptions-item label="记录数量">{{ importResult.metadata.recordCount }}</el-descriptions-item>
                <el-descriptions-item label="几何类型">{{ importResult.metadata.geometryType || '无' }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 数据导出 -->
      <el-tab-pane label="数据导出" name="export">
        <div class="export-section">
          <h4>选择导出数据</h4>
          <el-radio-group v-model="exportSource" @change="handleExportSourceChange">
            <el-radio label="current">当前显示的图层</el-radio>
            <el-radio label="selected">选中的要素</el-radio>
            <el-radio label="all">所有图层数据</el-radio>
          </el-radio-group>

          <!-- 图层选择 -->
          <div v-if="exportSource === 'current'" class="layer-selection">
            <el-select v-model="selectedLayerId" placeholder="选择要导出的图层">
              <el-option
                v-for="layer in availableLayers"
                :key="layer.id"
                :label="layer.name"
                :value="layer.id"
              />
            </el-select>
          </div>

          <!-- 导出选项 -->
          <div class="export-options">
            <h4>导出选项</h4>
            <el-form :model="exportOptions" label-width="100px" size="small">
              <el-form-item label="导出格式">
                <el-select v-model="exportOptions.format">
                  <el-option
                    v-for="format in supportedExportFormats"
                    :key="format.value"
                    :label="format.label"
                    :value="format.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="文件名">
                <el-input v-model="exportOptions.filename" placeholder="export_data" />
              </el-form-item>

              <el-form-item label="坐标系">
                <el-select v-model="exportOptions.coordinateSystem">
                  <el-option label="WGS84" value="WGS84" />
                  <el-option label="GCJ02" value="GCJ02" />
                  <el-option label="BD09" value="BD09" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-checkbox v-model="exportOptions.includeAttributes">
                  包含属性信息
                </el-checkbox>
              </el-form-item>
            </el-form>

            <div class="export-actions">
              <el-button type="primary" @click="executeExport" :loading="exporting">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 数据统计 -->
      <el-tab-pane label="数据统计" name="statistics">
        <div class="statistics-section">
          <el-card>
            <template #header>
              <span>数据概览</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="refreshStatistics">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="总图层数" :value="statistics.totalLayers" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="总要素数" :value="statistics.totalFeatures" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="数据大小" :value="formatFileSize(statistics.totalSize)" />
              </el-col>
            </el-row>
          </el-card>

          <!-- 图层详情 -->
          <el-table :data="layerStatistics" style="margin-top: 20px">
            <el-table-column prop="name" label="图层名称" />
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="featureCount" label="要素数量" />
            <el-table-column prop="geometryType" label="几何类型" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="exportLayer(scope.row)">
                  导出
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Upload, Download, Refresh } from '@element-plus/icons-vue'
import { DataImporter, type ImportOptions, type ImportResult } from '../../utils/dataImporter'
import { DataExporter, type ExportOptions, type ExportData } from '../../utils/dataExporter'

// Props
interface Props {
  layerManager?: any
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('import')
const selectedFile = ref<File | null>(null)
const importing = ref(false)
const exporting = ref(false)
const importResult = ref<ImportResult | null>(null)

// 导入选项
const importOptions = ref<ImportOptions>({
  coordinateSystem: 'WGS84',
  encoding: 'UTF-8',
  lngField: '',
  latField: ''
})

// 导出选项
const exportSource = ref('current')
const selectedLayerId = ref('')
const exportOptions = ref<ExportOptions>({
  format: 'geojson',
  filename: '',
  includeAttributes: true,
  coordinateSystem: 'WGS84'
})

// 统计数据
const statistics = ref({
  totalLayers: 0,
  totalFeatures: 0,
  totalSize: 0
})

const layerStatistics = ref<any[]>([])

// 计算属性
const acceptedFormats = computed(() => {
  return DataImporter.getSupportedFormats()
    .map(format => format.accept)
    .join(',')
})

const needsCoordinateFields = computed(() => {
  return selectedFile.value && 
    (selectedFile.value.name.endsWith('.csv') || selectedFile.value.name.endsWith('.xlsx'))
})

const supportedExportFormats = computed(() => {
  return DataExporter.getSupportedFormats()
})

const availableLayers = computed(() => {
  // 这里应该从layerManager获取可用图层
  return layerStatistics.value
})

// 方法
const handleFileSelect = (file: any) => {
  selectedFile.value = file.raw
  importResult.value = null
}

const clearSelection = () => {
  selectedFile.value = null
  importResult.value = null
}

const executeImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  importing.value = true
  try {
    const result = await DataImporter.importFile(selectedFile.value, importOptions.value)
    importResult.value = result

    if (result.success) {
      ElMessage.success('数据导入成功')
      // 这里可以将数据添加到地图
      if (props.layerManager && result.data) {
        await addDataToMap(result.data, result.metadata)
      }
    } else {
      ElMessage.error(result.error || '导入失败')
    }
  } catch (error) {
    ElMessage.error('导入过程中发生错误')
    console.error('Import error:', error)
  } finally {
    importing.value = false
  }
}

const executeExport = async () => {
  exporting.value = true
  try {
    const data = await getExportData()
    if (!data) {
      ElMessage.warning('没有可导出的数据')
      return
    }

    await DataExporter.exportData(data, exportOptions.value)
    ElMessage.success('数据导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  } finally {
    exporting.value = false
  }
}

const getExportData = async (): Promise<ExportData | null> => {
  // 根据导出源获取数据
  switch (exportSource.value) {
    case 'current':
      return getCurrentLayerData()
    case 'selected':
      return getSelectedFeaturesData()
    case 'all':
      return getAllLayersData()
    default:
      return null
  }
}

const getCurrentLayerData = (): ExportData | null => {
  // 实现获取当前图层数据的逻辑
  return null
}

const getSelectedFeaturesData = (): ExportData | null => {
  // 实现获取选中要素数据的逻辑
  return null
}

const getAllLayersData = (): ExportData | null => {
  // 实现获取所有图层数据的逻辑
  return null
}

const addDataToMap = async (data: any, metadata: any) => {
  // 实现将导入的数据添加到地图的逻辑
  console.log('Adding data to map:', data, metadata)
}

const handleExportSourceChange = () => {
  selectedLayerId.value = ''
}

const refreshStatistics = () => {
  // 刷新统计数据
  updateStatistics()
}

const updateStatistics = () => {
  // 更新统计数据的逻辑
  if (props.layerManager) {
    // 从layerManager获取统计信息
  }
}

const exportLayer = (layer: any) => {
  selectedLayerId.value = layer.id
  exportSource.value = 'current'
  activeTab.value = 'export'
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  updateStatistics()
})
</script>

<style scoped>
.data-management-panel {
  height: 100%;
}

.upload-area {
  margin-bottom: 20px;
}

.import-options,
.export-options {
  margin-top: 20px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 4px;
}

.import-actions,
.export-actions {
  margin-top: 16px;
  text-align: right;
}

.import-result {
  margin-top: 20px;
}

.result-details {
  margin-top: 12px;
}

.layer-selection {
  margin: 16px 0;
}

.statistics-section {
  padding: 16px;
}
</style>
