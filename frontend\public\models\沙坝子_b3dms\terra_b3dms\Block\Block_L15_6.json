{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 1.5819042921066284, "root": {"boundingVolume": {"box": [-131.93309020996094, -4.564239501953125, -32.12226104736328, 133.59425354003906, 0.0, 0.0, 0.0, 97.82608795166016, 0.0, 0.0, 0.0, 44.96598815917969]}, "children": [{"boundingVolume": {"box": [-176.46450805664062, 16.435091018676758, -24.761064529418945, 89.06283569335938, 0.0, 0.0, 0.0, 76.8267593383789, 0.0, 0.0, 0.0, 34.67660903930664]}, "content": {"uri": "Block_L17_59.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-42.87025451660156, -68.10908508300781, -28.559162139892578, 44.53141784667969, 0.0, 0.0, 0.0, 33.4559211730957, 0.0, 0.0, 0.0, 41.402889251708984]}, "children": [{"boundingVolume": {"box": [-61.425010681152344, -63.660987854003906, -21.486474990844727, 25.976659774780273, 0.0, 0.0, 0.0, 29.007827758789062, 0.0, 0.0, 0.0, 26.87367057800293]}, "content": {"uri": "Block_L17_53.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.89359474182129, -53.179622650146484, -48.96734619140625, 18.554758071899414, 0.0, 0.0, 0.0, 18.52646255493164, 0.0, 0.0, 0.0, 20.99470329284668]}, "children": [{"boundingVolume": {"box": [-16.89359474182129, -45.66840362548828, -55.770076751708984, 18.554758071899414, 0.0, 0.0, 0.0, 11.015243530273438, 0.0, 0.0, 0.0, 14.191974639892578]}, "content": {"uri": "Block_L18_100.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.89359474182129, -53.179622650146484, -34.77537536621094, 18.554758071899414, 0.0, 0.0, 0.0, 18.52646255493164, 0.0, 0.0, 0.0, 6.802728652954102]}, "children": [{"boundingVolume": {"box": [-26.170974731445312, -46.37123107910156, -34.77537536621094, 9.277379035949707, 0.0, 0.0, 0.0, 11.718069076538086, 0.0, 0.0, 0.0, 6.802728652954102]}, "content": {"uri": "Block_L19_187.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-7.616215705871582, -53.16597366333008, -34.77537536621094, 9.277379035949707, 0.0, 0.0, 0.0, 18.512813568115234, 0.0, 0.0, 0.0, 6.802728652954102]}, "content": {"uri": "Block_L19_186.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_99.b3dm"}, "geometricError": 0.2160925418138504, "refine": "REPLACE"}], "content": {"uri": "Block_L17_52.b3dm"}, "geometricError": 0.4435698688030243, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.89359474182129, -70.36185455322266, -7.564459323883057, 18.554758071899414, 0.0, 0.0, 0.0, 30.268510818481445, 0.0, 0.0, 0.0, 20.408185958862305]}, "children": [{"boundingVolume": {"box": [-16.89359474182129, -82.97418212890625, -7.564459323883057, 18.554758071899414, 0.0, 0.0, 0.0, 17.656185150146484, 0.0, 0.0, 0.0, 20.408185958862305]}, "children": [{"boundingVolume": {"box": [-16.89359474182129, -82.97418212890625, -7.564459323883057, 18.554758071899414, 0.0, 0.0, 0.0, 17.656185150146484, 0.0, 0.0, 0.0, 20.408185958862305]}, "content": {"uri": "Block_L19_185.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_98.b3dm"}, "geometricError": 0.31650787591934204, "refine": "REPLACE"}, {"boundingVolume": {"box": [-16.89359474182129, -52.705665588378906, -20.644519805908203, 18.554758071899414, 0.0, 0.0, 0.0, 12.612325668334961, 0.0, 0.0, 0.0, 7.328125953674316]}, "content": {"uri": "Block_L18_97.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_51.b3dm"}, "geometricError": 0.5262637734413147, "refine": "REPLACE"}], "content": {"uri": "Block_L16_15.b3dm"}, "geometricError": 0.9442278146743774, "refine": "REPLACE"}, {"boundingVolume": {"box": [-42.87025451660156, -11.08987045288086, -35.414588928222656, 44.53141784667969, 0.0, 0.0, 0.0, 23.563289642333984, 0.0, 0.0, 0.0, 35.64833068847656]}, "content": {"uri": "Block_L16_14.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-65.1359634399414, 52.86763381958008, -45.88849639892578, 22.265708923339844, 0.0, 0.0, 0.0, 40.39421463012695, 0.0, 0.0, 0.0, 21.476150512695312]}, "children": [{"boundingVolume": {"box": [-75.29132843017578, 32.67052459716797, -53.27894592285156, 10.155364990234375, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 11.884294509887695]}, "children": [{"boundingVolume": {"box": [-75.25511169433594, 32.67052459716797, -53.27894592285156, 10.119148254394531, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 11.884294509887695]}, "children": [{"boundingVolume": {"box": [-73.1297607421875, 22.571971893310547, -52.50773620605469, 7.993793487548828, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 11.11308479309082]}, "content": {"uri": "Block_L19_157.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-75.25511169433594, 42.769081115722656, -53.27894592285156, 10.119148254394531, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 11.884294509887695]}, "content": {"uri": "Block_L19_156.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_85.b3dm"}, "geometricError": 0.22081902623176575, "refine": "REPLACE"}], "content": {"uri": "Block_L17_42.b3dm"}, "geometricError": 0.4433591365814209, "refine": "REPLACE"}, {"boundingVolume": {"box": [-76.26881408691406, 32.67052459716797, -32.90349578857422, 11.132854461669922, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 8.4911527633667]}, "content": {"uri": "Block_L17_41.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-54.003108978271484, 32.67052459716797, -52.691871643066406, 11.132854461669922, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 14.672775268554688]}, "content": {"uri": "Block_L17_40.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.12429809570312, 73.06474304199219, -46.165069580078125, 9.27737808227539, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 18.460725784301758]}, "children": [{"boundingVolume": {"box": [-78.12429809570312, 62.9661865234375, -46.15800476074219, 9.27737808227539, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 18.453664779663086]}, "content": {"uri": "Block_L18_80.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.12429809570312, 83.16329956054688, -53.99631118774414, 9.27737808227539, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 10.60196304321289]}, "content": {"uri": "Block_L18_79.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-78.75186157226562, 83.16329956054688, -35.81550598144531, 8.64980697631836, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 7.578843116760254]}, "content": {"uri": "Block_L19_242.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_39.b3dm"}, "geometricError": 0.3646107614040375, "refine": "REPLACE"}, {"boundingVolume": {"box": [-55.858585357666016, 73.06474304199219, -52.80848693847656, 12.988330841064453, 0.0, 0.0, 0.0, 20.197107315063477, 0.0, 0.0, 0.0, 13.52021598815918]}, "children": [{"boundingVolume": {"box": [-55.858585357666016, 62.9661865234375, -52.79597473144531, 12.988330841064453, 0.0, 0.0, 0.0, 10.098554611206055, 0.0, 0.0, 0.0, 13.507699966430664]}, "content": {"uri": "Block_L18_78.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-55.858585357666016, 83.16329956054688, -54.194026947021484, 12.988330841064453, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 11.622394561767578]}, "children": [{"boundingVolume": {"box": [-55.858585357666016, 83.16329956054688, -54.18171691894531, 12.988330841064453, 0.0, 0.0, 0.0, 10.098552703857422, 0.0, 0.0, 0.0, 11.610084533691406]}, "content": {"uri": "Block_L19_143.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_77.b3dm"}, "geometricError": 0.2213456779718399, "refine": "REPLACE"}], "content": {"uri": "Block_L17_38.b3dm"}, "geometricError": 0.40710848569869995, "refine": "REPLACE"}], "content": {"uri": "Block_L16_13.b3dm"}, "geometricError": 0.792702317237854, "refine": "REPLACE"}, {"boundingVolume": {"box": [-20.60454559326172, 52.86763381958008, -58.35759735107422, 22.265708923339844, 0.0, 0.0, 0.0, 40.39421463012695, 0.0, 0.0, 0.0, 18.623741149902344]}, "content": {"uri": "Block_L16_12.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L15_6.b3dm"}, "geometricError": 1.5819042921066284, "refine": "REPLACE"}}