{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.3655269145965576, "root": {"boundingVolume": {"box": [-104.89190673828125, 80.16983795166016, -42.40575408935547, 17.490238189697266, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 17.031919479370117]}, "children": [{"boundingVolume": {"box": [-95.44886779785156, 80.16983795166016, -47.78350067138672, 8.047191619873047, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 11.310060501098633]}, "children": [{"boundingVolume": {"box": [-95.44886779785156, 80.16983795166016, -47.727195739746094, 8.047191619873047, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 11.253753662109375]}, "children": [{"boundingVolume": {"box": [-95.44886779785156, 73.62383270263672, -46.133270263671875, 8.047191619873047, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 9.659830093383789]}, "children": [{"boundingVolume": {"box": [-90.91230010986328, 73.62383270263672, -49.35321807861328, 3.5106277465820312, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 6.439886093139648]}, "children": [{"boundingVolume": {"box": [-90.91021728515625, 73.62383270263672, -49.35321807861328, 3.5085487365722656, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 6.439886093139648]}, "content": {"uri": "Block_L22_1166.b3dm"}, "geometricError": 0.013759441673755646, "refine": "REPLACE"}], "content": {"uri": "Block_L21_689.b3dm"}, "geometricError": 0.02751055359840393, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.40959167480469, 73.62383270263672, -39.69338607788086, 8.007923126220703, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 3.2199440002441406]}, "children": [{"boundingVolume": {"box": [-98.64718627929688, 73.62383270263672, -39.69338607788086, 4.630504608154297, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 3.2199440002441406]}, "content": {"uri": "Block_L22_1165.b3dm"}, "geometricError": 0.013128828257322311, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.70917510986328, 73.62383270263672, -39.69338607788086, 3.3075027465820312, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 3.2199440002441406]}, "content": {"uri": "Block_L22_1164.b3dm"}, "geometricError": 0.013417134992778301, "refine": "REPLACE"}], "content": {"uri": "Block_L21_688.b3dm"}, "geometricError": 0.026607871055603027, "refine": "REPLACE"}], "content": {"uri": "Block_L20_373.b3dm"}, "geometricError": 0.05364619567990303, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.01201629638672, 86.7158432006836, -47.727195739746094, 7.610343933105469, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 11.253753662109375]}, "children": [{"boundingVolume": {"box": [-92.6290054321289, 86.7158432006836, -51.399356842041016, 5.227333068847656, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 7.526172637939453]}, "children": [{"boundingVolume": {"box": [-92.6290054321289, 86.7158432006836, -51.272926330566406, 5.227333068847656, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 7.399740219116211]}, "content": {"uri": "Block_L22_1163.b3dm"}, "geometricError": 0.012178040109574795, "refine": "REPLACE"}], "content": {"uri": "Block_L21_687.b3dm"}, "geometricError": 0.024427462369203568, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.00932312011719, 86.7158432006836, -40.17331314086914, 7.6076507568359375, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 3.699871063232422]}, "children": [{"boundingVolume": {"box": [-98.17640686035156, 86.7158432006836, -40.17331314086914, 4.436653137207031, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 3.699871063232422]}, "content": {"uri": "Block_L22_1162.b3dm"}, "geometricError": 0.012493109330534935, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.57070922851562, 83.44284057617188, -40.17331314086914, 3.1690406799316406, 0.0, 0.0, 0.0, 3.2730026245117188, 0.0, 0.0, 0.0, 3.699871063232422]}, "content": {"uri": "Block_L22_1161.b3dm"}, "geometricError": 0.012550318613648415, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.57070922851562, 89.98884582519531, -40.17331314086914, 3.1690406799316406, 0.0, 0.0, 0.0, 3.2730026245117188, 0.0, 0.0, 0.0, 3.699871063232422]}, "content": {"uri": "Block_L23_930.b3dm"}, "geometricError": 0.010924897156655788, "refine": "REPLACE"}], "content": {"uri": "Block_L21_686.b3dm"}, "geometricError": 0.023773301392793655, "refine": "REPLACE"}], "content": {"uri": "Block_L20_372.b3dm"}, "geometricError": 0.04802253469824791, "refine": "REPLACE"}], "content": {"uri": "Block_L19_195.b3dm"}, "geometricError": 0.10145677626132965, "refine": "REPLACE"}], "content": {"uri": "Block_L18_104.b3dm"}, "geometricError": 0.20318245887756348, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.95616149902344, 80.16983795166016, -30.92363739013672, 16.42599105834961, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 5.5498046875]}, "children": [{"boundingVolume": {"box": [-111.75983428955078, 80.16983795166016, -30.92363739013672, 9.386222839355469, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 5.5498046875]}, "children": [{"boundingVolume": {"box": [-108.59025573730469, 74.71482849121094, -30.92363739013672, 6.216640472412109, 0.0, 0.0, 0.0, 7.637004852294922, 0.0, 0.0, 0.0, 5.5498046875]}, "children": [{"boundingVolume": {"box": [-108.59025573730469, 74.71482849121094, -30.92363739013672, 6.216640472412109, 0.0, 0.0, 0.0, 7.637004852294922, 0.0, 0.0, 0.0, 5.5498046875]}, "children": [{"boundingVolume": {"box": [-107.54612731933594, 71.53274536132812, -30.92363739013672, 5.172515869140625, 0.0, 0.0, 0.0, 4.454917907714844, 0.0, 0.0, 0.0, 5.5498046875]}, "content": {"uri": "Block_L22_1160.b3dm"}, "geometricError": 0.011812213808298111, "refine": "REPLACE"}, {"boundingVolume": {"box": [-108.56118774414062, 79.16975402832031, -30.92363739013672, 6.187580108642578, 0.0, 0.0, 0.0, 3.182086944580078, 0.0, 0.0, 0.0, 5.5498046875]}, "content": {"uri": "Block_L23_929.b3dm"}, "geometricError": 0.010648622177541256, "refine": "REPLACE"}], "content": {"uri": "Block_L21_685.b3dm"}, "geometricError": 0.02228495664894581, "refine": "REPLACE"}], "content": {"uri": "Block_L20_371.b3dm"}, "geometricError": 0.04461172595620155, "refine": "REPLACE"}, {"boundingVolume": {"box": [-111.67890167236328, 87.80683898925781, -30.92363739013672, 9.305290222167969, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 5.5498046875]}, "children": [{"boundingVolume": {"box": [-114.35169982910156, 87.80683898925781, -29.092729568481445, 5.989048004150391, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 3.7188968658447266]}, "children": [{"boundingVolume": {"box": [-114.35169982910156, 87.80683898925781, -29.092729568481445, 5.989048004150391, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 3.7188968658447266]}, "content": {"uri": "Block_L23_928.b3dm"}, "geometricError": 0.010266870260238647, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1665.b3dm"}, "geometricError": 0.020555246621370316, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.36813354492188, 87.80683898925781, -32.7735710144043, 2.9945220947265625, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 3.6998701095581055]}, "children": [{"boundingVolume": {"box": [-105.36813354492188, 87.80683898925781, -32.7735710144043, 2.9945220947265625, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 3.6998701095581055]}, "content": {"uri": "Block_L23_927.b3dm"}, "geometricError": 0.010700206272304058, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1664.b3dm"}, "geometricError": 0.021361015737056732, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.36813354492188, 87.80683898925781, -27.223766326904297, 2.9945220947265625, 0.0, 0.0, 0.0, 5.455005645751953, 0.0, 0.0, 0.0, 1.8499345779418945]}, "children": [{"boundingVolume": {"box": [-105.36813354492188, 85.07933807373047, -27.223766326904297, 2.9945220947265625, 0.0, 0.0, 0.0, 2.7275009155273438, 0.0, 0.0, 0.0, 1.8499345779418945]}, "content": {"uri": "Block_L23_926.b3dm"}, "geometricError": 0.009512840770184994, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.36813354492188, 90.53434753417969, -27.223766326904297, 2.9945220947265625, 0.0, 0.0, 0.0, 2.7275047302246094, 0.0, 0.0, 0.0, 1.8499345779418945]}, "content": {"uri": "Block_L23_925.b3dm"}, "geometricError": 0.009395311586558819, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1663.b3dm"}, "geometricError": 0.018905997276306152, "refine": "REPLACE"}], "content": {"uri": "Block_L21_935.b3dm"}, "geometricError": 0.03977010399103165, "refine": "REPLACE"}], "content": {"uri": "Block_L20_491.b3dm"}, "geometricError": 0.08302894979715347, "refine": "REPLACE"}, {"boundingVolume": {"box": [-95.95188903808594, 80.16983795166016, -30.92363739013672, 6.421722412109375, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 5.5498046875]}, "content": {"uri": "Block_L19_194.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_249.b3dm"}, "geometricError": 0.17400765419006348, "refine": "REPLACE"}], "content": {"uri": "Block_L17_54.b3dm"}, "geometricError": 0.3655269145965576, "refine": "REPLACE"}}