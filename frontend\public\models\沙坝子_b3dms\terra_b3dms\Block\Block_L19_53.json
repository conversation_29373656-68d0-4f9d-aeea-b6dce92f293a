{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.0975114107131958, "root": {"boundingVolume": {"box": [10.30698013305664, 126.4166030883789, -66.11181640625, 8.645816802978516, 0.0, 0.0, 0.0, 9.245338439941406, 0.0, 0.0, 0.0, 10.124589920043945]}, "children": [{"boundingVolume": {"box": [10.30698013305664, 122.56437683105469, -72.85742950439453, 8.645816802978516, 0.0, 0.0, 0.0, 5.3931121826171875, 0.0, 0.0, 0.0, 3.3744659423828125]}, "children": [{"boundingVolume": {"box": [5.984071731567383, 122.56437683105469, -75.19451141357422, 4.322908401489258, 0.0, 0.0, 0.0, 5.3931121826171875, 0.0, 0.0, 0.0, 1.0343017578125]}, "children": [{"boundingVolume": {"box": [5.984071731567383, 119.8678207397461, -75.2990951538086, 4.322908401489258, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 0.929718017578125]}, "content": {"uri": "Block_L22_148.b3dm"}, "geometricError": 0.0140439597889781, "refine": "REPLACE"}, {"boundingVolume": {"box": [5.984071731567383, 125.26093292236328, -74.95394897460938, 4.322908401489258, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 0.7937431335449219]}, "content": {"uri": "Block_L22_147.b3dm"}, "geometricError": 0.013397205621004105, "refine": "REPLACE"}], "content": {"uri": "Block_L21_176.b3dm"}, "geometricError": 0.027395140379667282, "refine": "REPLACE"}, {"boundingVolume": {"box": [14.629888534545898, 122.56437683105469, -72.8342514038086, 4.322908401489258, 0.0, 0.0, 0.0, 5.3931121826171875, 0.0, 0.0, 0.0, 3.351287841796875]}, "children": [{"boundingVolume": {"box": [14.629888534545898, 119.8678207397461, -72.85269165039062, 4.322908401489258, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 3.3328475952148438]}, "content": {"uri": "Block_L22_146.b3dm"}, "geometricError": 0.013741645961999893, "refine": "REPLACE"}, {"boundingVolume": {"box": [14.629888534545898, 125.26093292236328, -72.56950378417969, 4.322908401489258, 0.0, 0.0, 0.0, 2.6965560913085938, 0.0, 0.0, 0.0, 3.086536407470703]}, "content": {"uri": "Block_L22_145.b3dm"}, "geometricError": 0.01359205599874258, "refine": "REPLACE"}], "content": {"uri": "Block_L21_175.b3dm"}, "geometricError": 0.027328260242938995, "refine": "REPLACE"}], "content": {"uri": "Block_L20_102.b3dm"}, "geometricError": 0.05470820516347885, "refine": "REPLACE"}, {"boundingVolume": {"box": [10.30698013305664, 131.80972290039062, -72.60975646972656, 8.645816802978516, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.1267967224121094]}, "children": [{"boundingVolume": {"box": [5.984071731567383, 131.80972290039062, -72.60975646972656, 4.322908401489258, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.1267967224121094]}, "children": [{"boundingVolume": {"box": [3.822617530822754, 131.80972290039062, -72.69453430175781, 2.161454200744629, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 3.0420265197753906]}, "content": {"uri": "Block_L22_144.b3dm"}, "geometricError": 0.012650612741708755, "refine": "REPLACE"}, {"boundingVolume": {"box": [8.145525932312012, 131.80972290039062, -72.46405029296875, 2.161454200744629, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 2.9810867309570312]}, "content": {"uri": "Block_L22_143.b3dm"}, "geometricError": 0.012508931569755077, "refine": "REPLACE"}], "content": {"uri": "Block_L21_174.b3dm"}, "geometricError": 0.02512594312429428, "refine": "REPLACE"}, {"boundingVolume": {"box": [14.629888534545898, 131.80972290039062, -72.03390502929688, 4.322908401489258, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 2.550945281982422]}, "children": [{"boundingVolume": {"box": [12.46843433380127, 131.80972290039062, -72.03256225585938, 2.161454200744629, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 2.5495948791503906]}, "content": {"uri": "Block_L22_142.b3dm"}, "geometricError": 0.012470433488488197, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.791343688964844, 131.80972290039062, -71.88655090332031, 2.161454200744629, 0.0, 0.0, 0.0, 3.8522262573242188, 0.0, 0.0, 0.0, 2.4035911560058594]}, "content": {"uri": "Block_L22_141.b3dm"}, "geometricError": 0.012345991097390652, "refine": "REPLACE"}], "content": {"uri": "Block_L21_173.b3dm"}, "geometricError": 0.02482621744275093, "refine": "REPLACE"}], "content": {"uri": "Block_L20_101.b3dm"}, "geometricError": 0.04996132105588913, "refine": "REPLACE"}, {"boundingVolume": {"box": [12.00316047668457, 128.35635375976562, -62.73509216308594, 6.949636459350586, 0.0, 0.0, 0.0, 7.305583953857422, 0.0, 0.0, 0.0, 6.747869491577148]}, "children": [{"boundingVolume": {"box": [16.688339233398438, 125.92115783691406, -65.4041519165039, 2.2644577026367188, 0.0, 0.0, 0.0, 4.870388031005859, 0.0, 0.0, 0.0, 4.078813552856445]}, "children": [{"boundingVolume": {"box": [16.711833953857422, 123.4859619140625, -65.40476989746094, 2.2409634590148926, 0.0, 0.0, 0.0, 2.435192108154297, 0.0, 0.0, 0.0, 4.078195571899414]}, "content": {"uri": "Block_L22_140.b3dm"}, "geometricError": 0.011048851534724236, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.68982696533203, 128.35635375976562, -65.40476989746094, 2.2629709243774414, 0.0, 0.0, 0.0, 2.4351959228515625, 0.0, 0.0, 0.0, 4.078195571899414]}, "content": {"uri": "Block_L23_201.b3dm"}, "geometricError": 0.01066262274980545, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1300.b3dm"}, "geometricError": 0.02180110663175583, "refine": "REPLACE"}, {"boundingVolume": {"box": [12.009352684020996, 133.22674560546875, -62.73509216308594, 6.94344425201416, 0.0, 0.0, 0.0, 2.4351959228515625, 0.0, 0.0, 0.0, 6.747869491577148]}, "children": [{"boundingVolume": {"box": [9.121352195739746, 134.31475830078125, -63.577720642089844, 4.04824161529541, 0.0, 0.0, 0.0, 1.3471908569335938, 0.0, 0.0, 0.0, 5.905242919921875]}, "content": {"uri": "Block_L23_200.b3dm"}, "geometricError": 0.010119779966771603, "refine": "REPLACE"}, {"boundingVolume": {"box": [16.061195373535156, 133.22674560546875, -62.73711395263672, 2.8916015625, 0.0, 0.0, 0.0, 2.4351959228515625, 0.0, 0.0, 0.0, 6.745849609375]}, "content": {"uri": "Block_L23_199.b3dm"}, "geometricError": 0.010601803660392761, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1299.b3dm"}, "geometricError": 0.020875874906778336, "refine": "REPLACE"}], "content": {"uri": "Block_L21_759.b3dm"}, "geometricError": 0.042646169662475586, "refine": "REPLACE"}], "content": {"uri": "Block_L19_53.b3dm"}, "geometricError": 0.0975114107131958, "refine": "REPLACE"}}