import { FeatureConverter } from './featureConverter'
import type { GeoFeature } from './types'

/**
 * FeatureConverter 测试用例
 */

// 创建测试用的GeoFeature
const createTestPointFeature = (): GeoFeature => ({
  id: 'test-point-1',
  type: 'point',
  points: [{ longitude: 116.404, latitude: 39.915, height: 0 }],
  properties: { name: '测试点', description: '这是一个测试点' },
  timestamp: new Date()
})

const createTestLineFeature = (): GeoFeature => ({
  id: 'test-line-1',
  type: 'line',
  points: [
    { longitude: 116.404, latitude: 39.915, height: 0 },
    { longitude: 116.414, latitude: 39.925, height: 0 },
    { longitude: 116.424, latitude: 39.935, height: 0 }
  ],
  properties: { name: '测试线', description: '这是一个测试线' },
  timestamp: new Date()
})

const createTestPolygonFeature = (): GeoFeature => ({
  id: 'test-polygon-1',
  type: 'polygon',
  points: [
    { longitude: 116.404, latitude: 39.915, height: 0 },
    { longitude: 116.414, latitude: 39.915, height: 0 },
    { longitude: 116.414, latitude: 39.925, height: 0 },
    { longitude: 116.404, latitude: 39.925, height: 0 }
  ],
  properties: { name: '测试面', description: '这是一个测试面' },
  timestamp: new Date()
})

// 测试函数
export function testFeatureConverter() {
  console.log('🧪 开始测试 FeatureConverter...')

  // 测试1: 转换点要素
  console.log('📍 测试点要素转换...')
  const pointFeature = createTestPointFeature()
  const pointGeoJSON = FeatureConverter.convertSingleFeatureToGeoJSON(pointFeature)
  
  if (pointGeoJSON && pointGeoJSON.geometry.type === 'Point') {
    console.log('✅ 点要素转换成功')
  } else {
    console.error('❌ 点要素转换失败')
  }

  // 测试2: 转换线要素
  console.log('📏 测试线要素转换...')
  const lineFeature = createTestLineFeature()
  const lineGeoJSON = FeatureConverter.convertSingleFeatureToGeoJSON(lineFeature)
  
  if (lineGeoJSON && lineGeoJSON.geometry.type === 'LineString') {
    console.log('✅ 线要素转换成功')
  } else {
    console.error('❌ 线要素转换失败')
  }

  // 测试3: 转换面要素
  console.log('🔲 测试面要素转换...')
  const polygonFeature = createTestPolygonFeature()
  const polygonGeoJSON = FeatureConverter.convertSingleFeatureToGeoJSON(polygonFeature)
  
  if (polygonGeoJSON && polygonGeoJSON.geometry.type === 'Polygon') {
    console.log('✅ 面要素转换成功')
  } else {
    console.error('❌ 面要素转换失败')
  }

  // 测试4: 转换要素集合
  console.log('📦 测试要素集合转换...')
  const features = [pointFeature, lineFeature, polygonFeature]
  const featureCollection = FeatureConverter.convertToGeoJSON(features)
  
  if (featureCollection.type === 'FeatureCollection' && featureCollection.features.length === 3) {
    console.log('✅ 要素集合转换成功')
  } else {
    console.error('❌ 要素集合转换失败')
  }

  // 测试5: 验证GeoJSON
  console.log('🔍 测试GeoJSON验证...')
  const isValid = FeatureConverter.validateGeoJSON(featureCollection)
  
  if (isValid) {
    console.log('✅ GeoJSON验证通过')
  } else {
    console.error('❌ GeoJSON验证失败')
  }

  // 测试6: 获取要素统计
  console.log('📊 测试要素统计...')
  const stats = FeatureConverter.getFeatureStatistics(features)
  
  if (stats.total === 3 && stats.types.length === 3) {
    console.log('✅ 要素统计正确')
    console.log('📈 统计结果:', stats)
  } else {
    console.error('❌ 要素统计错误')
  }

  // 测试7: 创建要素摘要
  console.log('📝 测试要素摘要...')
  const summary = FeatureConverter.createFeatureSummary(features)
  
  if (summary.includes('3个要素')) {
    console.log('✅ 要素摘要正确:', summary)
  } else {
    console.error('❌ 要素摘要错误:', summary)
  }

  console.log('🎉 FeatureConverter 测试完成!')
  return {
    pointGeoJSON,
    lineGeoJSON,
    polygonGeoJSON,
    featureCollection,
    stats,
    summary
  }
}

// 如果在浏览器环境中，可以在控制台中调用测试
if (typeof window !== 'undefined') {
  (window as any).testFeatureConverter = testFeatureConverter
}
