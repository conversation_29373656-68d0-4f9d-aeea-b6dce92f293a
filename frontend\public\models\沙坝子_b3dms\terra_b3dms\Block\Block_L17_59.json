{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.6839703321456909, "root": {"boundingVolume": {"box": [-176.46450805664062, 16.435091018676758, -24.761064529418945, 89.06283569335938, 0.0, 0.0, 0.0, 76.8267593383789, 0.0, 0.0, 0.0, 34.67660903930664]}, "children": [{"boundingVolume": {"box": [-198.730224609375, 69.3275146484375, -11.931145668029785, 66.79712677001953, 0.0, 0.0, 0.0, 23.934335708618164, 0.0, 0.0, 0.0, 14.992585182189941]}, "children": [{"boundingVolume": {"box": [-198.730224609375, 69.3275146484375, -11.931145668029785, 66.79712677001953, 0.0, 0.0, 0.0, 23.934335708618164, 0.0, 0.0, 0.0, 14.992585182189941]}, "children": [{"boundingVolume": {"box": [-198.730224609375, 69.3319091796875, -11.931145668029785, 66.79712677001953, 0.0, 0.0, 0.0, 23.929935455322266, 0.0, 0.0, 0.0, 14.992585182189941]}, "children": [{"boundingVolume": {"box": [-198.730224609375, 69.33745574951172, -11.931145668029785, 66.79712677001953, 0.0, 0.0, 0.0, 23.924394607543945, 0.0, 0.0, 0.0, 14.992585182189941]}, "children": [{"boundingVolume": {"box": [-204.29664611816406, 89.00787353515625, -15.644976615905762, 61.23069763183594, 0.0, 0.0, 0.0, 4.253971099853516, 0.0, 0.0, 0.0, 11.278754234313965]}, "children": [{"boundingVolume": {"box": [-204.29664611816406, 89.037353515625, -15.644976615905762, 61.23069763183594, 0.0, 0.0, 0.0, 4.224498748779297, 0.0, 0.0, 0.0, 11.278754234313965]}, "content": {"uri": "Block_L23_958.b3dm"}, "geometricError": 0.0069818682968616486, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1680.b3dm"}, "geometricError": 0.014011646620929241, "refine": "REPLACE"}, {"boundingVolume": {"box": [-137.15713500976562, 61.36265563964844, -3.9271788597106934, 5.2240447998046875, 0.0, 0.0, 0.0, 15.94959831237793, 0.0, 0.0, 0.0, 6.98861837387085]}, "children": [{"boundingVolume": {"box": [-137.15713500976562, 53.387855529785156, -4.488781452178955, 5.2240447998046875, 0.0, 0.0, 0.0, 7.974798202514648, 0.0, 0.0, 0.0, 6.375482082366943]}, "content": {"uri": "Block_L23_957.b3dm"}, "geometricError": 0.007558196317404509, "refine": "REPLACE"}, {"boundingVolume": {"box": [-136.30984497070312, 69.33745574951172, -2.4408841133117676, 4.376762390136719, 0.0, 0.0, 0.0, 7.974800109863281, 0.0, 0.0, 0.0, 5.502323627471924]}, "content": {"uri": "Block_L23_956.b3dm"}, "geometricError": 0.005756908096373081, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1679.b3dm"}, "geometricError": 0.013223129324615002, "refine": "REPLACE"}, {"boundingVolume": {"box": [-137.49951171875, 85.28704833984375, -10.599844932556152, 5.566429138183594, 0.0, 0.0, 0.0, 7.974796295166016, 0.0, 0.0, 0.0, 13.020596504211426]}, "children": [{"boundingVolume": {"box": [-135.85520935058594, 88.55479431152344, -20.423734664916992, 3.922119140625, 0.0, 0.0, 0.0, 4.707054138183594, 0.0, 0.0, 0.0, 3.160236358642578]}, "content": {"uri": "Block_L23_955.b3dm"}, "geometricError": 0.008916147984564304, "refine": "REPLACE"}, {"boundingVolume": {"box": [-137.32859802246094, 88.0672836303711, -15.006186485290527, 5.3955078125, 0.0, 0.0, 0.0, 5.1945648193359375, 0.0, 0.0, 0.0, 2.2573118209838867]}, "content": {"uri": "Block_L23_954.b3dm"}, "geometricError": 0.007881173864006996, "refine": "REPLACE"}, {"boundingVolume": {"box": [-137.49951171875, 85.28704833984375, -5.164306163787842, 5.566429138183594, 0.0, 0.0, 0.0, 7.974796295166016, 0.0, 0.0, 0.0, 7.584568500518799]}, "content": {"uri": "Block_L23_953.b3dm"}, "geometricError": 0.0059974039904773235, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1678.b3dm"}, "geometricError": 0.014695022255182266, "refine": "REPLACE"}], "content": {"uri": "Block_L21_942.b3dm"}, "geometricError": 0.028279073536396027, "refine": "REPLACE"}], "content": {"uri": "Block_L20_495.b3dm"}, "geometricError": 0.05718535929918289, "refine": "REPLACE"}], "content": {"uri": "Block_L19_251.b3dm"}, "geometricError": 0.11466143280267715, "refine": "REPLACE"}], "content": {"uri": "Block_L18_128.b3dm"}, "geometricError": 0.2314390391111374, "refine": "REPLACE"}, {"boundingVolume": {"box": [-105.88282012939453, -29.56264877319336, -5.314880847930908, 18.48114776611328, 0.0, 0.0, 0.0, 30.656761169433594, 0.0, 0.0, 0.0, 15.230424880981445]}, "children": [{"boundingVolume": {"box": [-105.88282012939453, -29.500831604003906, -5.358509540557861, 18.48114776611328, 0.0, 0.0, 0.0, 30.59494400024414, 0.0, 0.0, 0.0, 15.185813903808594]}, "content": {"uri": "Block_L18_107.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L17_56.b3dm"}, "geometricError": 0.4910772442817688, "refine": "REPLACE"}, {"boundingVolume": {"box": [-107.40612030029297, 15.757158279418945, -14.349686622619629, 20.00444793701172, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 23.07742691040039]}, "children": [{"boundingVolume": {"box": [-104.68896484375, 15.757158279418945, -25.90442657470703, 17.28729248046875, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 11.522685050964355]}, "content": {"uri": "Block_L18_106.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-109.2472915649414, 15.757158279418945, -2.8270015716552734, 18.16327667236328, 0.0, 0.0, 0.0, 14.663045883178711, 0.0, 0.0, 0.0, 11.554740905761719]}, "content": {"uri": "Block_L19_250.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_127.b3dm"}, "geometricError": 0.33776378631591797, "refine": "REPLACE"}, {"boundingVolume": {"box": [-100.8868408203125, 48.74901580810547, -36.34993362426758, 13.485172271728516, 0.0, 0.0, 0.0, 18.328811645507812, 0.0, 0.0, 0.0, 10.97610092163086]}, "children": [{"boundingVolume": {"box": [-100.81414794921875, 48.74901580810547, -36.340721130371094, 13.412471771240234, 0.0, 0.0, 0.0, 18.328811645507812, 0.0, 0.0, 0.0, 10.966886520385742]}, "children": [{"boundingVolume": {"box": [-100.81414794921875, 42.63941192626953, -32.10771179199219, 13.412471771240234, 0.0, 0.0, 0.0, 12.219207763671875, 0.0, 0.0, 0.0, 6.733880996704102]}, "children": [{"boundingVolume": {"box": [-104.15141296386719, 42.63941192626953, -32.032005310058594, 10.075206756591797, 0.0, 0.0, 0.0, 12.219207763671875, 0.0, 0.0, 0.0, 6.658170700073242]}, "children": [{"boundingVolume": {"box": [-104.09342193603516, 42.63941192626953, -32.00333023071289, 10.017219543457031, 0.0, 0.0, 0.0, 12.219207763671875, 0.0, 0.0, 0.0, 6.629497528076172]}, "children": [{"boundingVolume": {"box": [-100.19437408447266, 36.529808044433594, -29.41558265686035, 6.118171691894531, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 4.041749954223633]}, "content": {"uri": "Block_L22_1177.b3dm"}, "geometricError": 0.013280900195240974, "refine": "REPLACE"}, {"boundingVolume": {"box": [-104.08799743652344, 48.74901580810547, -31.976377487182617, 10.011795043945312, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 6.602544784545898]}, "content": {"uri": "Block_L22_1176.b3dm"}, "geometricError": 0.012825093232095242, "refine": "REPLACE"}], "content": {"uri": "Block_L21_694.b3dm"}, "geometricError": 0.02599383518099785, "refine": "REPLACE"}], "content": {"uri": "Block_L20_377.b3dm"}, "geometricError": 0.05195947736501694, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.73893737792969, 42.63941192626953, -32.032005310058594, 3.3372650146484375, 0.0, 0.0, 0.0, 12.219207763671875, 0.0, 0.0, 0.0, 6.658170700073242]}, "children": [{"boundingVolume": {"box": [-90.73893737792969, 36.529808044433594, -31.2672061920166, 3.3372650146484375, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 5.893373489379883]}, "children": [{"boundingVolume": {"box": [-90.73893737792969, 33.475006103515625, -31.2672061920166, 3.3372650146484375, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 5.893373489379883]}, "content": {"uri": "Block_L22_1175.b3dm"}, "geometricError": 0.012226520106196404, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.73893737792969, 39.58460998535156, -30.708484649658203, 3.3372650146484375, 0.0, 0.0, 0.0, 3.0548019409179688, 0.0, 0.0, 0.0, 5.334651947021484]}, "content": {"uri": "Block_L22_1174.b3dm"}, "geometricError": 0.012166186235845089, "refine": "REPLACE"}], "content": {"uri": "Block_L21_693.b3dm"}, "geometricError": 0.024406595155596733, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.73893737792969, 48.74901580810547, -32.00333023071289, 3.3372650146484375, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 6.629497528076172]}, "children": [{"boundingVolume": {"box": [-90.73893737792969, 48.74901580810547, -34.18644332885742, 3.3372650146484375, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 4.406306266784668]}, "content": {"uri": "Block_L22_1173.b3dm"}, "geometricError": 0.011479450389742851, "refine": "REPLACE"}, {"boundingVolume": {"box": [-90.73893737792969, 48.74901580810547, -27.576984405517578, 3.3372650146484375, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 2.203152656555176]}, "content": {"uri": "Block_L23_935.b3dm"}, "geometricError": 0.010624836198985577, "refine": "REPLACE"}], "content": {"uri": "Block_L22_1668.b3dm"}, "geometricError": 0.021788032725453377, "refine": "REPLACE"}], "content": {"uri": "Block_L20_376.b3dm"}, "geometricError": 0.046094778925180435, "refine": "REPLACE"}], "content": {"uri": "Block_L19_197.b3dm"}, "geometricError": 0.09666498750448227, "refine": "REPLACE"}, {"boundingVolume": {"box": [-100.75962829589844, 60.968223571777344, -36.271324157714844, 13.357955932617188, 0.0, 0.0, 0.0, 6.1096038818359375, 0.0, 0.0, 0.0, 10.897489547729492]}, "content": {"uri": "Block_L19_196.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_105.b3dm"}, "geometricError": 0.19127359986305237, "refine": "REPLACE"}], "content": {"uri": "Block_L17_55.b3dm"}, "geometricError": 0.3843250572681427, "refine": "REPLACE"}, {"boundingVolume": {"box": [-104.89190673828125, 80.16983795166016, -42.40575408935547, 17.490238189697266, 0.0, 0.0, 0.0, 13.092010498046875, 0.0, 0.0, 0.0, 17.031919479370117]}, "content": {"uri": "Block_L17_54.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-109.6673812866211, 46.130615234375, -8.710418701171875, 22.265708923339844, 0.0, 0.0, 0.0, 15.710411071777344, 0.0, 0.0, 0.0, 16.663414001464844]}, "children": [{"boundingVolume": {"box": [-120.80023193359375, 46.130615234375, -8.712194442749023, 11.132854461669922, 0.0, 0.0, 0.0, 15.710411071777344, 0.0, 0.0, 0.0, 16.661638259887695]}, "content": {"uri": "Block_L19_248.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.53453063964844, 36.966209411621094, -13.569663047790527, 11.132854461669922, 0.0, 0.0, 0.0, 6.5460052490234375, 0.0, 0.0, 0.0, 11.804169654846191]}, "content": {"uri": "Block_L19_247.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-98.56327819824219, 52.67662048339844, -14.58230209350586, 11.104106903076172, 0.0, 0.0, 0.0, 9.164405822753906, 0.0, 0.0, 0.0, 10.79153060913086]}, "children": [{"boundingVolume": {"box": [-98.56613159179688, 52.67662048339844, -14.603621482849121, 11.101245880126953, 0.0, 0.0, 0.0, 9.164405822753906, 0.0, 0.0, 0.0, 10.770211219787598]}, "content": {"uri": "Block_L20_486.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L19_246.b3dm"}, "geometricError": 0.17350339889526367, "refine": "REPLACE"}], "content": {"uri": "Block_L18_126.b3dm"}, "geometricError": 0.3169492185115814, "refine": "REPLACE"}, {"boundingVolume": {"box": [-111.71873474121094, 77.55143737792969, -11.208927154541016, 20.214359283447266, 0.0, 0.0, 0.0, 15.710411071777344, 0.0, 0.0, 0.0, 14.164905548095703]}, "children": [{"boundingVolume": {"box": [-123.51043701171875, 77.55143737792969, -11.208927154541016, 8.422649383544922, 0.0, 0.0, 0.0, 15.710411071777344, 0.0, 0.0, 0.0, 14.164905548095703]}, "content": {"uri": "Block_L19_245.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}, {"boundingVolume": {"box": [-103.29608154296875, 77.55143737792969, -16.933460235595703, 11.791709899902344, 0.0, 0.0, 0.0, 15.710411071777344, 0.0, 0.0, 0.0, 8.440372467041016]}, "content": {"uri": "Block_L19_244.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_125.b3dm"}, "geometricError": 0.2877724766731262, "refine": "REPLACE"}], "content": {"uri": "Block_L17_59.b3dm"}, "geometricError": 0.6839703321456909, "refine": "REPLACE"}}