@echo off
echo ========================================
echo MTDT System Quick Test
echo ========================================
echo.

echo Testing Docker Services...
docker-compose -f docker-compose.internal.yml ps
echo.

echo Testing Frontend Development Server...
curl -s -o nul -w "%%{http_code}" http://localhost:5174 > temp_status.txt
set /p DEV_STATUS=<temp_status.txt
if "%DEV_STATUS%"=="200" (
    echo [OK] Frontend Dev Server: http://localhost:5174
) else (
    echo [FAIL] Frontend Dev Server not responding
)
del temp_status.txt
echo.

echo Testing Production Frontend...
curl -s -o nul -w "%%{http_code}" http://localhost:8080 > temp_status.txt
set /p PROD_STATUS=<temp_status.txt
if "%PROD_STATUS%"=="200" (
    echo [OK] Production Frontend: http://localhost:8080
) else (
    echo [FAIL] Production Frontend not responding
)
del temp_status.txt
echo.

echo Testing Backend API...
curl -s -o nul -w "%%{http_code}" http://localhost:8000/api/health > temp_status.txt
set /p API_STATUS=<temp_status.txt
if "%API_STATUS%"=="200" (
    echo [OK] Backend API: http://localhost:8000/api
) else (
    echo [FAIL] Backend API not responding
)
del temp_status.txt
echo.

echo Checking Critical Files...
if exist "docker-compose.internal.yml" (
    echo [OK] Docker compose file exists
) else (
    echo [FAIL] Docker compose file missing
)

if exist ".env" (
    echo [OK] Environment file exists
) else (
    echo [FAIL] Environment file missing
)

if exist "frontend\dist" (
    echo [OK] Frontend build directory exists
) else (
    echo [WARN] Frontend build directory missing
)

if exist "backend\uploads" (
    echo [OK] Backend uploads directory exists
) else (
    echo [WARN] Backend uploads directory missing
)

echo.
echo ========================================
echo Test Summary
echo ========================================
echo.
echo System Access URLs:
echo - Development: http://localhost:5174
echo - Production:  http://localhost:8080
echo - Backend API: http://localhost:8000/api
echo.
echo For detailed logs, run:
echo   docker-compose -f docker-compose.internal.yml logs
echo.
echo ========================================
echo Test Complete
echo ========================================

pause
