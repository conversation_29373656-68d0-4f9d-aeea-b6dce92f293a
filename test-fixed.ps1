# Fixed database connection test
Write-Host "MTDT Fixed Database Connection Test" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.accessToken  # Fixed: use correct property path
    Write-Host "Login successful" -ForegroundColor Green
    Write-Host "Token (first 50 chars): $($token.Substring(0,50))..." -ForegroundColor Gray
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test database connection
Write-Host ""
Write-Host "Step 2: Test database connection..." -ForegroundColor Yellow

$connectionData = @{
    host = "postgres"
    port = 5432
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $connectionData
    Write-Host "Database connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
} catch {
    Write-Host "Database connection failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Get tables
Write-Host ""
Write-Host "Step 3: Get database tables..." -ForegroundColor Yellow

try {
    $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $connectionData
    Write-Host "Successfully retrieved $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    
    if ($tablesResponse.Count -gt 0) {
        Write-Host ""
        Write-Host "Tables found:" -ForegroundColor Cyan
        $tablesResponse | ForEach-Object {
            Write-Host "- $($_.schema).$($_.name) ($($_.type))" -ForegroundColor White
            if ($_.geometryColumns -and $_.geometryColumns.Count -gt 0) {
                $geomCols = $_.geometryColumns | ForEach-Object { $_.columnName }
                Write-Host "  Geometry columns: $($geomCols -join ', ')" -ForegroundColor Gray
            }
            if ($_.totalRows -ne $null) {
                Write-Host "  Total rows: $($_.totalRows)" -ForegroundColor Gray
            }
        }
    }
} catch {
    Write-Host "Get tables failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Test completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "You can now use the database layer functionality in MTDT:" -ForegroundColor Cyan
Write-Host "1. Open the MTDT system: http://localhost:5174" -ForegroundColor White
Write-Host "2. Login with username: lhgadmin, password: 11111111" -ForegroundColor White
Write-Host "3. Open Layer Management panel" -ForegroundColor White
Write-Host "4. Click 'Add Layer' -> 'Database Layer'" -ForegroundColor White
Write-Host "5. Select 'WebGIS Main Database (Container)' connection" -ForegroundColor White
Write-Host "6. Test connection and load table list" -ForegroundColor White
Write-Host ""
Write-Host "Or press Ctrl+Shift+D to open the database test panel" -ForegroundColor White
