import { Controller, Post, Get, Body, Query, UseGuards, Logger } from '@nestjs/common';
import { DatabaseService } from './database.service';
import {
  TestConnectionDto,
  QueryLayerDto,
  ConnectionTestResult,
  LayerQueryResult,
  DatabaseTableInfo,
} from './dto/database-connection.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('database')
@UseGuards(JwtAuthGuard)
export class DatabaseController {
  private readonly logger = new Logger(DatabaseController.name);

  constructor(private readonly databaseService: DatabaseService) {}

  /**
   * 测试数据库连接
   */
  @Post('test-connection')
  async testConnection(@Body() testDto: TestConnectionDto): Promise<ConnectionTestResult> {
    this.logger.log(`Testing database connection to ${testDto.host}:${testDto.port}/${testDto.database}`);
    return this.databaseService.testConnection(testDto);
  }

  /**
   * 获取数据库表列表
   */
  @Post('tables')
  async getTables(@Body() testDto: TestConnectionDto): Promise<DatabaseTableInfo[]> {
    this.logger.log(`Getting tables from ${testDto.host}:${testDto.port}/${testDto.database}`);
    return this.databaseService.getTables(testDto);
  }

  /**
   * 查询空间图层数据
   */
  @Post('query-layer')
  async queryLayer(
    @Body() body: { queryDto: QueryLayerDto; connectionConfig: TestConnectionDto }
  ): Promise<LayerQueryResult> {
    this.logger.log(`Querying layer ${body.queryDto.tableName} from database`);
    return this.databaseService.queryLayer(body.queryDto, body.connectionConfig);
  }

  /**
   * 获取表的详细信息包括几何列
   */
  @Post('table-info')
  async getTableInfo(
    @Body() body: { connectionConfig: TestConnectionDto; tableName: string }
  ): Promise<any> {
    this.logger.log(`Getting table info for ${body.tableName}`);
    return this.databaseService.getTableInfo(body.connectionConfig, body.tableName);
  }

  /**
   * 预览图层数据（限制返回条数）
   */
  @Post('preview-layer')
  async previewLayer(
    @Body() body: { queryDto: QueryLayerDto; connectionConfig: TestConnectionDto }
  ): Promise<LayerQueryResult> {
    // 限制预览数据为最多100条
    const previewQuery = { ...body.queryDto, limit: Math.min(body.queryDto.limit || 100, 100) };
    this.logger.log(`Previewing layer ${previewQuery.tableName} (max 100 features)`);
    return this.databaseService.queryLayer(previewQuery, body.connectionConfig);
  }
}
