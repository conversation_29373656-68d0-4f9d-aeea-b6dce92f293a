{"asset": {"gltfUpAxis": "Z", "version": "0.0"}, "geometricError": 0.3765641450881958, "root": {"boundingVolume": {"box": [21.143657684326172, -3.629993438720703, -64.91272735595703, 19.482494354248047, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 14.722204208374023]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -3.629993438720703, -61.046630859375, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 10.856107711791992]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -3.629993438720703, -64.66608428955078, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.236656188964844]}, "children": [{"boundingVolume": {"box": [11.402410507202148, -3.629993438720703, -64.66608428955078, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.236656188964844]}, "children": [{"boundingVolume": {"box": [5.720016002655029, -3.629993438720703, -64.66608428955078, 4.058852672576904, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.236656188964844]}, "children": [{"boundingVolume": {"box": [5.720016002655029, -7.137482166290283, -64.65974426269531, 4.058852672576904, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 7.230312347412109]}, "content": {"uri": "Block_L22_382.b3dm"}, "geometricError": 0.013328174129128456, "refine": "REPLACE"}, {"boundingVolume": {"box": [5.720016002655029, 1.2804903984069824, -66.24291229248047, 4.058852672576904, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 4.406744003295898]}, "content": {"uri": "Block_L22_381.b3dm"}, "geometricError": 0.012729261070489883, "refine": "REPLACE"}, {"boundingVolume": {"box": [5.720016002655029, 1.2804903984069824, -59.63279724121094, 4.058852672576904, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 2.203371047973633]}, "content": {"uri": "Block_L22_380.b3dm"}, "geometricError": 0.011822205036878586, "refine": "REPLACE"}], "content": {"uri": "Block_L21_292.b3dm"}, "geometricError": 0.025357065722346306, "refine": "REPLACE"}, {"boundingVolume": {"box": [15.461263656616211, -3.629993438720703, -64.66608428955078, 5.682394504547119, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.236656188964844]}, "children": [{"boundingVolume": {"box": [13.496973037719727, -7.137482166290283, -64.65974426269531, 3.718104839324951, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 7.230312347412109]}, "content": {"uri": "Block_L22_379.b3dm"}, "geometricError": 0.012334264814853668, "refine": "REPLACE"}, {"boundingVolume": {"box": [15.461263656616211, 1.2804903984069824, -64.49940490722656, 5.682394504547119, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 7.069980621337891]}, "content": {"uri": "Block_L22_378.b3dm"}, "geometricError": 0.011904431506991386, "refine": "REPLACE"}], "content": {"uri": "Block_L21_291.b3dm"}, "geometricError": 0.024223526939749718, "refine": "REPLACE"}], "content": {"uri": "Block_L20_160.b3dm"}, "geometricError": 0.049852415919303894, "refine": "REPLACE"}], "content": {"uri": "Block_L19_86.b3dm"}, "geometricError": 0.099789559841156, "refine": "REPLACE"}, {"boundingVolume": {"box": [11.402410507202148, -3.629993438720703, -53.81427001953125, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 3.6151561737060547]}, "content": {"uri": "Block_L20_430.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_44.b3dm"}, "geometricError": 0.18143518269062042, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.884904861450195, -3.629993438720703, -65.16581726074219, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 14.43305778503418]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -3.629993438720703, -72.38594818115234, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.212928771972656]}, "children": [{"boundingVolume": {"box": [30.884904861450195, -3.629993438720703, -72.38594818115234, 9.741247177124023, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.212928771972656]}, "children": [{"boundingVolume": {"box": [27.6378231048584, -2.1899771690368652, -67.3210220336914, 6.494165420532227, 0.0, 0.0, 0.0, 6.977956295013428, 0.0, 0.0, 0.0, 2.1480026245117188]}, "children": [{"boundingVolume": {"box": [27.6378231048584, -2.1899771690368652, -67.3210220336914, 6.494165420532227, 0.0, 0.0, 0.0, 6.977956295013428, 0.0, 0.0, 0.0, 2.1480026245117188]}, "content": {"uri": "Block_L22_374.b3dm"}, "geometricError": 0.01215666625648737, "refine": "REPLACE"}], "content": {"uri": "Block_L21_289.b3dm"}, "geometricError": 0.024330182000994682, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, -7.137482166290283, -72.23351287841797, 3.247081756591797, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 7.060493469238281]}, "children": [{"boundingVolume": {"box": [39.17351531982422, -7.137482166290283, -74.57463073730469, 1.4526348114013672, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 4.700809478759766]}, "content": {"uri": "Block_L22_373.b3dm"}, "geometricError": 0.015055017545819283, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, -7.137482166290283, -67.52342224121094, 3.247081756591797, 0.0, 0.0, 0.0, 4.9104838371276855, 0.0, 0.0, 0.0, 2.35040283203125]}, "content": {"uri": "Block_L22_372.b3dm"}, "geometricError": 0.013840689323842525, "refine": "REPLACE"}], "content": {"uri": "Block_L21_288.b3dm"}, "geometricError": 0.02795799821615219, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, 1.2804903984069824, -72.38594818115234, 3.247081756591797, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 7.212928771972656]}, "children": [{"boundingVolume": {"box": [37.4337043762207, 1.2804903984069824, -75.3913345336914, 3.1924476623535156, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 4.207542419433594]}, "content": {"uri": "Block_L22_371.b3dm"}, "geometricError": 0.014516822062432766, "refine": "REPLACE"}, {"boundingVolume": {"box": [37.37907028198242, 1.2804903984069824, -68.17840576171875, 3.247081756591797, 0.0, 0.0, 0.0, 3.50748872756958, 0.0, 0.0, 0.0, 3.0053863525390625]}, "content": {"uri": "Block_L22_370.b3dm"}, "geometricError": 0.012332390993833542, "refine": "REPLACE"}], "content": {"uri": "Block_L21_287.b3dm"}, "geometricError": 0.026551956310868263, "refine": "REPLACE"}], "content": {"uri": "Block_L20_158.b3dm"}, "geometricError": 0.05318823084235191, "refine": "REPLACE"}], "content": {"uri": "Block_L19_85.b3dm"}, "geometricError": 0.10634064674377441, "refine": "REPLACE"}, {"boundingVolume": {"box": [30.636341094970703, -3.629993438720703, -57.95288848876953, 9.492683410644531, 0.0, 0.0, 0.0, 8.417972564697266, 0.0, 0.0, 0.0, 7.220129013061523]}, "content": {"uri": "Block_L19_84.json"}, "geometricError": 10000000000.0, "refine": "REPLACE"}], "content": {"uri": "Block_L18_43.b3dm"}, "geometricError": 0.19418783485889435, "refine": "REPLACE"}], "content": {"uri": "Block_L17_19.b3dm"}, "geometricError": 0.3765641450881958, "refine": "REPLACE"}}