<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header" @click="toggleExpanded">
      <el-icon :class="{ 'is-expanded': expanded }">
        <ArrowDown />
      </el-icon>
      <span class="monitor-title">性能监控</span>
      <div class="monitor-status" :class="getStatusClass()">
        {{ getStatusText() }}
      </div>
    </div>
    
    <transition name="monitor-expand">
      <div v-show="expanded" class="monitor-content">
        <!-- 基本性能指标 -->
        <div class="metrics-section">
          <h4>渲染性能</h4>
          <div class="metric-item">
            <span class="metric-label">最近渲染时间:</span>
            <span class="metric-value">{{ formatTime(metrics.lastRenderTime) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">平均渲染时间:</span>
            <span class="metric-value">{{ formatTime(metrics.averageRenderTime) }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">内存使用:</span>
            <span class="metric-value">{{ formatMemory(metrics.memoryUsage) }}</span>
          </div>
        </div>
        
        <!-- 实时FPS -->
        <div class="metrics-section">
          <h4>实时性能</h4>
          <div class="metric-item">
            <span class="metric-label">FPS:</span>
            <span class="metric-value" :class="getFpsClass()">{{ currentFps }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">活动面板:</span>
            <span class="metric-value">{{ activePanelsCount }}</span>
          </div>
        </div>
        
        <!-- 性能建议 -->
        <div v-if="suggestions.length > 0" class="suggestions-section">
          <h4>性能建议</h4>
          <div 
            v-for="suggestion in suggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            :class="suggestion.type"
          >
            <el-icon>
              <Warning v-if="suggestion.type === 'warning'" />
              <InfoFilled v-else />
            </el-icon>
            <span>{{ suggestion.message }}</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="monitor-actions">
          <el-button size="small" @click="clearMetrics">
            <el-icon><Delete /></el-icon>
            清除数据
          </el-button>
          <el-button size="small" @click="exportMetrics">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ArrowDown, Warning, InfoFilled, Delete, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUIStore } from '../../stores/ui'

interface PerformanceSuggestion {
  id: string
  type: 'info' | 'warning'
  message: string
}

const uiStore = useUIStore()

// 组件状态
const showMonitor = ref(import.meta.env.DEV) // 只在开发模式显示
const expanded = ref(false)
const currentFps = ref(0)
const frameCount = ref(0)
const lastTime = ref(performance.now())

// 性能建议
const suggestions = ref<PerformanceSuggestion[]>([])

// 计算属性
const metrics = computed(() => uiStore.performanceMetrics)

const activePanelsCount = computed(() => uiStore.activePanels.length)

// FPS监控
let animationId: number

const updateFps = () => {
  frameCount.value++
  const currentTime = performance.now()
  
  if (currentTime - lastTime.value >= 1000) {
    currentFps.value = Math.round((frameCount.value * 1000) / (currentTime - lastTime.value))
    frameCount.value = 0
    lastTime.value = currentTime
    
    // 更新性能建议
    updateSuggestions()
  }
  
  animationId = requestAnimationFrame(updateFps)
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

// 格式化时间
const formatTime = (time: number): string => {
  if (time < 1) return `${(time * 1000).toFixed(1)}μs`
  if (time < 1000) return `${time.toFixed(1)}ms`
  return `${(time / 1000).toFixed(2)}s`
}

// 格式化内存
const formatMemory = (bytes: number): string => {
  if (bytes === 0) return 'N/A'
  const units = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${units[i]}`
}

// 获取状态类
const getStatusClass = (): string => {
  if (currentFps.value >= 50) return 'status-good'
  if (currentFps.value >= 30) return 'status-warning'
  return 'status-poor'
}

// 获取状态文本
const getStatusText = (): string => {
  if (currentFps.value >= 50) return '良好'
  if (currentFps.value >= 30) return '一般'
  return '较差'
}

// 获取FPS类
const getFpsClass = (): string => {
  if (currentFps.value >= 50) return 'fps-good'
  if (currentFps.value >= 30) return 'fps-warning'
  return 'fps-poor'
}

// 更新性能建议
const updateSuggestions = () => {
  const newSuggestions: PerformanceSuggestion[] = []
  
  if (currentFps.value < 30) {
    newSuggestions.push({
      id: 'low-fps',
      type: 'warning',
      message: 'FPS较低，建议关闭一些面板或减少地图要素'
    })
  }
  
  if (activePanelsCount.value > 3) {
    newSuggestions.push({
      id: 'too-many-panels',
      type: 'info',
      message: '打开的面板较多，可能影响性能'
    })
  }
  
  if (metrics.value.memoryUsage > 100 * 1024 * 1024) { // 100MB
    newSuggestions.push({
      id: 'high-memory',
      type: 'warning',
      message: '内存使用较高，建议刷新页面'
    })
  }
  
  suggestions.value = newSuggestions
}

// 清除指标
const clearMetrics = () => {
  uiStore.performanceMetrics.lastRenderTime = 0
  uiStore.performanceMetrics.averageRenderTime = 0
  uiStore.performanceMetrics.memoryUsage = 0
  suggestions.value = []
  ElMessage.success('性能数据已清除')
}

// 导出指标
const exportMetrics = () => {
  const data = {
    timestamp: new Date().toISOString(),
    metrics: metrics.value,
    fps: currentFps.value,
    activePanels: activePanelsCount.value,
    suggestions: suggestions.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-metrics-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('性能数据已导出')
}

// 生命周期
onMounted(() => {
  if (showMonitor.value) {
    updateFps()
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 250px;
  backdrop-filter: blur(5px);
}

.monitor-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.monitor-header:hover {
  background: #f5f5f5;
}

.monitor-header .el-icon {
  margin-right: 8px;
  transition: transform 0.3s;
}

.monitor-header .el-icon.is-expanded {
  transform: rotate(180deg);
}

.monitor-title {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
}

.monitor-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.status-good {
  background: #f0f9ff;
  color: #67c23a;
}

.status-warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.status-poor {
  background: #fef0f0;
  color: #f56c6c;
}

.monitor-content {
  padding: 12px;
}

.metrics-section {
  margin-bottom: 16px;
}

.metrics-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 500;
}

.fps-good { color: #67c23a; }
.fps-warning { color: #e6a23c; }
.fps-poor { color: #f56c6c; }

.suggestions-section {
  margin-bottom: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
}

.suggestion-item.warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.suggestion-item.info {
  background: #f0f9ff;
  color: #409eff;
}

.suggestion-item .el-icon {
  margin-right: 6px;
}

.monitor-actions {
  display: flex;
  gap: 8px;
}

.monitor-expand-enter-active,
.monitor-expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.monitor-expand-enter-from,
.monitor-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

.monitor-expand-enter-to,
.monitor-expand-leave-from {
  max-height: 500px;
  opacity: 1;
}
</style>
