const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// 检查特定用户
async function checkSpecificUser(username, testPassword) {
  console.log(`🔍 检查用户: ${username}`);
  
  const config = {
    host: 'localhost',
    port: 5432,
    database: 'webgis_db',
    user: 'testUser',
    password: '1234',
    max: 1,
    connectionTimeoutMillis: 5000,
  };

  let pool;
  let client;

  try {
    console.log(`📡 连接到: ${config.host}:${config.port}/${config.database}`);
    
    pool = new Pool(config);
    client = await pool.connect();
    
    console.log('✅ 数据库连接成功');
    
    // 查询特定用户
    const userQuery = `
      SELECT 
        user_id,
        username, 
        email, 
        full_name,
        password_hash,
        is_active,
        login_attempts,
        locked_until,
        last_login,
        created_at
      FROM system.users
      WHERE username = $1
    `;
    
    const userResult = await client.query(userQuery, [username]);
    
    if (userResult.rows.length === 0) {
      console.log(`❌ 用户 "${username}" 不存在`);
      return;
    }
    
    const user = userResult.rows[0];
    console.log(`\n✅ 找到用户 ${username}:`);
    console.log(`用户ID: ${user.user_id}`);
    console.log(`用户名: ${user.username}`);
    console.log(`邮箱: ${user.email}`);
    console.log(`全名: ${user.full_name || '未设置'}`);
    console.log(`是否激活: ${user.is_active}`);
    console.log(`登录尝试次数: ${user.login_attempts}`);
    console.log(`锁定到: ${user.locked_until || 'null'}`);
    console.log(`最后登录: ${user.last_login ? new Date(user.last_login).toLocaleString('zh-CN') : '从未登录'}`);
    console.log(`创建时间: ${new Date(user.created_at).toLocaleString('zh-CN')}`);
    console.log(`密码哈希: ${user.password_hash.substring(0, 20)}...`);
    
    // 验证密码
    if (testPassword) {
      console.log(`\n🔐 验证密码 "${testPassword}":`);
      const isPasswordValid = await bcrypt.compare(testPassword, user.password_hash);
      console.log(`密码验证结果: ${isPasswordValid ? '✅ 正确' : '❌ 错误'}`);
    }
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    if (client) {
      client.release();
    }
    if (pool) {
      await pool.end();
    }
  }
}

// 检查 lhgadmin 用户
checkSpecificUser('lhgadmin', '11111111').catch(console.error);
