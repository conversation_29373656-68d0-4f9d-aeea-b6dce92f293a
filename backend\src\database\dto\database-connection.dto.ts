import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsEnum, IsBoolean } from 'class-validator';

export enum DatabaseType {
  POSTGRESQL = 'postgresql',
  MYSQL = 'mysql',
  SQLITE = 'sqlite',
}

export class CreateDatabaseConnectionDto {
  @IsString()
  name: string;

  @IsEnum(DatabaseType)
  type: DatabaseType;

  @IsString()
  host: string;

  @IsNumber()
  port: number;

  @IsString()
  database: string;

  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsOptional()
  @IsString()
  schema?: string;
}

export class TestConnectionDto {
  @IsString()
  host: string;

  @IsNumber()
  port: number;

  @IsString()
  database: string;

  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsOptional()
  @IsString()
  schema?: string;
}

export class QueryLayerDto {
  @IsString()
  connectionId: string;

  @IsString()
  tableName: string;

  @IsOptional()
  @IsString()
  geometryColumn?: string;

  @IsOptional()
  @IsString()
  whereClause?: string;

  @IsOptional()
  @IsNumber()
  limit?: number;
}

export class DatabaseTableInfo {
  name: string;
  schema: string;
  type: 'table' | 'view';
  geometryColumns: GeometryColumnInfo[];
  totalRows?: number;
}

export class GeometryColumnInfo {
  columnName: string;
  geometryType: string;
  srid: number;
  dimension: number;
}

export class ConnectionTestResult {
  success: boolean;
  message: string;
  version?: string;
  postgisVersion?: string;
}

export class LayerQueryResult {
  success: boolean;
  data?: any;
  totalFeatures?: number;
  bounds?: [number, number, number, number];
  error?: string;
}
