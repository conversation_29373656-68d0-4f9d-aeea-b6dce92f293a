# Test network access database connections
Write-Host "Testing Network Access Database Connections" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Step 1: Login
Write-Host "Step 1: Login..." -ForegroundColor Yellow
$loginData = @{
    username = "lhgadmin"
    password = "11111111"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.accessToken
    Write-Host "Login successful" -ForegroundColor Green
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Step 2: Test host database (port 5432)
Write-Host ""
Write-Host "Step 2: Test host database (*************:5432)..." -ForegroundColor Yellow

$hostConnectionData = @{
    host = "*************"
    port = 5432
    database = "webgis_db"
    username = "testUser"
    password = "1234"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $hostConnectionData
    Write-Host "✅ Host database connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
    
    # Get tables from host database
    try {
        $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $hostConnectionData
        Write-Host "✅ Host database has $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Failed to get tables from host database: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Host database connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Test container database (port 5433)
Write-Host ""
Write-Host "Step 3: Test container database (*************:5433)..." -ForegroundColor Yellow

$containerConnectionData = @{
    host = "*************"
    port = 5433
    database = "webgis_db"
    username = "mtdt_user"
    password = "mtdt_secure_password_2024"
} | ConvertTo-Json

try {
    $testResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/test-connection" -Method POST -Headers $headers -Body $containerConnectionData
    Write-Host "✅ Container database connection successful!" -ForegroundColor Green
    Write-Host "Message: $($testResponse.message)" -ForegroundColor Gray
    if ($testResponse.postgisVersion) {
        Write-Host "PostGIS Version: $($testResponse.postgisVersion)" -ForegroundColor Gray
    }
    
    # Get tables from container database
    try {
        $tablesResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/database/tables" -Method POST -Headers $headers -Body $containerConnectionData
        Write-Host "✅ Container database has $($tablesResponse.Count) tables with geometry columns" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Failed to get tables from container database: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Container database connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Network Access Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Database Connections for Network Access:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Host Database (Your existing data):" -ForegroundColor White
Write-Host "   - Host: *************" -ForegroundColor Gray
Write-Host "   - Port: 5432" -ForegroundColor Gray
Write-Host "   - Username: testUser" -ForegroundColor Gray
Write-Host "   - Password: 1234" -ForegroundColor Gray
Write-Host "   - Contains: 9 tables with geographic data" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Container Database (New/empty):" -ForegroundColor White
Write-Host "   - Host: *************" -ForegroundColor Gray
Write-Host "   - Port: 5433" -ForegroundColor Gray
Write-Host "   - Username: mtdt_user" -ForegroundColor Gray
Write-Host "   - Password: mtdt_secure_password_2024" -ForegroundColor Gray
Write-Host "   - Contains: Empty database for new data" -ForegroundColor Gray
Write-Host ""
Write-Host "Access the system via:" -ForegroundColor Cyan
Write-Host "- Network: http://*************:8080" -ForegroundColor White
Write-Host "- Local: http://localhost:8080" -ForegroundColor White
Write-Host ""
Write-Host "In the MTDT system, you can now choose:" -ForegroundColor Cyan
Write-Host "- 'WebGIS主数据库(宿主机)' for your existing data" -ForegroundColor White
Write-Host "- 'WebGIS主数据库(容器)' for new containerized data" -ForegroundColor White
